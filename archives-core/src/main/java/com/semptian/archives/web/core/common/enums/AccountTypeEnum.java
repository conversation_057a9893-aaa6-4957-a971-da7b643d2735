package com.semptian.archives.web.core.common.enums;

/**
 * @author: <PERSON>Q<PERSON>
 * @create: 2021/03/15
 * desc: 账号类型枚举类
 **/
public enum AccountTypeEnum {

    RADIUS(1, "radius"),

    EMAIL(2, "email"),

    FTP(3, "ftp"),

    IM(4, "im"),

    TELNET(5, "telnet"),

    SNS(6, "sns"),

    WEBCHAT(7, "webchat");

    private int key;

    private String value;

    AccountTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(int key) {
        AccountTypeEnum[] values = AccountTypeEnum.values();
        for (AccountTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getValue();
            }
        }
        return "";
    }
}
