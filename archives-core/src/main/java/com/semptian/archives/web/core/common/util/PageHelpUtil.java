package com.semptian.archives.web.core.common.util;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/6 17:51
 **/
public class PageHelpUtil {

    public static String getSortSql(String originalSql, String sortCondition) {
        return originalSql + " " + sortCondition + " ";
    }

    public static String getPageSql(String originalSql, Integer onPage, Integer size) {
        Integer startOffset = (onPage - 1) * size;
        return originalSql + " limit " + size + " offset " + startOffset;
    }

    /**
     * 获取初始偏移量
     * @param onPage
     * @param size
     * @return
     */
    public static Integer getStartOffset(Integer onPage, Integer size) {
        return (onPage - 1) * size;
    }

    public static String getCountSql(String originalSql) {
        return " select count(1) cnt from (" + originalSql + ") as num";
    }


    /**
     * @param size  当前页面大小
     * @param onPage 当前页码
     * @param list      需要分页的集合
     * @return
     */
    public static <T> List<T> listPage(List<T> list, int onPage, int size) {
        if (onPage < 1) {
            onPage = 1;
        }

        //当前第几页数据
        int currentPage;

        // 一共多少条记录
        int totalRecord = list.size();

        // 一共多少页
        int totalPage = totalRecord % size;

        if (totalPage > 0) {
            totalPage = totalRecord / size + 1;
        } else {
            totalPage = totalRecord / size;
        }

        // 当前第几页数据
        currentPage = Math.min(totalPage, onPage);

        // 起始索引
        int fromIndex = size * (currentPage - 1);

        // 结束索引
        int toIndex = Math.min(size * currentPage, totalRecord);
        return list.subList(fromIndex, toIndex);
    }
}
