package com.semptian.archives.web.core.common.enums;


/**
 * @author: LMZ
 * @create: 2022/03/09
 * desc: 常量类枚举类
 **/
public enum ConstantEnum {

    ALL_ARCHIVES(1, "allArchives","全部档案"),

    CONTAIN_TODAY(2, "containToday","查询时是否包含当天，与配置archive.contain-today:false有关，默认为false");


    private Integer key;
    private String value;
    private String description;

    public Integer getKey() {
        return key;
    }



    public String getValue() {
        return value;
    }



    public String getDescription() {
        return description;
    }



    ConstantEnum(Integer key, String value, String description) {
        this.key = key;
        this.value = value;
        this.description = description;
    }

    public static String getByKey(int key) {
        AccountTypeEnum[] values = AccountTypeEnum.values();
        for (AccountTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getValue();
            }
        }
        return "";
    }
}
