package com.semptian.archives.web.core.common.enums;

/**
 * 协议类型枚举
 */
public enum DataTypeEnum {

    EMAIL("101", "EMAIL"),

    IM("103", "IM"),

    VOIP("109", "VOIP"),

    CALL("200", "CALL");

    private String key;

    private String value;

    DataTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        DataTypeEnum[] values = DataTypeEnum.values();
        for (DataTypeEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }
}
