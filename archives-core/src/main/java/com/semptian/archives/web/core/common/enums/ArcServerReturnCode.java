package com.semptian.archives.web.core.common.enums;

import com.semptian.base.service.ICode;

/**
 * <AUTHOR>
 */
public enum ArcServerReturnCode implements ICode {
    TAG_IS_BLANK(800300,"tagIsBlank"),
    SUCCESS(800302,"success"),
    PARAM_IS_BLANK(10302,"paramIsBlank"),
    NO_INITIAL_DATA(30301,"noInitialData"),
    INTERFACE_INNER_INVOKE_ERROR(60301, "interfaceInnerInvokeError"),
    UPDATE_TAG_ERROR(30302,"updateTagError"),
    PARAM_IS_INVALID(10301, "paramIsInvalid"),
    MERGE_FILE_IS_EMPTY(30303,"mergeFileIsEmpty"),
    REMARK_SAVE_ERROR(800301,"remarkSaveError"),
    DATA_TYPE_IS_BLANK(800333,"data_type_list is blank"),
    /**
     * 用户ID为空
     */
    CURRENT_USERID_BLANK(801006, "当前登录用户id为空!");

    private Integer code;
    private String msg;

    private ArcServerReturnCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Object getData() {
        return null;
    }

}
