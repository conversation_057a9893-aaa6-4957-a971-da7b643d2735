package com.semptian.archives.web.core.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2021/7/10 15:20
 * @description mysql的模糊查询时特殊字符转义
 */
public class EscapeUtil {
    public static String escapeChar(String before){
        if(StringUtils.isNotBlank(before)){
            before = before.replaceAll("\\\\", "\\\\\\\\");
            before = before.replaceAll("_", "\\\\_");
            before = before.replaceAll("%", "\\\\%");
        }
        return before ;
    }

    /**
     * clickHouse 模糊查询时对通配符转义
     * @param before
     * @return
     */
    public static String escapeCKMatch(String before){
        if(StringUtils.isNotBlank(before)){
            before = before.replaceAll("\\\\", "\\\\\\\\\\\\\\\\");
            before = before.replaceAll("_", "\\\\\\\\_");
            before = before.replaceAll("%", "\\\\\\\\%");
        }
        return before ;
    }

    /**
     * 对sql中单引号转义
     * @param str 待处理字符串
     * @return 转义后字符串
     */
    public static String escapeSingleQuote(String str){
        if(StringUtils.isNotBlank(str)){
            str = str.replaceAll("'", "''''");
        }
        return str ;
    }


    /**
     * 将 es 查询调整成短语匹配
     * @param condition
     * @return
     */
    public static String escapeEsCondition(String condition){
        if(StringUtils.isNotBlank(condition)){
            condition = "\"" + condition + "\"";
        }
        return condition ;
    }


    /**
     * 将 es 查询调整成短语匹配,多个以OR分隔
     * @param condition
     * @return
     */
    public static String escapeEsConditionWithOr(String condition, String prefixTag){
        if(StringUtils.isNotBlank(condition)){
            if(condition.contains(",")){
                return buildORConditionPrefix(condition, prefixTag);
            }
            else if (StringUtils.isNotEmpty(prefixTag) && condition.contains(prefixTag)) {
                String prefix = condition.substring(0, condition.indexOf(prefixTag));
                return "\"" + prefix + "\"";
            }
            else {
                return "\"" + condition + "\"";
            }
        }
        return condition ;
    }


    private static String buildORConditionPrefix(String accounts, String prefixTag) {
        String[] splitAuthAccount = accounts.split(",");

        HashMap<String, String> reduceMap = new HashMap<>();
        for (String account : splitAuthAccount) {
            if (StringUtils.isNotEmpty(prefixTag)) {
                if (StringUtils.isNotEmpty(account) && account.contains(prefixTag)) {
                    String prefix = account.substring(0, account.indexOf(prefixTag));
                    reduceMap.put(escapeEsCondition(prefix), prefix);
                }
            } else {
                reduceMap.put(escapeEsCondition(account), account);
            }
        }

        return String.join(" OR ", reduceMap.keySet());
    }


    /**
     * 特殊字符转义处理
     *
     * @param s
     * @return + - && || ! ( ) { } [ ] ^ " ~ * : \ / #
     */
    public static String escapeAll(String s) {
        if(StringUtils.isEmpty(s)){
            return s;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            // These characters are part of the query syntax and must be escaped
            if (c == '\\' || c == '+' || c == '-' || c == '!' || c == '(' || c == ')' || c == ':'
                        || c == '^' || c == '[' || c == ']'  || c == '{' || c == '}' || c == '~'
                        || c == '|' || c == '&' || c == '/' || c == '?' || c == '#') {
                    sb.append("\\\\");
            }
            else if (c == '\"') {
                sb.append("\\");
            }
            sb.append(c);
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(escapeEsCondition("radius2"));
    }
}
