package com.semptian.archives.web.core.common.constent;

import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.enums.ArcTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * date  2020/11/30 10:36
 * 通用常量类
 * description
 */
public class CommonConstent {



    //","分隔符
    public static final String SPLIT = ",";

    //"or" 分隔符
    public static final String OR_SPLIT = " or ";

    //"*"通配符
    public static final String ALL_WILDCARD_CHARACTER = "*";

    //选中标识
    public static final String SELECTED = "1";

    public static final List<Integer> VIRTUAL_LIST = Lists.newArrayList(Integer.parseInt(ArcTypeEnum.EMAIL.getKey()),Integer.parseInt(ArcTypeEnum.IM.getKey()));
    public static final List<Integer> DOMAIN_APP_LIST = Lists.newArrayList(Integer.parseInt(ArcTypeEnum.APP.getKey()),Integer.parseInt(ArcTypeEnum.WEB_SITE.getKey()),Integer.parseInt(ArcTypeEnum.NET_PROTOCOL.getKey()));
    public static final List<Integer> RADIUS_PHONE_LIST = Lists.newArrayList(Integer.parseInt(ArcTypeEnum.RADIUS.getKey()),Integer.parseInt(ArcTypeEnum.PHONE.getKey()),Integer.parseInt(ArcTypeEnum.FIXED_IP.getKey()));

    //ES索引档案id字段
    public static final String ES_ARC_ID_FIELD = "id";

    //排序类型
    public static final String ORDER_TYPE = "orderType";

    //排序字段
    public static final String ORDER_FIELD = "orderField";

    //档案类型全部
    public static final Integer ARC_TYPE_ALL = 0;

    //协议类型
    public static final String DATA_TYPE = "data_type";

    public static final String NORM_DATA_TYPE = "norm_data_type";

    public static final String CHILD_TYPE = "child_type";




    /**
     * 通联归属区域国家码翻译字段
     */
    public static final String COUNTRY_CODE_FIELD = "country_code";

    /**
     * 全部协议ID:-1表示全部协议通用
     */
    public static final Integer COMMON_ALL_PROTOCOL_ID = -1;
}
