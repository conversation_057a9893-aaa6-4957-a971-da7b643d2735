package com.semptian.archives.web.core.common.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 排序字段枚举
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
public enum SortFieldEnum {


    /**
     * 活跃次数
     */
    ACTIVE_NUM("activeRate", "behavior_num"),

    /**
     * 采集时间=最后关联时间
     */
    LAST_RELATION_TIME("latestRelationTime", "latest_relation_time"),

    /**
     * 创建时间
     */
    CREATE_TIME("createTime", "create_time");

    private final String value;

    private final String field;

    public String getField() {
        return field;
    }

    public String getValue() {
        return value;
    }

    SortFieldEnum(String value, String field) {
        this.value = value;
        this.field = field;
    }

    public static Map<String, String> getSortFieldMapByValueAndSortType(String value, Integer sortType) {
        LinkedHashMap<String, String> sortFieldMap = new LinkedHashMap<>(8);

        for (SortFieldEnum sortFieldEnum : SortFieldEnum.values()) {
            if (sortFieldEnum.getValue().equals(value)) {
                String[] fields = sortFieldEnum.getField().split(",");
                for (String field : fields) {
                    String sortTypeName = (sortType == 0) ? "asc" : "desc";
                    sortFieldMap.put(field, sortTypeName);
                }
            }
        }
        return sortFieldMap;
    }
}
