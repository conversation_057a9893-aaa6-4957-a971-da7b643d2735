package com.semptian.archives.web.core.common.enums;

/**
 * @author: SunQi
 **/
public enum ArcTableEnum {


    WEB_SITE("deye_v64_quanxidangan_website", "website"),

    APP("deye_v64_quanxidangan_app", "app"),

    FIXED_IP("deye_v64_quanxidangan_fix_ip","fixed ip"),

    PHONE("deye_v64_quanxidangan_phone","phone"),

    radius("deye_v64_quanxidangan_radius","radius"),

    EMAIL("deye_v64_quanxidangan_email","email"),

    IM("deye_v64_quanxidangan_im","im"),

    PROTOCOL("deye_v64_quanxidangan_app","protocol");

    private final String key;

    private final String value;

    ArcTableEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        ArcTableEnum[] values = ArcTableEnum.values();
        for (ArcTableEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }
}
