package com.semptian.archives.web.core.common.util;

import cn.hutool.crypto.SecureUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/5/18 17:14
 **/
public class ArcIdUtil {

    /**
     * 获取应用 id
     * @param appName
     * @param appType
     * @return
     */
    public static String getAppId(String appName, String appType) {
        return SecureUtil.md5(appName + "_" + appType) ;
    }

    /**
     * 获取网站 id
     * @param domain
     * @return
     */
    public static String getDomainId(String domain) {
        return SecureUtil.md5(domain);
    }

    /**
     * 获取 email 档案id
     * @param virtualAccount
     * @return
     */
    public static String getEmailAccountId(String virtualAccount) {
        return SecureUtil.md5("101" + "_" + virtualAccount);
    }

    /**
     * 生成虚拟账户的IM账号ID。
     * 该方法通过将固定字符串"103"、虚拟账户和虚拟应用类型连接起来，并对连接后的字符串进行MD5加密，来生成一个IM账号ID。
     *
     * @param virtualAccount 虚拟账户，参与IM账号ID的生成。
     * @param virtualAppType 虚拟应用类型，参与IM账号ID的生成。
     * @return 返回通过MD5加密生成的IM账号ID字符串。
     */
    public static String getImAccountId(String virtualAccount, String virtualAppType) {
        // 对固定字符串和参数进行拼接，然后计算MD5值
        return SecureUtil.md5("103" + "_" + virtualAccount + virtualAppType) ;
    }

    /**
     * 获取号码档案id
     * @param phoneNumber
     * @return
     */
    public static String getPhoneId(String phoneNumber) {
        return SecureUtil.md5(phoneNumber);
    }

    /**
     * 获取认证账户id
     *
     * @param authType
     * @param authAccount
     * @return
     */
    public static String getAuthAccountId(String authType, String authAccount) {
        if (("1020001".equals(authType) || "1029997".equals(authType)) && StringUtils.isNotBlank(authAccount)) {
            return SecureUtil.md5(authType + "_" + authAccount) ;
        } else if ("1020004".equals(authType) && StringUtils.isNotBlank(authAccount)) {
            return getPhoneId(authAccount);
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
//        String virtualAccountId = getVirtualAccountId("103",  "**********");
//        System.out.println(virtualAccountId);
        String authAccountId = getAuthAccountId( "1029997", "192.168.6.140");
        String authAccountId1 = getAuthAccountId("1020001", "Gljhomeradius");
        String phoneId = getPhoneId("682");
        System.out.println(authAccountId);
        System.out.println(authAccountId1);
        System.out.println(phoneId);
    }
}
