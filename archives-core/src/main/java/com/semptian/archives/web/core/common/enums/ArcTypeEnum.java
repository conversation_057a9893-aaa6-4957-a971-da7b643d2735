package com.semptian.archives.web.core.common.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 档案类型枚举
 */
public enum ArcTypeEnum {

    RADIUS("1", "radius"),

    EMAIL("2", "email"),

    WEB_SITE("3", "website"),

    APP("4", "app"),

    PHONE("5", "phone"),

    IM("6", "im"),

    NET_PROTOCOL("7", "protocol"),

    FIXED_IP("8","fixed_ip");

    private String key;

    private String value;

    ArcTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        ArcTypeEnum[] values = ArcTypeEnum.values();
        for (ArcTypeEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }

    public static List<Integer> keys(){
        List<Integer> keyList = new ArrayList<>();
        ArcTypeEnum[] values  = ArcTypeEnum.values();
        for (ArcTypeEnum value : values) {
            String key = ArcTypeEnum.getByValue(value.value);
            keyList.add(Integer.parseInt(key));
            }
        return keyList;
    }

    public static String getByValue(String value){
        ArcTypeEnum[] values = ArcTypeEnum.values();
        for (ArcTypeEnum arcTypeEnum : values) {
            if (arcTypeEnum.getValue().equals(value)){
                return arcTypeEnum.getKey();
            }
        }
        return "";
    }

    public static void main(String[] args) {
        ArcTypeEnum app = ArcTypeEnum.valueOf("app");
        System.out.println(app.key);
    }
}
