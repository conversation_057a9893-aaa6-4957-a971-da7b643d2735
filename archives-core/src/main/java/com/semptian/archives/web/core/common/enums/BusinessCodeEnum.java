package com.semptian.archives.web.core.common.enums;

/**
 * 业务sql 编码
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/06/3
 * desc: 账号类型枚举类
 **/
public enum BusinessCodeEnum {

    /**
     * 业务码
     * */

    PHONE_TOTAL_COMMUNICATION_AREA_NUMBER(2, "PHONE_TOTAL_COMMUNICATION_AREA_NUMBER", "号码档案-通联区域个数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    PHONE_ONE_WEEK_COMMUNICATION_AREA_NUMBER(2, "PHONE_ONE_WEEK_COMMUNICATION_AREA_NUMBER", "号码档案-近7天通联区域个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    PHONE_TOTAL_FAX_NUMBER(2, "PHONE_TOTAL_FAX_NUMBER", "号码档案-传真总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 9 == dimension;
        }
    },

    PHONE_ONE_WEEK_FAX_NUMBER(2, "PHONE_ONE_WEEK_FAX_NUMBER", "号码档案-近7天传真次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 9 == dimension;
        }
    },

    PHONE_TOTAL_CALL_NUMBER(2, "PHONE_TOTAL_CALL_NUMBER", "号码档案-通话总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 8 == dimension;
        }
    },

    PHONE_ONE_WEEK_CALL_NUMBER(2, "PHONE_ONE_WEEK_CALL_NUMBER", "号码档案-近7天通话次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 8 == dimension;
        }
    },

    PHONE_TOTAL_SMS_NUMBER(2, "PHONE_TOTAL_SMS_NUMBER", "号码档案-短信总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 10 == dimension;
        }
    },

    PHONE_ONE_WEEK_SMS_NUMBER(2, "PHONE_ONE_WEEK_SMS_NUMBER", "号码档案-近7天短信条数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 10 == dimension;
        }
    },

    PHONE_TOTAL_APP_NUMBER(2, "PHONE_TOTAL_APP_NUMBER", "号码档案-应用总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 2 == dimension;
        }
    },

    PHONE_ONE_WEEK_APP_NUMBER(2, "PHONE_ONE_WEEK_APP_NUMBER", "号码档案-近7天应用数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 2 == dimension;
        }
    },

    PHONE_TOTAL_VIRTUAL_ACCOUNT_NUMBER(2, "PHONE_TOTAL_VIRTUAL_ACCOUNT_NUMBER", "号码档案-虚拟账号总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 4 == dimension;
        }
    },

    PHONE_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER(2, "PHONE_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER", "号码档案-近7天虚拟账号个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 4 == dimension;
        }
    },

    PHONE_TOTAL_BLOCK_APP_NUMBER(2, "PHONE_TOTAL_BLOCK_APP_NUMBER", "号码档案-总阻断应用数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 7 == dimension;
        }
    },

    PHONE_ONE_WEEK_BLOCK_APP_NUMBER(2, "PHONE_ONE_WEEK_BLOCK_APP_NUMBER", "号码档案-近7天阻断应用数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 7 == dimension;
        }
    },

    PHONE_TOTAL_BLOCK_NUMBER(2, "PHONE_TOTAL_BLOCK_NUMBER", "号码档案-总阻断行为数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    PHONE_ONE_WEEK_BLOCK_NUMBER(2, "PHONE_ONE_WEEK_BLOCK_NUMBER", "号码档案-近7天阻断行为数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.PHONE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    PHONE_QUERY_APP_TIME_BY_BLOCK_DAY_INFO(1, "PHONE_QUERY_APP_TIME_BY_BLOCK_DAY_INFO", "号码档案-阻断应用列表"),

    PHONE_QUERY_APP_TIME_DAY_INFO(1, "PHONE_QUERY_APP_TIME_DAY_INFO", "号码档案-应用时段分析"),
    PHONE_QUERY_APP_LIST_DAY_INFO(1, "PHONE_QUERY_APP_LIST_DAY_INFO", "号码档案-应用列表"),

    PHONE_QUERY_APP_LIST_BY_BLOCK_DAY_INFO(1, "PHONE_QUERY_APP_LIST_BY_BLOCK_DAY_INFO", "号码档案-阻断应用列表"),


    PHONE_QUERY_BLOCK_APP_TABLE_PIE_INFO(1, "PHONE_QUERY_BLOCK_APP_TABLE_PIE_INFO", "号码档案-阻断应用饼图"),

    PHONE_QUERY_BLOCK_APP_TABLE_LINE_CHART_INFO(1, "PHONE_QUERY_BLOCK_APP_TABLE_LINE_CHART_INFO", "号码档案-阻断应用折线图"),

    PHONE_QUERY_APP_TABLE_PIE_INFO(1, "PHONE_QUERY_APP_TABLE_PIE_INFO", "号码档案-应用饼图"),

    PHONE_QUERY_APP_TABLE_LINE_CHART_INFO(1, "PHONE_QUERY_APP_TABLE_LINE_CHART_INFO", "号码档案-应用折线图"),

    PHONE_TOP_VIRTUAL_ACCOUNT(1, "PHONE_TOP_VIRTUAL_ACCOUNT", "号码档案-虚拟账号Top"),

    PHONE_TOP_APP(1, "PHONE_TOP_APP", "号码档案-应用Top"),

    PHONE_TOP_PHONE(1, "PHONE_TOP_PHONE", "号码档案-通联号码Top"),

    PHONE_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE(3, "PHONE_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE", "号码档案, 关联虚拟账户类型统计"),


    PHONE_CONNECT_AREA_BELONG(1, "PHONE_CONNECT_AREA_BELONG", "号码档案通联归属区域"),

    PHONE_FREQUENT_ACTIVE_AREA(1,"PHONE_FREQUENT_ACTIVE_AREA","常活动区域(基站信息)"),
    PHONE_REAL_TIME_TRAJECTORY(1,"PHONE_REAL_TIME_TRAJECTORY","实时轨迹"),
    PHONE_HIS_DAY_TRAJECTORY(1,"PHONE_HIS_DAY_TRAJECTORY","历史天轨迹"),

    PHONE_HIS_WEEK_TRAJECTORY(1,"PHONE_HIS_WEEK_TRAJECTORY","历史周轨迹"),

    PHONE_HIS_MONTH_TRAJECTORY(1,"PHONE_HIS_MONTH_TRAJECTORY","历史月轨迹"),
    PHONE_AREA_HOT_TRAJECTORY(1,"PHONE_AREA_HOT_TRAJECTORY","区域热力图"),
    PHONE_VIRTUAL_ACCOUNT_TAB(1,"PHONE_VIRTUAL_ACCOUNT_TAB","虚拟账号Tab"),
    PHONE_COMMUNICATION_TREND(1,"PHONE_COMMUNICATION_TREND","通联日趋势"),
    PHONE_COMMUNICATION_RANK(1,"PHONE_COMMUNICATION_RANK","通联号码次数排名"),
    PHONE_NETWORK_TYPE_DISTRIBUTION(1,"PHONE_NETWORK_TYPE_DISTRIBUTION","网络类型分布"),
    PHONE_USE_DEVICE_RANK(1,"PHONE_USE_DEVICE_RANK","使用设备排名"),

    PHONE_IMEI_LIST(1,"PHONE_IMEI_LIST","使用设备列表"),
    PHONE_IMEI_LIST_BATCH(1,"PHONE_IMEI_LIST_BATCH","批量查询使用设备列表"),
    PHONE_CONNECT_AREA_BELONG_DETAIL(1,"PHONE_CONNECT_AREA_BELONG_DETAIL","号码档案通联区域Tab"),
    PHONE_CONNECT_AREA_BELONG_DETAIL_DRILL_DOWN(1,"PHONE_CONNECT_AREA_BELONG_DETAIL_DRILL_DOWN","号码档案-通联归属区域-通联号码个数下钻"),

    PHONE_CONNECT_AREA_DETAIL_INFO(1, "PHONE_CONNECT_AREA_DETAIL_INFO", "号码档案 通联区域明细下钻"),

    DOMAIN_TOTAL_BEHAVIOR_NUMBER(2, "DOMAIN_TOTAL_BEHAVIOR_NUMBER", "网站档案-访问次数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    DOMAIN_ONE_WEEK_BEHAVIOR_NUMBER(2, "DOMAIN_ONE_WEEK_BEHAVIOR_NUMBER", "网站档案-近7天访问次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    DOMAIN_TOTAL_BLOCK_BEHAVIOR_NUMBER(2, "DOMAIN_TOTAL_BLOCK_BEHAVIOR_NUMBER", "网站档案-阻断访问总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    DOMAIN_ONE_WEEK_BLOCK_BEHAVIOR_NUMBER(2, "DOMAIN_ONE_WEEK_BLOCK_BEHAVIOR_NUMBER", "网站档案-近7天阻断访问数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    DOMAIN_TOTAL_IMPORTANT_TARGET_NUMBER(2, "DOMAIN_TOTAL_IMPORTANT_TARGET_NUMBER", "网站档案-重要目标总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    DOMAIN_ONE_WEEK_IMPORTANT_TARGET_NUMBER(2, "DOMAIN_ONE_WEEK_IMPORTANT_TARGET_NUMBER", "网站档案-近7天重要目标数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    DOMAIN_TOTAL_IP_NUMBER(2, "DOMAIN_TOTAL_IP_NUMBER", "网站档案-占用IP总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 13 == dimension;
        }
    },

    DOMAIN_ONE_WEEK_IP_NUMBER(2, "DOMAIN_ONE_WEEK_IP_NUMBER", "网站档案-近7天占用IP数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 13 == dimension;
        }
    },

    DOMAIN_TOTAL_VISIT_AREA(2, "DOMAIN_TOTAL_VISIT_AREA", "网站档案-总访问区域数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    DOMAIN_ONE_WEEK_VISIT_AREA(2, "DOMAIN_ONE_WEEK_VISIT_AREA", "网站档案-近7天访问区域数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    DOMAIN_TOTAL_ARC_NUMBER(2, "DOMAIN_TOTAL_ARC_NUMBER", "网站档案-总档案访问数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 12 == dimension;
        }
    },

    DOMAIN_ONE_WEEK_ARC_NUMBER(2, "DOMAIN_ONE_WEEK_ARC_NUMBER", "网站档案-近7天档案访问数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.WEB_SITE.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 12 == dimension;
        }
    },

    DOMAIN_ARC_TARGET_TOP(2, "DOMAIN_ARC_TARGET_TOP", "网站档案-档案访问TOP不包含当天"),
    DOMAIN_IP_TARGET_TOP(2, "DOMAIN_IP_TARGET_TOP", "网站档案-IP访问TOP不包含当天"),

    DOMAIN_IMPORTANT_TARGET_TOP(2, "DOMAIN_IMPORTANT_TARGET_TOP", "网站档案-重要目标访问TOP不包含当天"),

    DOMAIN_ARC_TARGET_DAY_LIST(2, "DOMAIN_ARC_TARGET_DAY_LIST", "网站档案-天档案访问目标列表"),
    DOMAIN_IP_TARGET_DAY_LIST(2, "DOMAIN_IP_TARGET_DAY_LIST", "网站档案-天IP访问目标列表"),

    DOMAIN_SERVER_IP_LIST(2, "DOMAIN_SERVER_IP_LIST", "网站档案-服务器IP列表"),

    DOMAIN_IMPORTANT_TARGET_DAY_LIST(2, "DOMAIN_IMPORTANT_TARGET_DAY_LIST", "网站档案-天重要目标访问目标列表"),

    RADIUS_TOTAL_ACTIVE_NUMBER(2, "RADIUS_TOTAL_ACTIVE_NUMBER", "Radius档案-活跃次数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    RADIUS_ONE_WEEK_ACTIVE_NUMBER(2, "RADIUS_ONE_WEEK_ACTIVE_NUMBER", "RADIUS档案-近7天活跃次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    RADIUS_TOTAL_VIRTUAL_ACCOUNT_NUMBER(2, "RADIUS_TOTAL_VIRTUAL_ACCOUNT_NUMBER", "RADIUS档案-虚拟账号总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 4 == dimension;
        }
    },

    RADIUS_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER(2, "RADIUS_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER", "RADIUS档案-近7天虚拟账号数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 4 == dimension;
        }
    },

    RADIUS_TOTAL_APP_NUMBER(2, "RADIUS_TOTAL_APP_NUMBER", "RADIUS档案-应用个数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 2 == dimension;
        }
    },

    RADIUS_ONE_WEEK_APP_NUMBER(2, "RADIUS_ONE_WEEK_APP_NUMBER", "RADIUS档案-近7天应用个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 2 == dimension;
        }
    },

    RADIUS_TOTAL_FILE_NUMBER(2, "RADIUS_TOTAL_FILE_NUMBER", "RADIUS档案-文件个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 6 == dimension;
        }
    },

    RADIUS_ONE_WEEK_FILE_NUMBER(2, "RADIUS_ONE_WEEK_FILE_NUMBER", "RADIUS档案-近7天文件个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 6 == dimension;
        }
    },

    RADIUS_TOTAL_BLOCK_NUMBER(2, "RADIUS_TOTAL_BLOCK_NUMBER", "RADIUS档案-总阻断数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    RADIUS_ONE_WEEK_BLOCK_NUMBER(2, "RADIUS_ONE_WEEK_BLOCK_NUMBER", "RADIUS档案-近7天阻断数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    RADIUS_TOTAL_BLOCK_APP_NUMBER(2, "RADIUS_TOTAL_BLOCK_APP_NUMBER", "RADIUS档案-总阻断应用个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 7 == dimension;
        }
    },

    RADIUS_ONE_WEEK_BLOCK_APP_NUMBER(2, "RADIUS_ONE_WEEK_BLOCK_APP_NUMBER", "RADIUS档案-近7天阻断应用个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.RADIUS.getValue().equals(arcType) || ArcTypeEnum.FIXED_IP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 7 == dimension;
        }
    },


    RADIUS_TOP_APP(3, "RADIUS_TOP_APP", "radius 档案应用排名"),
    RADIUS_TOP_VIRTUAL_ACCOUNT(3, "RADIUS_TOP_VIRTUAL_ACCOUNT", "radius 档案虚拟账户排名"),
    RADIUS_VIRTUAL_ACCOUNT_TAB(3, "RADIUS_VIRTUAL_ACCOUNT_TAB", "radius 虚拟账号"),

    RADIUS_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE(3, "RADIUS_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE", "radius 档案, 关联虚拟账户类型统计"),
    QUERY_ATTACHMENT_INFO(3, "QUERY_ATTACHMENT_INFO", "radius/固定IP,号码档案附件查询"),
    QUERY_ATTACHMENT_INFO_OVERVIEW(3, "QUERY_ATTACHMENT_INFO_OVERVIEW", "档案概览附件查询"),
    QUERY_ATTACHMENT_INFO_FILE_PATH(3, "QUERY_ATTACHMENT_INFO_FILE_PATH", "radius/固定IP,号码档案附件关联报文地址查询"),

    QUERY_DOMAIN_ATTACHMENT_INFO_FILE_PATH(3, "QUERY_DOMAIN_ATTACHMENT_INFO_FILE_PATH", "网站档案附件关联报文地址查询"),
    RADIUS_QUERY_APP_LIST_DAY_INFO(3, "RADIUS_QUERY_APP_LIST_DAY_INFO", "radius 档案应用列表"),
    RADIUS_QUERY_APP_LIST_BY_BLOCK_DAY_INFO(3, "RADIUS_QUERY_APP_LIST_BY_BLOCK_DAY_INFO", "radius 档案应用阻断列表"),
    RADIUS_QUERY_APP_TABLE_PIE_INFO(3, "RADIUS_QUERY_APP_TABLE_PIE_INFO", "radius 档案应用外图"),

    RADIUS_QUERY_BLOCK_APP_TABLE_PIE_INFO(3, "RADIUS_QUERY_BLOCK_APP_TABLE_PIE_INFO", "radius 档案阻断应用外图"),

    RADIUS_QUERY_APP_TABLE_LINE_CHART_INFO(3, "RADIUS_QUERY_APP_TABLE_LINE_CHART_INFO", "radius 档案应用折线图"),

    RADIUS_QUERY_BLOCK_APP_TABLE_LINE_CHART_INFO(3, "RADIUS_QUERY_BLOCK_APP_TABLE_LINE_CHART_INFO", "radius 档案阻断应用折线图"),
    RADIUS_QUERY_APP_TIME_DAY_INFO(3, "RADIUS_QUERY_APP_TIME_DAY_INFO", "radius 档案-应用时段分析"),
    RADIUS_QUERY_APP_TIME_BY_BLOCK_DAY_INFO(3, "RADIUS_QUERY_APP_TIME_BY_BLOCK_DAY_INFO", "radius 档案-阻断应用时段分析"),
    RADIUS_AUTH_RECORD(3,"RADIUS_AUTH_RECORD","radius档案认证记录列表"),

    RADIUS_AUTH_RECORD_DURATION_STATISTICS(3,"RADIUS_AUTH_RECORD_DURATION_STATISTICS","radius档案认证记录时长统计"),

    RADIUS_QUERY_AUTH_ACCOUNT_STATISTICS(3, "RADIUS_QUERY_AUTH_ACCOUNT_STATISTICS", "radius/固定IP档案统计信息"),

    EMAIL_TOTAL_BEHAVIOR_NUMBER(2, "EMAIL_TOTAL_BEHAVIOR_NUMBER", "Email档案-活跃次数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    EMAIL_ONE_WEEK_BEHAVIOR_NUMBER(2, "EMAIL_ONE_WEEK_BEHAVIOR_NUMBER", "Email档案-近7天活跃次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    EMAIL_TOTAL_COMMUNICATE_MAIL_NUMBER(2, "EMAIL_TOTAL_COMMUNICATE_MAIL_NUMBER", "Email档案-通联邮箱总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 14 == dimension;
        }
    },

    EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER(2, "EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER", "Email档案-近7天通联邮箱数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 14 == dimension;
        }
    },

    EMAIL_TOTAL_ACTIVE_AREA_NUMBER(2, "EMAIL_TOTAL_ACTIVE_AREA_NUMBER", "Email档案-活跃区域总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 15 == dimension;
        }
    },

    EMAIL_ONE_WEEK_ACTIVE_AREA_NUMBER(2, "EMAIL_ONE_WEEK_ACTIVE_AREA_NUMBER", "Email档案-近7天活跃区域数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 15 == dimension;
        }
    },

    EMAIL_TOTAL_NICKNAME_NUMBER(2, "EMAIL_TOTAL_NICKNAME_NUMBER", "Email档案-昵称总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 16 == dimension;
        }
    },

    EMAIL_ONE_WEEK_NICKNAME_NUMBER(2, "EMAIL_ONE_WEEK_NICKNAME_NUMBER", "Email档案-近7天昵称数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 16 == dimension;
        }
    },

    EMAIL_TOTAL_PASSWORD_NUMBER(2, "EMAIL_TOTAL_PASSWORD_NUMBER", "Email档案-密码总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 17 == dimension;
        }
    },

    EMAIL_ONE_WEEK_PASSWORD_NUMBER(2, "EMAIL_ONE_WEEK_PASSWORD_NUMBER", "Email档案-近7天密码个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 17 == dimension;
        }
    },

    EMAIL_TOTAL_FILE_NUMBER(2, "EMAIL_TOTAL_FILE_NUMBER", "Email档案-文件总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 6 == dimension;
        }
    },

    EMAIL_ONE_WEEK_FILE_NUMBER(2, "EMAIL_ONE_WEEK_FILE_NUMBER", "Email档案-近7天文件个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 6 == dimension;
        }
    },

    EMAIL_TOTAL_IMPORTANT_TARGET_NUMBER(2, "EMAIL_TOTAL_IMPORTANT_TARGET_NUMBER", "Email档案-总重要目标个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    EMAIL_ONE_WEEK_IMPORTANT_TARGET_NUMBER(2, "EMAIL_ONE_WEEK_IMPORTANT_TARGET_NUMBER", "Email档案-近7天重要目标个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.EMAIL.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    PHONE_LAST_RELATION_AREA_STATISTICS(2, "PHONE_LAST_RELATION_AREA_STATISTICS", "号码档案-统计表查询区域信息"),

    EMAIL_LAST_RELATION_AREA_STATISTICS(2, "EMAIL_LAST_RELATION_AREA_STATISTICS", "Email档案-统计表查询区域信息"),

    IM_TOTAL_BEHAVIOR_NUMBER(2, "IM_TOTAL_BEHAVIOR_NUMBER", "IM档案-活跃次数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    IM_ONE_WEEK_BEHAVIOR_NUMBER(2, "IM_ONE_WEEK_BEHAVIOR_NUMBER", "IM档案-近7天活跃次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    IM_TOTAL_ACTIVE_AREA_NUMBER(2, "IM_TOTAL_ACTIVE_AREA_NUMBER", "IM档案-活跃区域总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    IM_ONE_WEEK_ACTIVE_AREA_NUMBER(2, "IM_ONE_WEEK_ACTIVE_AREA_NUMBER", "IM档案-近7天活跃区域数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    IM_TOTAL_COMMUNICATE_ACCOUNT_NUMBER(2, "IM_TOTAL_COMMUNICATE_ACCOUNT_NUMBER", "IM档案-通联账号总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 4 == dimension;
        }
    },

    IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER(2, "IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER", "IM档案-近7天通联账号数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 4 == dimension;
        }
    },

    IM_TOTAL_FILE_NUMBER(2, "IM_TOTAL_FILE_NUMBER", "IM档案-文件总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 6 == dimension;
        }
    },

    IM_ONE_WEEK_FILE_NUMBER(2, "IM_ONE_WEEK_FILE_NUMBER", "IM档案-近7天文件个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 6 == dimension;
        }
    },

    IM_TOTAL_IMPORTANT_TARGET_NUMBER(2, "IM_TOTAL_IMPORTANT_TARGET_NUMBER", "IM档案-总重要目标个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    IM_ONE_WEEK_IMPORTANT_TARGET_NUMBER(2, "IM_ONE_WEEK_IMPORTANT_TARGET_NUMBER", "IM档案-近7天重要目标个数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.IM.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    IM_LAST_RELATION_AREA_STATISTICS(4, "IM_LAST_RELATION_AREA_STATISTICS", "IM档案-统计表查询区域信息"),
    IM_LAST_RELATIOJN_NICKNAME_CAPTUREDAY(4, "IM_LAST_RELATIOJN_NICKNAME_CAPTUREDAY", "IM档案-查询关联到昵称的最新日期"),
    IM_LAST_RELATIOJN_NICKNAME(4, "IM_LAST_RELATIOJN_NICKNAME", "IM档案-查询关联到的最新昵称"),

    EMAIL_TOP_EMAIL_ACCOUNT(4, "EMAIL_TOP_EMAIL_ACCOUNT", "Email档案-常通联邮箱Top"),

    IM_TOP_COMMUNICATE_ACCOUNT(4, "IM_TOP_COMMUNICATE_ACCOUNT", "IM档案-常通联账号Top"),



    APP_TOTAL_BEHAVIOR_NUMBER(2, "APP_TOTAL_BEHAVIOR_NUMBER", "应用档案-访问次数总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    APP_ONE_WEEK_BEHAVIOR_NUMBER(2, "APP_ONE_WEEK_BEHAVIOR_NUMBER", "应用档案-近7天访问次数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 1 == dimension;
        }
    },

    APP_TOTAL_BLOCK_BEHAVIOR_NUMBER(2, "APP_TOTAL_BLOCK_BEHAVIOR_NUMBER", "应用档案-阻断访问总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    APP_ONE_WEEK_BLOCK_BEHAVIOR_NUMBER(2, "APP_ONE_WEEK_BLOCK_BEHAVIOR_NUMBER", "应用档案-近7天阻断访问数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 5 == dimension;
        }
    },

    APP_TOTAL_IMPORTANT_TARGET_NUMBER(2, "APP_TOTAL_IMPORTANT_TARGET_NUMBER", "应用档案-重要目标总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    APP_ONE_WEEK_IMPORTANT_TARGET_NUMBER(2, "APP_ONE_WEEK_IMPORTANT_TARGET_NUMBER", "应用档案-近7天重要目标数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 11 == dimension;
        }
    },

    APP_TOTAL_IP_NUMBER(2, "APP_TOTAL_IP_NUMBER", "应用档案-占用IP总数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 13 == dimension;
        }
    },

    APP_ONE_WEEK_IP_NUMBER(2, "APP_ONE_WEEK_IP_NUMBER", "应用档案-近7天占用IP数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 13 == dimension;
        }
    },

    APP_TOTAL_VISIT_AREA(2, "APP_TOTAL_VISIT_AREA", "应用档案-总访问区域数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    APP_ONE_WEEK_VISIT_AREA(2, "APP_ONE_WEEK_VISIT_AREA", "应用档案-近7天访问区域数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 3 == dimension;
        }
    },

    APP_TOTAL_ARC_NUMBER(2, "APP_TOTAL_ARC_NUMBER", "应用档案-总档案访问数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForTotal() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 12 == dimension;
        }
    },

    APP_ONE_WEEK_ARC_NUMBER(2, "APP_ONE_WEEK_ARC_NUMBER", "应用档案-近7天档案访问数") {

        @Override
        public  boolean usedForArcType(String arcType) {
            return ArcTypeEnum.APP.getValue().equals(arcType);
        }

        @Override
        public boolean usedForFunctional(String functional) {
            return "dimension_statistics".equals(functional);
        }

        @Override
        public boolean usedForOneWeek() {
            return true;
        }

        @Override
        public boolean usedForDimension(int dimension) {
            return 12 == dimension;
        }
    },

    APP_ONE_WEEK_ACTIVE(5, "APP_ONE_WEEK_ACTIVE", "应用档案-近一周活跃次数"),
    APP_TODAY_ACTIVE(5, "APP_TODAY_ACTIVE", "应用档案-当天活跃次数"),
    APP_ONE_WEEK_CONTAIN_TODAY_VISIT_AREA(5,"APP_ONE_WEEK_CONTAIN_TODAY_VISIT_AREA","应用档案-近一周访问区域数包含当天"),

    APP_ONE_WEEK_CONTAIN_TODAY_ARC_NUMBER(5,"APP_ONE_WEEK_CONTAIN_TODAY_ARC_NUMBER","应用档案-近一周档案访问数包含当天"),

    APP_CONTAIN_TODAY_BLOCK_ACTIVE(5,"APP_CONTAIN_TODAY_BLOCK_ACTIVE","应用档案-总阻断次数包含当天"),
    APP_TOTAL_BLOCK_ACTIVE(5, "APP_TOTAL_BLOCK_ACTIVE", "应用档案-总阻断次数不包含当天"),
    APP_ONE_WEEK_CONTAIN_TODAY_BLOCK_ACTIVE(5,"APP_ONE_WEEK_CONTAIN_TODAY_BLOCK_ACTIVE","应用档案-近一周阻断次数包含当天"),
    APP_ONE_WEEK_BLOCK_ACTIVE(5, "APP_ONE_WEEK_BLOCK_ACTIVE", "应用档案-近一周访问次数不包含当天"),

    APP_ONE_WEEK_ACTIVE_BATCH(5, "APP_ONE_WEEK_ACTIVE_BATCH", "应用档案-近一周活跃次数"),
    APP_TODAY_ACTIVE_BATCH(5, "APP_TODAY_ACTIVE_BATCH", "应用档案-当天活跃次数"),
    APP_TOTAL_VISIT_AREA_BATCH(5, "APP_TOTAL_VISIT_AREA_BATCH", "应用档案-总访问区域数据不包含当天"),
    APP_ONE_WEEK_CONTAIN_TODAY_VISIT_AREA_BATCH(5,"APP_ONE_WEEK_CONTAIN_TODAY_VISIT_AREA_BATCH","应用档案-近一周访问区域数包含当天"),
    APP_ONE_WEEK_VISIT_AREA_BATCH(5, "APP_ONE_WEEK_VISIT_AREA_BATCH", "应用档案-近一周访问区域数不包含当天"),
    APP_TOTAL_ARC_NUMBER_BATCH(5, "APP_TOTAL_ARC_NUMBER_BATCH", "应用档案-总档案访问数不包含当天"),
    APP_CONTAIN_TODAY_ARC_NUMBER_BATCH(5,"APP_CONTAIN_TODAY_ARC_NUMBER_BATCH","应用档案-总档案访问数包含当天"),
    APP_ONE_WEEK_CONTAIN_TODAY_ARC_NUMBER_BATCH(5,"APP_ONE_WEEK_CONTAIN_TODAY_ARC_NUMBER_BATCH","应用档案-近一周档案访问数包含当天"),
    APP_ONE_WEEK_ARC_NUMBER_BATCH(5, "APP_ONE_WEEK_ARC_NUMBER_BATCH", "应用档案-近一周档案访问数不包含当天"),
    APP_CONTAIN_TODAY_BLOCK_ACTIVE_BATCH(5,"APP_CONTAIN_TODAY_BLOCK_ACTIVE_BATCH","应用档案-总阻断次数包含当天"),
    APP_TOTAL_BLOCK_ACTIVE_BATCH(5, "APP_TOTAL_BLOCK_ACTIVE_BATCH", "应用档案-总阻断次数不包含当天"),
    APP_ONE_WEEK_CONTAIN_TODAY_BLOCK_ACTIVE_BATCH(5,"APP_ONE_WEEK_CONTAIN_TODAY_BLOCK_ACTIVE_BATCH","应用档案-近一周阻断次数包含当天"),
    APP_ONE_WEEK_BLOCK_ACTIVE_BATCH(5, "APP_ONE_WEEK_BLOCK_ACTIVE_BATCH", "应用档案-近一周访问次数不包含当天"),
    APP_CONTAIN_TODAY_ATTACHMENT_NUMBER_PERFORM_BATCH(5,"APP_CONTAIN_TODAY_ATTACHMENT_NUMBER_PERFORM_BATCH","应用档案-性能统计总计附数包含当天"),
    APP_TOTAL_ATTACHMENT_NUMBER_PERFORM_BATCH(5,"APP_TOTAL_ATTACHMENT_NUMBER_PERFORM_BATCH","应用档案-性能统计总计附件数不包含当天"),
    APP_ONE_WEEK_CONTAIN_TODAY_ATTACHMENT_NUMBER_PERFORM_BATCH(5,"APP_ONE_WEEK_CONTAIN_TODAY_ATTACHMENT_NUMBER_PERFORM_BATCH","应用档案-性能统计近一周附件数包含当天"),
    APP_CONTAIN_TODAY_VISIT_AREA_BATCH(5,"APP_CONTAIN_TODAY_VISIT_AREA_BATCH","应用档案-包含当天活跃区域总数"),

    APP_SERVER_IP_LIST(5, "APP_SERVER_IP_LIST", "应用档案-服务器IP列表"),

    APP_ARC_TARGET_DAY_LIST(5, "APP_ARC_TARGET_DAY_LIST", "应用档案日访问目标列表"),
    APP_ARC_TARGET_BLOCK_DAY_LIST(5, "APP_ARC_TARGET_BLOCK_DAY_LIST", "应用档案日访问目标阻断列表"),
    APP_ARC_TARGET_MONTH_LIST(5, "APP_ARC_TARGET_MONTH_LIST", "应用档案月访问目标列表"),
    APP_ARC_TARGET_BLOCK_MONTH_LIST(5, "APP_ARC_TARGET_BLOCK_MONTH_LIST", "应用档案月访问目标阻断列表"),
    APP_IP_TARGET_DAY_LIST(5, "APP_IP_TARGET_DAY_LIST", "应用档案日IP访问目标列表"),

    APP_IMPORTANT_TARGET_DAY_LIST(5, "APP_IMPORTANT_TARGET_DAY_LIST", "应用档案日重要访问目标列表"),
    APP_IP_TARGET_BLOCK_DAY_LIST(5, "APP_IP_TARGET_BLOCK_DAY_LIST", "应用档案日IP访问目标阻断列表"),
    APP_IP_TARGET_MONTH_LIST(5, "APP_IP_TARGET_MONTH_LIST", "应用档案月IP访问目标列表"),
    APP_IP_TARGET_BLOCK_MONTH_LIST(5, "APP_IP_TARGET_BLOCK_MONTH_LIST", "应用档案月IP访问目标阻断列表"),
    APP_DAY_HEAT_MAP(5, "APP_DAY_HEAT_MAP", "应用档案按天热图"),
    APP_MONTH_HEAT_MAP(5, "APP_MONTH_HEAT_MAP", "应用档案按月热图"),
    APP_ARC_TARGET_TOP_CONTAIN_TODAY(5,"APP_ARC_TARGET_TOP_CONTAIN_TODAY","应用档案-档案访问TOP包含当天"),
    APP_ARC_TARGET_TOP(5, "APP_ARC_TARGET_TOP", "应用档案-档案访问TOP不包含当天"),
    APP_IP_TARGET_TOP_CONTAIN_TODAY(5,"APP_IP_TARGET_TOP_CONTAIN_TODAY","应用档案-IP访问TOP包含当天"),
    APP_IP_TARGET_TOP(5, "APP_IP_TARGET_TOP", "应用档案-IP访问TOP不包含当天"),

    APP_IMPORTANT_TARGET_TOP(5, "APP_IMPORTANT_TARGET_TOP", "应用档案-重要目标访问TOP不包含当天"),
    APP_QUERY_IP_LIST_INFO(5, "APP_QUERY_IP_LIST_INFO", "应用档案最近IP列表"),
    APP_CONTAIN_TODAY_VISIT_AREA(5,"APP_CONTAIN_TODAY_VISIT_AREA","应用档案-总访问区域数包含当天"),
    APP_CONTAIN_TODAY_ARC_NUMBER(5,"APP_CONTAIN_TODAY_ARC_NUMBER","应用档案-总档案访问数包含当天"),

    COMMON_RADIUS_DAY_TREND_ANALYZE(6, "COMMON_RADIUS_DAY_TREND_ANALYZE", "radius活跃区域分析"),
    COMMON_VIRTUAL_DAY_TREND_ANALYZE(6, "COMMON_VIRTUAL_DAY_TREND_ANALYZE", "虚拟活跃区域分析"),
    COMMON_APP_DAY_TREND_ANALYZE(6, "COMMON_APP_DAY_TREND_ANALYZE", "APP活跃区域分析"),
    COMMON_IM_DAY_TREND_ANALYZE(6, "COMMON_IM_DAY_TREND_ANALYZE", "活跃趋势分析"),

    COMMON_IM_LIS_DAY_TREND_ANALYZE(6, "COMMON_IM_LIS_DAY_TREND_ANALYZE", "LIS活跃趋势分析"),

    COMMON_APP_TYPE_LIST(6, "COMMON_APP_TYPE_LIST", "APP类型列表"),
    COMMON_DOMAIN_DAY_TREND_ANALYZE(6, "COMMON_DOMAIN_DAY_TREND_ANALYZE", "DOMAIN活跃区域分析"),
    COMMON_PHONE_DAY_TREND_ANALYZE(6, "COMMON_PHONE_DAY_TREND_ANALYZE", "PHONE活跃区域分析"),

    COMMON_EMAIL_DAY_TREND_ANALYZE(6, "COMMON_EMAIL_DAY_TREND_ANALYZE", "Email活跃区域分析"),

    COMMON_PHONE_ACTIVE_CITY_MAP(6, "COMMON_PHONE_ACTIVE_CITY_MAP", "PHONE活跃地分析"),
    COMMON_RADIUS_DAY_HOUR_ANALYZE(6, "COMMON_RADIUS_DAY_HOUR_ANALYZE", "radius时段分析"),
    COMMON_VIRTUAL_DAY_HOUR_ANALYZE(6, "COMMON_VIRTUAL_DAY_HOUR_ANALYZE", "虚拟时段分析"),
    COMMON_APP_DAY_HOUR_ANALYZE(6, "COMMON_APP_DAY_HOUR_ANALYZE", "APP时段分析"),
    COMMON_DOMAIN_DAY_HOUR_ANALYZE(6, "COMMON_DOMAIN_DAY_HOUR_ANALYZE", "DOMAIN时段分析"),
    COMMON_PHONE_DAY_HOUR_ANALYZE(6, "COMMON_PHONE_DAY_HOUR_ANALYZE", "PHONE时段分析"),
    COMMON_EMAIL_DAY_HOUR_ANALYZE(6, "COMMON_EMAIL_DAY_HOUR_ANALYZE", "Email时段分析"),
    COMMON_IM_DAY_HOUR_ANALYZE(6, "COMMON_IM_DAY_HOUR_ANALYZE", "IM时段分析"),

    COMMON_APP_DAY_COMMUNICATION_AREA(6, "COMMON_APP_DAY_COMMUNICATION_AREA", "应用通联区域"),
    COMMON_DOMAIN_DAY_COMMUNICATION_AREA(6, "COMMON_DOMAIN_DAY_COMMUNICATION_AREA", "网站通联区域"),

    COMMON_GET_APP_LIST_CONDITIONS(6, "COMMON_GET_APP_LIST_CONDITIONS", "查询应用分析过滤条件"),
    COMMON_GET_TAG_TOP_LIST(6, "COMMON_GET_TAG_TOP_LIST", "查询档案标签TOP列表"),
    COMMON_GET_TAG_LIST_DAY(6, "COMMON_GET_TAG_LIST_DAY", "按天查询档案标签列表"),
    COMMON_GET_TAG_LIST_MONTH(6, "COMMON_GET_TAG_LIST_MONTH", "按月查询档案标签列表"),

    //关系扩线相关枚举
    PHONE_RELATION_EXTENSION(7, "PHONE_RELATION_EXTENSION", "号码关系 扩线"),
    RADIUS_RELATE_VIRTUAL(7, "RADIUS_RELATE_VIRTUAL", "radius 关联虚拟账号"),

    PHONE_RELATE_VIRTUAL(7, "PHONE_RELATE_VIRTUAL", "号码 关联虚拟账号"),
    VIRTUAL_RELATE_RADIUS(7, "VIRTUAL_RELATE_RADIUS", "虚拟关联 关联 radius 账号"),

    PHONE_VIRTUAL_RELATE_RADIUS(7, "PHONE_VIRTUAL_RELATE_RADIUS", "号码档案扩线 虚拟关联 关联 radius 账号"),

    HAS_MORE_RELATION(7, "HAS_MORE_RELATION", "可扩线标识查询"),
    EMAIL_RELATION_EXTENSION(7, "EMAIL_RELATION_EXTENSION", "EMAIL 扩线"),
    EMAIL_RELATION_ACCOUNT(7, "EMAIL_RELATION_ACCOUNT", "EMAIL 相关邮箱"),
    EMAIL_NICKNAME_RECORD(7, "EMAIL_NICKNAME_RECORD", "EMAIL 昵称记录"),
    EMAIL_PASSWORD_RECORD(7, "EMAIL_PASSWORD_RECORD", "EMAIL 昵称记录"),

    EMAIL_PHONE_EXTRACT(7, "EMAIL_PHONE_EXTRACT", "EMAIL 号码提取"),
    EMAIL_PHONE_EXTRACT_SOURCE(7, "EMAIL_PHONE_EXTRACT_SOURCE", "EMAIL 号码提取 原始报文"),

    EMAIL_PHONE_EXTRACT_FILE_LIST(7, "EMAIL_PHONE_EXTRACT_FILE_LIST", "EMAIL 号码提取 文件列表"),

    PHONE_AREA_HOT_TIME_STATISTICS(7, "PHONE_AREA_HOT_TIME_STATISTICS", "号码档案活跃区域时段统计"),

    PHONE_AREA_HOT_STAY_RECORD(7, "PHONE_AREA_HOT_STAY_RECORD", "号码档案活跃区域驻留记录"),

    VIRTUAL_RELATE_AUTH_ACCOUNT(7, "VIRTUAL_RELATE_AUTH_ACCOUNT", "虚拟账号关联认证账号"),

    EMAIL_LIS_DETAIL_LIST(7, "EMAIL_LIS_DETAIL_LIST", "Email档案-LIS明细列表"),

    RADIUS_HIGH_FREQUENCY_WORDS_RANK(7,"RADIUS_HIGH_FREQUENCY_WORDS_RANK","高频词排名"),
    EMAIL_HIGH_FREQUENCY_WORDS_RANK(7,"EMAIL_HIGH_FREQUENCY_WORDS_RANK","高频词排名"),
    HIGH_FREQUENCY_WORDS_RANK_TOTAL(7,"HIGH_FREQUENCY_WORDS_RANK_TOTAL","高频词排名总计"),
    RADIUS_HIGH_FREQUENCY_WORDS_TREND(7, "RADIUS_HIGH_FREQUENCY_WORDS_TREND","高频词趋势"),
    EMAIL_HIGH_FREQUENCY_WORDS_TREND(7, "EMAIL_HIGH_FREQUENCY_WORDS_TREND","高频词趋势"),
    BANK_CARD_INFO(7,  "BANK_CARD_INFO","银行卡信息"),
    BANK_CARD_INFO_TOTAL(7,  "BANK_CARD_INFO_TOTAL","银行卡信息"),
    FLIGHT_INFO(7, "FLIGHT_INFO","航班信息"),
    FLIGHT_INFO_TOTAL(7, "FLIGHT_INFO_TOTAL","航班信息"),

    OWNER_INFO(8,"OWNER_INFO","机主信息"),
    OWNER_PHONE_INFO(8,"OWNER_PHONE_INFO","机主号码人员信息"),
    OWNER_RELATION_PHONE_INFO(8,"OWNER_RELATION_PHONE_INFO","机主家庭关系人人员信息"),

    DOMAIN_QUERY_STATISTICS(2, "DOMAIN_QUERY_STATISTICS", "网站档案统计信息"),
    APP_QUERY_STATISTICS(2, "APP_QUERY_STATISTICS", "应用档案统计信息");


    /**
     * 区分档案类型的编号
     */
    private final int key;

    private final String value;

    private final String description;

    /**
     * 适用的档案类型
     * 待后续所有业务码都复写该方法后可以将其转换为abstract方法
     * */
    public  boolean usedForArcType(String arcType) {
        return false;
    }

    public boolean usedForFunctional(String functional) {
        return false;
    }

    public boolean usedForTotal() {
        return false;
    }

    public boolean usedForOneWeek() {
        return false;
    }

    public boolean usedForDimension(int dimension) {
        return false;
    }

    BusinessCodeEnum(int key, String value, String description) {
        this.key = key;
        this.value = value;
        this.description = description;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static String getByKey(int key) {
        BusinessCodeEnum[] values = BusinessCodeEnum.values();
        for (BusinessCodeEnum value : values) {
            if (value.getKey() == key) {
                return value.getValue();
            }
        }
        return "";
    }

    public static String getDescriptionByCode(String code) {
        BusinessCodeEnum[] values = BusinessCodeEnum.values();
        for (BusinessCodeEnum value : values) {
            if (value.getValue().equals(code)) {
                return value.getValue() + ":" + value.getDescription();
            }
        }
        return "";
    }

    public static String getDescription(String code) {
        BusinessCodeEnum[] values = BusinessCodeEnum.values();
        for (BusinessCodeEnum value : values) {
            if (value.getValue().equals(code)) {
                return value.getDescription();
            }
        }
        return "";
    }
}
