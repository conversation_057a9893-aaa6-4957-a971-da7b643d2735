package com.semptian.archives.web.core.common.util;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;

import static java.util.concurrent.Executors.newFixedThreadPool;

/**
 * <AUTHOR>
 * @date  2020/9/10 16:31
 * @Description  线程池工具类
 */
public class ThreadPoolUtil {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolUtil.class);

    private static ExecutorService THREAD_POOL_EXECUTOR = newFixedThreadPool(
            getCPUCore(),
            new ThreadFactoryBuilder().setNamePrefix("common thread pool").build());

    private ThreadPoolUtil() {
    }

    /**
     * 默认构造方法
     *
     * @return 返回一个线程池
     */
    public static ExecutorService getDefaultExecutorInstance() {
        return THREAD_POOL_EXECUTOR;
    }

    /**
     * 获取CPU核数
     *
     * @return CPU核数
     */
    private static int getCPUCore() {
        int availProcess = Runtime.getRuntime().availableProcessors();
        logger.info("availProcess : {}", availProcess);
        return 2 * availProcess + 1;
    }

}
