package com.semptian.archives.web.core.common.enums;

import com.semptian.archives.web.core.ArchivesEngineCoreApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesEngineCoreApp.class)
public class BusinessCodeEnumTest {

    @Test
    public void test_domain_total_behaviour_number() {
        assertTrue(BusinessCodeEnum.DOMAIN_TOTAL_BEHAVIOR_NUMBER.usedForArcType(ArcTypeEnum.WEB_SITE.getValue()));
        assertTrue(BusinessCodeEnum.DOMAIN_TOTAL_BEHAVIOR_NUMBER.usedForFunctional("dimension_statistics"));
        assertTrue(BusinessCodeEnum.DOMAIN_TOTAL_BEHAVIOR_NUMBER.usedForTotal());
        assertTrue(BusinessCodeEnum.DOMAIN_TOTAL_BEHAVIOR_NUMBER.usedForDimension(1));

        List<BusinessCodeEnum> candidateBusinessCodesTotal = Arrays.stream(BusinessCodeEnum.values())
                .filter(code -> code.usedForArcType(ArcTypeEnum.WEB_SITE.getValue()))
               .filter(code -> code.usedForFunctional("dimension_statistics"))
                  .filter(BusinessCodeEnum::usedForTotal)
                 .filter(code -> code.usedForDimension(1))
                .collect(Collectors.toList());

        System.out.println(candidateBusinessCodesTotal.get(0).name());

        assertEquals(1, candidateBusinessCodesTotal.size());
    }
  
}