package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 证件类型枚举
 * <AUTHOR>
 * @date 2024/3/29
 */
@Getter
public enum CertificateTypeEnum {

    ID_CARD(1, "id_card"),

    PASSPORT(2, "passport"),

    DRIVER_LICENSE(3, "driver_license"),

    OTHER(4, "other");

    private final Integer code;

    private final String description;

    CertificateTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getDescByCode(Integer code) {
        CertificateTypeEnum[] values = CertificateTypeEnum.values();
        for (CertificateTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return "";
    }
}

