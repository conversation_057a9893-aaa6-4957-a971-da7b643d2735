package com.semptian.archives.web.service.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.semptian.archives.web.dao.archive.entity.ArcDictEntity;
import com.semptian.archives.web.service.common.enums.LanguageEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArcDictService;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27 17:03
 **/
@Slf4j
@Service
public class ImArcServiceImpl {

    @Resource
    private RedisOps redisOps;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private ArcDictService arcDictService;

    public Object getAppNameList(String lang) {
        String redisKey = arcCommonService.getRedisKey("getAppTypeList", lang);

        if (redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return (JSONArray) redisOps.get(redisKey);
        }

        JSONArray res = new JSONArray();

        // 查询IM类型
        QueryWrapper<ArcDictEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("dict_key", "child_type");
        wrapper.likeRight("dict_code",103);

        List<ArcDictEntity> arcDrillDownFieldTranslateConfigEntities = arcDictService.list(wrapper);

        for (ArcDictEntity target : arcDrillDownFieldTranslateConfigEntities) {
            JSONObject t = new JSONObject();
            String appType = target.getDictCode();

            if (StringUtils.isNotEmpty(appType)) {
                t.put("id", appType);
                if (LanguageEnum.ZH_CN.getLang().equals(lang)) {
                    t.put("label", target.getDictNameZh());
                } else if (LanguageEnum.FR_DZ.getLang().equals(lang)) {
                    t.put("label", target.getDictNameFr());
                } else if (LanguageEnum.EN_US.getLang().equals(lang)) {
                    t.put("label", target.getDictNameEn());
                } else {
                    t.put("label", target.getDictNameFr());
                }

                res.add(t);
            }
        }

        redisOps.set(redisKey, res, DateUtils.getRedisExpireTime());
        return res;
    }
}
