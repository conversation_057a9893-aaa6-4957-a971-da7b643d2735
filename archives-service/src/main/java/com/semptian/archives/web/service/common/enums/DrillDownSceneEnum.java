package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 *  下钻场景枚举
 */
@Getter
public enum DrillDownSceneEnum {

    CONNECT_RECORD_DETAIL("CONNECT_RECORD_DETAIL", "通联记录,通联归属区域"),
    LIS_DETAIL("LIS_DETAIL", "lis 明细下钻"),
    NF_DETAIL("NF_DETAIL", "nf 明细下钻"),
    VPN_DETAIL("VPN_DETAIL", "VPN分析 明细下钻"),
    VIRTUAL_ACCOUNT_DETAIL("VIRTUAL_ACCOUNT_DETAIL", "虚拟账号明细下钻"),
    RADIUS_EXPANSION_DETAIL("RADIUS_EXPANSION_DETAIL", "RADIUS扩线 明细下钻"),
    PHONE_EXPANSION_DETAIL("PHONE_EXPANSION_DETAIL", "号码扩线 明细下钻"),
    ATTACH_SOURCE_DETAIL("ATTACH_SOURCE_DETAIL", "附件来源 明细数据"),
    PHONE_EXTRACT_SOURCE_DETAIL("PHONE_EXTRACT_SOURCE_DETAIL", "号码提取来源"),
    WEBSITE_ARC_VISITOR_DETAIL("WEBSITE_ARC_VISITOR_DETAIL", "网站档案访问者下钻"),
    APP_ARC_VISITOR_DETAIL("APP_ARC_VISITOR_DETAIL", "应用档案访问者下钻"),
    AUTHENTICATION_BILLING("AUTHENTICATION_BILLING","鉴权计费明细下钻");


    private final String code;

    private final String value;

    DrillDownSceneEnum(String key, String value) {
        this.code = key;
        this.value = value;
    }

    public static String getByKey(String key) {
        DrillDownSceneEnum[] values = DrillDownSceneEnum.values();
        for (DrillDownSceneEnum value : values) {
            if (value.getCode().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }

    public static DrillDownSceneEnum getEnumByKey(String key) {
        DrillDownSceneEnum[] values = DrillDownSceneEnum.values();
        for (DrillDownSceneEnum value : values) {
            if (value.getCode().equals(key)) {
               return value;
            }
        }
        return null;
    }

    public static void main(String[] args) {
        DrillDownSceneEnum enumByKey = DrillDownSceneEnum.getEnumByKey("APP_ARC_VISITOR_DETAIL");
        System.out.println(enumByKey);
    }
}
