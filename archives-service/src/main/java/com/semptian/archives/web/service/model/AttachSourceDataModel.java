package com.semptian.archives.web.service.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 附件来源数据模型
 *
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
@NoArgsConstructor
public class AttachSourceDataModel {

    /**
     * 认证账号
     */
    private String authAccount;

    /**
     * 虚拟账号
     */
    private String virtualAccount;

    /**
     * 捕获时间
     */
    private String captureTime;

    /**
     * 原始明细数据
     */
    private List<DetailEntity> detail;

    @Data
    public static class DetailEntity {
        /**
         * 字段名
         */
        private String field;

        /**
         * 字段国际化描述
         */
        private String desc;

        /**
         * 字段值
         */
        private Object value;
    }
}





