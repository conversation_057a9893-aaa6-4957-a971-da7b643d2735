package com.semptian.archives.web.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.archives.web.dao.archive.entity.ArcConnectionTypeEntity;

/**
 * 档案关系服务
 * <AUTHOR>
 * @date 2024/3/26
 */
public interface ArcConnectionTypeService extends IService<ArcConnectionTypeEntity> {

    void updateConnectionTypeCache(String srcArcId, String destArcId, String connectionType);

    String getArcConnectionTypeByArcId(String srcArcId, String destArcId);

}
