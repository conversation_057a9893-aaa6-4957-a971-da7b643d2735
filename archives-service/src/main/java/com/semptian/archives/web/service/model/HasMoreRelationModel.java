package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/4/2 21:29
 **/
@Data
public class HasMoreRelationModel {

    private String srcAccount;

    private String arcType;

    /**
     * 关系类型：0 全部； 1 同事; 2 朋友; 3 亲人； 4 夫妻 5其他
     */
    @ApiModelProperty(notes = "关系类型：0 全部； 1 同事; 2 朋友; 3 亲人； 4 夫妻 5 其他")
    private String connectType = "0";

    /**
     * 扩线规则：0:全部 1:传真 2:通话 3:短信 4:EMAIL, 多个用逗号分隔
     */
    @ApiModelProperty(notes = "扩线规则：0:全部 1:传真 2:通话 3:短信 , 多个用逗号分隔")
    private String expansionRule = "0";

    /**
     * 最小通联次数
     */
    @ApiModelProperty(notes = "最小通联次数")
    private int minLinkCount = 1;

    private ArrayList<String> account;

    private DateModel dateModel;
}
