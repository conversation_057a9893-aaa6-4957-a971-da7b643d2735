package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.meiya.whalex.util.JsonUtil;
import com.semptian.archives.web.core.common.enums.ArcTypeEnum;
import com.semptian.archives.web.core.common.enums.SortFieldEnum;
import com.semptian.archives.web.dao.archive.entity.ArcCollectionEntity;
import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.archives.web.service.common.enums.ArcQueryEsConditionEnum;
import com.semptian.archives.web.service.common.enums.ArcStaticTypeEnum;
import com.semptian.archives.web.service.fegin.PermissionApiClient;
import com.semptian.archives.web.service.model.ArcInfoModel;
import com.semptian.archives.web.service.service.ArcCollectionService;
import com.semptian.archives.web.service.service.ArcIndexService;
import com.semptian.base.builders.BoolQueryBuilder;
import com.semptian.base.builders.QueryBuilder;
import com.semptian.base.builders.QueryBuilders;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.service.elasticsearch.SearchOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 档案统计相关的接口
 * <AUTHOR>
 * @date 2024/01/10
 */
@Slf4j
@Service
public class ArcIndexServiceImpl implements ArcIndexService {


    @Resource
    SearchOperation searchOperation;

    @Resource
    @Lazy
    private PermissionApiClient permissionFeign;

    @Resource
    private ArcCommonServiceImpl arcCommonService;

    @Resource
    private ArchivesInfoServiceImpl archivesInfoService;

    @Resource
    private ArcCollectionService arcCollectionService;

    @Value("${archive.index.last60.days:59}")
    private Integer arcIndexLast60Days;

    @Value("${archive.index.last30.days:29}")
    private Integer arcIndexLast30Days;

    @Override
    @SuppressWarnings({"unchecked"})
    public Map<String, Long> statisticArcAmount(List<Integer> permissionList, String userId) {
        Map<String, Long> amountMap = new HashMap<>(8);

        for (ArcStaticTypeEnum value : ArcStaticTypeEnum.values()) {
            Map<String, Object> countMap = statisticArcAmountByType(value.getType(), permissionList, userId);
            Map<String, Long> totalAllMap = (Map<String, Long>) countMap.get("total");

            amountMap.put(value.getType(), totalAllMap.get("all"));
        }
        return amountMap;
    }

    @Override
    public Map<String, Object> statisticArcAmountByType(String staticType, List<Integer> permissionList, String userId) {
        List<String> arcTypes = permissionList.stream().map(id -> id + "").collect(Collectors.toList());
        return getArcCountByType(staticType, arcTypes, permissionList, userId);
    }

    /**
     * 按类型统计档案数量
     * @param staticType 统计类型 默认值为total。total=全部，active=近30天活跃，loseActive=近30天失活，new=近30天新增
     * @param arcTypes 档案类型
     * @return 档案数量
     */
    public Map<String, Object> getArcCountByType(String staticType, List<String> arcTypes, List<Integer> permissionList, String userId) {
        Map<String, Object> countAllMap = new HashMap<>();
        long allTotalAll = 0L;
        long careTotalAll = 0L;

        //统计时间：最近30天、最近60天-最近30天
        long past30Days = DateUtil.beginOfDay(new Date()).offset(DateField.DAY_OF_YEAR, -arcIndexLast30Days).getTime();
        long past60Days = DateUtil.beginOfDay(new Date()).offset(DateField.DAY_OF_YEAR, -arcIndexLast60Days).getTime();
        long past30DaysEnd = DateUtil.beginOfDay(new Date()).offset(DateField.DAY_OF_YEAR, -arcIndexLast30Days).getTime();

        ArcStaticTypeEnum staticTypeByType = ArcStaticTypeEnum.getStaticTypeByType(staticType);

        //根据统计类型拼接dsl
        String timeCondition = "";
        switch (staticTypeByType) {
            case ACTIVE:
                timeCondition = "AND latest_relation_time:[" + past30Days + " TO *]";
                break;
            case LOSE_ACTIVE:
                timeCondition = "AND latest_relation_time:[" + past60Days + " TO " + past30DaysEnd + "]";
              break;
            case NEW:
                timeCondition = "AND create_time:[" + past30Days + " TO *]";
                break;
            default: // 统计总数
                timeCondition = "AND latest_relation_time:>0";
                break;
        }

        List<String> allCareArcIds = arcCollectionService.queryCareArcWithArcType(Long.parseLong(userId),StringUtils.join(permissionList, ",") )
                .stream().map(ArcCollectionEntity::getArchiveId).collect(Collectors.toList());

        for (String arcType : arcTypes) {
            String appTypeCondition = "";
            if (ArcTypeEnum.APP.getKey().equals(arcType)) {
                appTypeCondition = "AND archive_type: 4";
            } else if (ArcTypeEnum.NET_PROTOCOL.getKey().equals(arcType)) {
                appTypeCondition = "AND archive_type: 7";
            }

            String careIdQuery = "";
            if(CollectionUtils.isNotEmpty(allCareArcIds)){
                careIdQuery = " AND id: (" + StringUtils.join(allCareArcIds, " OR ") + ") ";
            }

            String allDsl = "{\"query\":{\"query_string\":{\"query\":\"archive_name:* " + appTypeCondition + " " + timeCondition  + "\"}},\"track_total_hits\":true}";
            String careDsl = "{\"query\":{\"query_string\":{\"query\":\"archive_name:* " + appTypeCondition + " " + timeCondition + careIdQuery + "\"}},\"track_total_hits\":true}";

            List<String> arcIndex = arcCommonService.getEsResourceNameByArcTypeAndPermission(arcType, permissionList);

            if (arcIndex == null || arcIndex.isEmpty()) {
                continue;
            }
            Long allCount = searchOperation.count(JsonUtil.jsonStrToMap(allDsl), arcIndex.get(0));
            Map<String, Long> valueMap = new HashMap<>();
            valueMap.put("all", allCount);
            //收藏档案统计
            if(CollectionUtils.isNotEmpty(allCareArcIds)){
                Long careCount = searchOperation.count(JsonUtil.jsonStrToMap(careDsl), arcIndex.get(0));
                valueMap.put("care", careCount);
                careTotalAll += careCount;
            }
            countAllMap.put(ArcTypeEnum.getByKey(arcType).replaceAll("_", " "), valueMap);
            allTotalAll += allCount;
        }

        //统计全部档案数量(通过计算各个档案数量再求和保证总数一致性)
        Map<String, Long> totalAllMap = new HashMap<>();
        totalAllMap.put("all", allTotalAll);
        totalAllMap.put("care", careTotalAll);

        countAllMap.put("total", totalAllMap);
        return countAllMap;
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<Integer> getUserArchivePermission(String appId, String userId) {
        List<Integer> userPermissionList = new ArrayList<>();
        try {
            ReturnModel userServicePermission = permissionFeign.getUserServicePermission(appId, "1", userId);
            if (userServicePermission == null || userServicePermission.getCode() != 1) {
                log.error("call deye-permission-center get user arc permission failed,the userId is{}", userId);
                return null;
            }
            Object data = userServicePermission.getData();
            if (data == null) {
                log.error("call deye-permission-center get data is null");
                return userPermissionList;
            }
            Map dataMap = JSON.parseObject(JSON.toJSONString(data), Map.class);
            if (dataMap == null || dataMap.isEmpty()) {
                log.error("call deye-permission-center get data is null");
                return userPermissionList;
            }

            List<Map> permissionMapList = JSONArray.parseArray(JSON.toJSONString(dataMap.get("permissionList")), Map.class);
            if (CollUtil.isNotEmpty(permissionMapList)) {
                List<String> permissionNameList = permissionMapList.stream().map(map -> {
                    Object name = map.get("name");
                    List<Integer> optionCode = (List<Integer>) map.get("permissionOptionCodes");
                    if (name != null && optionCode.contains(1)) {
                        return name.toString().toLowerCase();
                    }
                    return "";
                }).collect(Collectors.toList());
                for (String name : permissionNameList) {
                    if (StringUtils.isNotBlank(ArcTypeEnum.getByValue(name))) {
                        userPermissionList.add(Integer.parseInt(ArcTypeEnum.getByValue(name)));
                    }

                }
            }
        } catch (Exception e) {
            log.error("get permission failed ", e);
            return userPermissionList;
        }

        return userPermissionList;
    }

    @Override
    public List<ArcInfoModel> allActiveArcTop10(Integer arcType, List<Integer> permissionList, String lang) {
        //按活跃次数排序取前10
        Map<String, String> sortFields = SortFieldEnum.getSortFieldMapByValueAndSortType("activeRate", 1);

        return queryArcTop(arcType, permissionList, QueryBuilders.queryStringQuery("archive_name:*"), sortFields, lang);
    }

    @Override
    public List<ArcInfoModel> allLostActiveArcTop10(Integer arcType, List<Integer> permissionList, String lang) {
        //最近失活top10，取最近活跃时间在30-60天之间的档案
        long past60Days = DateUtil.beginOfDay(new Date()).offset(DateField.DAY_OF_YEAR, -arcIndexLast60Days).getTime();
        long past30DaysEnd = DateUtil.beginOfDay(new Date()).offset(DateField.DAY_OF_YEAR, -arcIndexLast30Days).getTime();

        Map<String, String> sortFields = SortFieldEnum.getSortFieldMapByValueAndSortType("latestRelationTime", 1);

        BoolQueryBuilder totalBoolBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder queryStringBuilder = QueryBuilders.boolQuery().must(QueryBuilders.queryStringQuery("archive_name:*"));
        totalBoolBuilder.must(queryStringBuilder);

        QueryBuilder latestRelationTimeRange = QueryBuilders.rangeQuery(ArcQueryEsConditionEnum.LAST_RELATION_TIME.getFieldName()).gte(past60Days).lte(past30DaysEnd);
        totalBoolBuilder.filter(latestRelationTimeRange);

        return queryArcTop(arcType, permissionList, totalBoolBuilder, sortFields, lang);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private List<ArcInfoModel> queryArcTop(Integer arcType, List<Integer> permissionList, QueryBuilder queryBuilder, Map<String, String> sortFields, String lang) {
        Map<String, Object> esResultMap = arcCommonService.query(1, 10, queryBuilder, sortFields, arcType, permissionList);

        if (MapUtil.isEmpty(esResultMap)) {
            log.error("query es failed");
            return Lists.newArrayList();
        }

        List<ArcEsEntity> arcInfoList = (List) esResultMap.get("data");
        if (CollUtil.isEmpty(arcInfoList)) {
            return Lists.newArrayList();
        }

        return arcInfoList.stream().map(arcEsEntity -> archivesInfoService.arcEsEntity2ArcInfoModel(arcEsEntity, false, lang)).collect(Collectors.toList());
    }
}
