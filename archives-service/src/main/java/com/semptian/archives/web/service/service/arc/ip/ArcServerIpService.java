package com.semptian.archives.web.service.service.arc.ip;

import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.ArcFeatures;
import com.semptian.archives.web.service.service.arc.ArcFunctional;

import java.util.Map;

/**
 * 档案服务器IP服务接口
 * */
public interface ArcServerIpService extends ArcFunctional {

    @Override
    default ArcFeatures feature() {
        return ArcFeatures.SERVER_IP;
    }

    /**
     * 获取服务器IP列表
     *
     * @param ctx 查询上下文
     * @return 服务器IP列表
     */
    Map<String, Object> getServerIpList(ArcContext ctx);
}
