package com.semptian.archives.web.service.service.impl;

import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.dao.archive.entity.SlowQueryLog;
import com.semptian.archives.web.dao.archive.mapper.SlowQueryLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

/**
 * <AUTHOR>
 * @date 2021/6/2 18:03
 **/
@Service
@Slf4j
public class ComposeQueryService {


    @Resource
    DorisQueryServiceImpl queryService;

    @Value("${archive.slowSql.limit:10000}")
    private Long slowSqlLimit;

    @Resource
    SlowQueryLogMapper slowQueryLogMapper;

    public void setSlowSqlLimit(Long slowSqlLimit) {
        this.slowSqlLimit = slowSqlLimit;
    }

    /**
     *
     * @param sql
     * @return
     */
    public List<Map<String, Object>> queryForList(String sql) {
        sql = fillTemplateByParams(sql, null, true);
        long start = System.currentTimeMillis();
        List<Map<String, Object>> result = queryService.queryForList(sql);
        queryLog(sql,  "", (System.currentTimeMillis()-start));
        return result;
    }

    public List<Map<String, Object>> queryForList(String sql, String serviceCode) {
        sql = fillTemplateByParams(sql, null, true);
        long start = System.currentTimeMillis();
        List<Map<String, Object>> result = queryService.queryForList(sql);
        queryLog(sql,  serviceCode, (System.currentTimeMillis()-start));
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    /**
     *
     * @param sql
     * @param params 可为空
     * @param serviceCode 可为空
     * @return
     */
    public List<Map<String, Object>> queryForList(String sql, Map<String, Object> params, String serviceCode) {
        sql = fillTemplateByParams(sql, params, true);
        long start = System.currentTimeMillis();
        List<Map<String, Object>> result = queryService.queryForList(sql);
        queryLog(sql,  serviceCode, (System.currentTimeMillis()-start));
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    /**
     * @param sql
     * @param params
     * @return
     */
    public List<Map<String, Object>> queryForList(String sql, Map<String, Object> params, String serviceCode, Boolean needSqlEscape) {
        sql = fillTemplateByParams(sql, params, needSqlEscape);
        long start = System.currentTimeMillis();
        List<Map<String, Object>> result = queryService.queryForList(sql);
        queryLog(sql,  serviceCode, (System.currentTimeMillis()-start));
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    public void queryLog(String sql, String serviceCode, Long cost) {
        String description = BusinessCodeEnum.getDescriptionByCode(serviceCode);

        if (StringUtils.isBlank(description)) {
            description = serviceCode;
        }

        log.info("查询业务码: {}, 耗时: {} 毫秒 SQL: \r\n [{}]", description, cost, sql);
        if (cost >= slowSqlLimit) {
            try {
                log.warn("发现慢sql, 业务码 {}, 耗时 {} 毫秒, 配置阈值 {}, sql \r\n [{}]", serviceCode, cost, slowSqlLimit, sql);
                SlowQueryLog slowQueryLog = new SlowQueryLog();
                slowQueryLog.setCost(cost);
                slowQueryLog.setServiceCode(serviceCode);
                slowQueryLog.setInsertTime(new Date());
                slowQueryLog.setSql(sql.replaceAll("\\r", " ").replaceAll("\\n", " ").replaceAll("\\t", " ").replaceAll("'", "\""));
                slowQueryLogMapper.insertData(slowQueryLog);
            } catch (Exception e) {
                log.error("慢日志记录出错 ", e);
            }
        }
    }

    /**
     * @param sql
     * @param params
     * @return
     */
    public Long queryForCount(String sql, Map<String, Object> params, String serviceCode, boolean needSqlEscape) {
        sql = fillTemplateByParams(sql, params, needSqlEscape);
        Long count;
        long start = System.currentTimeMillis();
        count = queryService.queryForCount(sql);
        queryLog(sql, serviceCode, (System.currentTimeMillis()-start));
        return count;
    }

    public Long queryForCount(String sql, Map<String, Object> params, String serviceCode) {
        return queryForCount(sql, params, serviceCode, false);
    }

    /**
     * 查询统计求和
     * @param sql sql
     * @param params 参数
     * @return 求和结果
     */
    public Double queryForSum(String sql, Map<String, Object> params, String serviceCode) {
        sql = fillTemplateByParams(sql, params, false);
        Double total;
        long start = System.currentTimeMillis();
        total = queryService.queryForSum(sql);
        queryLog(sql, serviceCode, (System.currentTimeMillis()-start));
        return total;
    }

    /**
     * sql 参数替换方法, 用于替换sql中的${}和{}中的参数
     * 1 推荐使用 {}, 可根据值类型自动拼接引号，使用 ${} 则不会拼接引号
     * TODO 后续扩展 #{}, 进行预编译参数渲染（解决特殊符号转义问题）
     * @param sqlTemplate
     * @param params
     * @param needSqlEscape
     * @return
     */
    public static String fillTemplateByParams(String sqlTemplate, Map<String, Object> params, Boolean needSqlEscape) {
        if (StringUtils.isBlank(sqlTemplate)) {
            return null;
        }
        if (params == null || params.keySet().isEmpty()) {
            return sqlTemplate;
        }
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (StringUtils.isNotBlank(key) && value != null) {
                if (key.contains("List") || key.contains("list")) {
                    List<Object> valueList = (List) value;
                    value = listToCondition(valueList);
                }
                if (value != null) {
                    //解决因value中含有$使用replaceAll报 no group 8 或 illegal group reference的问题
                    String valueStr = Matcher.quoteReplacement(value.toString());
                    if (value instanceof String) {
                        if (StringUtils.isNotBlank(valueStr)) {
                            sqlTemplate = sqlTemplate.replaceAll("\\$\\{" + key + "\\}", valueStr);
                            //转义单引号
                            if (needSqlEscape && valueStr.contains("'")) {
                                valueStr = ((String) valueStr).replace("'", "''");
                            }
                            sqlTemplate = sqlTemplate.replaceAll("\\{" + key + "\\}", "'" + valueStr + "'");
                        } else {
                            sqlTemplate = sqlTemplate.replaceAll("\\$\\{" + key + "\\}", valueStr);
                            sqlTemplate = sqlTemplate.replaceAll("\\{" + key + "\\}", " ");
                        }
                    } else {
                        sqlTemplate = sqlTemplate.replaceAll("\\$\\{" + key + "\\}", valueStr);
                        sqlTemplate = sqlTemplate.replaceAll("\\{" + key + "\\}", valueStr);
                    }
                }
            }
        }
        return sqlTemplate;
    }

    public static String listToCondition(List<Object> paramList) {
        if (paramList != null && paramList.size() > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(");
            for (int i = 0; i < paramList.size(); i++) {
                Object value = paramList.get(i);
                String temp;
                if (i != paramList.size() - 1) {
                    if (value instanceof String) {
                        temp = "'" + value + "'" + ",";
                    } else {
                        temp = value + ",";
                    }
                } else {
                    if (value instanceof String) {
                        temp = "'" + value + "'";
                    } else {
                        temp = value + "";
                    }
                }
                stringBuilder.append(temp);
            }
            stringBuilder.append(")");
            return stringBuilder.toString();
        }
        return null;
    }


}
