package com.semptian.archives.web.service.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description 关系扩线结果返回
 * <AUTHOR>
 * @Date 2021/6/15
 */

@Data
@NoArgsConstructor
public class RelationExpansionResultModel implements Serializable {

    /**
     * 节点信息
     */
    private List<RelationNodeModel> nodes = new ArrayList<>();

    /**
     * 关系信息
     */
    private List<RelationLinkModel> links = new ArrayList<>();

    /**
     * 目标账户总数
      */
    private Long total=0L;

    /**
     * 是否可能存在更多的关系，true：可能存在更多关系，false：没有更多关系
     */
    private boolean more;
}
