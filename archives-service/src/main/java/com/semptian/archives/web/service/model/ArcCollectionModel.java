package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/4 15:47
 **/
@ApiModel(value = "档案收藏传参实体类")
@Data
public class ArcCollectionModel {

    @ApiModelProperty(value = "档案id")
    String arcId;

    @ApiModelProperty(value = "档案类型")
    Integer arcType;

    @ApiModelProperty(value = "是否关注：0=否，1=是")
    Integer isCare;
}
