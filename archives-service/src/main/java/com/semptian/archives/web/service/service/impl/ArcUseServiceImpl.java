package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.archives.web.dao.archive.entity.ArcUseEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcUseMapper;
import com.semptian.archives.web.service.common.util.PermissionUtil;
import com.semptian.archives.web.service.service.ArcUseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ArcUseServiceImpl extends ServiceImpl<ArcUseMapper, ArcUseEntity> implements ArcUseService {

    @Override
    public void updateArcUser(Long userId, String arcId, Integer arcType, String arcAccount, String arcAccountType, String dataType) {
        ArcUseEntity queryEntity = new ArcUseEntity();
        QueryWrapper<ArcUseEntity> entityWrapper = new QueryWrapper<>();
        entityWrapper.eq("user_id", userId).eq("arc_id", arcId);
        ArcUseEntity arcUseEntity = baseMapper.selectOne(entityWrapper);
        if (arcUseEntity == null) {
            queryEntity.setUserId(userId);
            queryEntity.setArcId(arcId);
            queryEntity.setArcType(arcType);
            queryEntity.setArcAccount(arcAccount);
            queryEntity.setArcAccountType(arcAccountType);
            queryEntity.setDataType(dataType);
            queryEntity.setUseNumber(1);
            queryEntity.setCreateTime(System.currentTimeMillis());
            queryEntity.setModifyTime(System.currentTimeMillis());
            baseMapper.insert(queryEntity);
        } else {
            arcUseEntity.setUseNumber(arcUseEntity.getUseNumber() + 1);
            arcUseEntity.setModifyTime(System.currentTimeMillis());
            baseMapper.updateById(arcUseEntity);
        }
    }

    @Override
    public List<ArcUseEntity> latestUseArcTop10(Long userId, Integer arcType, List<Integer> permissionList) {

        if (arcType != 0) {
            return baseMapper.latestUseArcTop10(userId, arcType);
        }
        boolean allPermission = PermissionUtil.hasAllPermission(permissionList);
        if (allPermission) {
            return baseMapper.latestUseArcTopByType(userId,null);
        } else {
            return baseMapper.latestUseArcTopByType(userId, StringUtils.join(permissionList, ","));
        }
    }

    @Override
    public List<ArcUseEntity> frequencyUseArcTop10(Long userId, Integer arcType, List<Integer> permissionList) {
        if (arcType != 0) {
            return baseMapper.frequencyUseArcTop10(userId, arcType);
        }
        boolean allPermission = PermissionUtil.hasAllPermission(permissionList);
        if (allPermission) {
            return baseMapper.frequencyUseArcTop10ByType(userId, null);
        } else {
            return baseMapper.frequencyUseArcTop10ByType(userId, StringUtils.join(permissionList, ","));
        }
    }
}
