package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.ArchiveModel;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ProtocolArcHomePageService extends AbstractArcHomePageService {

    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.NET_PROTOCOL;
    }

    @Override
    protected Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("appName", authAccounts);
        params.put("app_type", PROTOCOL_APP_TYPE);

        return params;
    }

    @Override
    protected Map<String, String> buildArcInfoMap(List<ArchiveModel> archiveModels) {
        return archiveModels.stream().collect(Collectors.toMap(arc -> arc.getArcAccount() + arc.getArcAccountType(), ArchiveModel::getArcId));
    }

    @Override
    protected Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        return new HashMap<>(DateUtils.getPast7Day());
    }

    @Override
    public void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.put("app_name", EscapeUtil.escapeSingleQuote(ctx.getAppName()));
        params.put("app_type", PROTOCOL_APP_TYPE);
        //处理keyWord
        params.put("keyWord"," ");
        params.put("data_type_condition", "");
    }

    @Override
    public String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        switch (accessTargetTopEnum) {
            case ARC:
                return BusinessCodeEnum.APP_ARC_TARGET_TOP.getValue();
            case IP:
                return BusinessCodeEnum.APP_IP_TARGET_TOP.getValue();
            case IMPORTANT:
                return BusinessCodeEnum.APP_IMPORTANT_TARGET_TOP.getValue();
        }
        return null;
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        String arcAccountType = ctx.getArcAccountType();
        if (StringUtils.isNotBlank(arcAccountType)) {
            params.put("arc_account_type", "AND app_type = " + "'" + PROTOCOL_APP_TYPE + "'");
        } else {
            params.put("arc_account_type", "");
        }
    }

    @Override
    public String activeTrendTemplate(ArcContext ctx) {
        return BusinessCodeEnum.COMMON_APP_DAY_TREND_ANALYZE.getValue();
    }

    @Override
    public String periodStatisticsTemplate() {
        return BusinessCodeEnum.COMMON_APP_DAY_HOUR_ANALYZE.getValue();
    }

    @Override
    public String communicationAreaTemplate() {
        return BusinessCodeEnum.COMMON_APP_DAY_COMMUNICATION_AREA.getValue();
    }
}
