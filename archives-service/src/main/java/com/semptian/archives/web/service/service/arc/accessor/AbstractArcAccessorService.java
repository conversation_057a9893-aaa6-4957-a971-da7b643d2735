package com.semptian.archives.web.service.service.arc.accessor;

import cn.hutool.core.collection.CollectionUtil;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.core.common.util.ArcIdUtil;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.SortTypeEnum;
import com.semptian.archives.web.service.common.util.ArcCommonUtils;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.fegin.BasicFeign;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.ImportantTargetModel;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.base.service.ReturnModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public abstract class AbstractArcAccessorService implements ArcAccessorService {

    private static final Logger log = LoggerFactory.getLogger(AbstractArcAccessorService.class);
    @Resource
    private ArcCommonService arcCommonService;

    @Autowired
    private BasicFeign basicFeign;

    @Override
    public Map<String, Object> getAccessTargetList(ArcContext ctx) {
        //时间参数
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam(ctx.getDateModel());

        // 构建捕获月参数
        // 如果命中月物化视图则使用yyyyMM的开始时间和结束时间构建基于capture_month的范围查询条件，否则使用yyyy-MM-dd的开始时间和结束时间构建基于capture_day的范围查询条件
        String timeCondition = "";
        if (hitMonthMaterializedView(ctx.getDateModel())) {
            // 使用DateUtils将yyyy-MM-dd格式的时间转换为yyyyMM格式
            int startMonth = Integer.parseInt(ctx.getDateModel().getStartDay().replaceAll("-", "").substring(0, 6));
            int endMonth = Integer.parseInt(ctx.getDateModel().getEndDay().replaceAll("-", "").substring(0, 6));

            timeCondition = " capture_month >= " + startMonth + " and capture_month <= " + endMonth;
        } else {
            timeCondition = " capture_day >= '" + ctx.getDateModel().getStartDay() + "' and capture_day <= '" + ctx.getDateModel().getEndDay() + "' ";
        }
        params.put("timeCondition", timeCondition);

        if (hitMonthMaterializedView(ctx.getDateModel())) {

        }

        //构造档案特定的参数
        buildParams(params, ctx);

        //构造关键字搜索参数
        buildKeywordParams(params, ctx);

        //排序字段
        String orderType = SortTypeEnum.getNameByCode(ctx.getSortType());
        params.put(CommonConstent.ORDER_TYPE, orderType);
        params.put(CommonConstent.ORDER_FIELD, ctx.getSortField() + " " + orderType + ", arcAccount ");

        //获取doris sql模板
        String accessTargetListTemplate = accessTargetListTemplate(ctx);

        Long total = arcCommonService.getCommonServiceCountResult(accessTargetListTemplate, params, true);
        List<Map<String, Object>> list = new ArrayList<>();
        if (total > 0) {
            list = arcCommonService.getCommonServiceListResult(accessTargetListTemplate, params, true, ctx.getOnPage(), ctx.getSize());
        }

        if (CollectionUtil.isNotEmpty(list)) {
            for (Map<String, Object> data : list) {
                Object arcTypeObj = data.get("arcType");
                String arcAccount = data.getOrDefault("arcAccount", "").toString();
                String arcId = "";

                if (arcTypeObj != null) {
                    if (ArcTypeEnum.PHONE.getKey().equals(arcTypeObj.toString())) {
                        arcId = ArcIdUtil.getPhoneId(arcAccount);
                    } else if (ArcTypeEnum.RADIUS.getKey().equals(arcTypeObj.toString())) {
                        arcId = ArcIdUtil.getAuthAccountId("1020001",arcAccount);
                    } else if (ArcTypeEnum.FIXED_IP.getKey().equals(arcTypeObj.toString())) {
                        arcId = ArcIdUtil.getAuthAccountId("1029997",arcAccount);
                    }

                    data.put("arcId", arcId);
                }
            }
        }

        // 对重要目标的类型进行翻译
        if (ctx.getVisitorType() == 3 && !list.isEmpty()) {
            List<ImportantTargetModel> importantTargetModelList = list.stream().map(map -> {
                ImportantTargetModel model = new ImportantTargetModel();
                model.setImportantTarget((String) map.getOrDefault("arcAccount", ""));
                model.setImportantTargetType((String) map.getOrDefault("important_target_type", ""));

                return model;
            }).collect(Collectors.toList());
            try {
                ReturnModel<List<ImportantTargetModel>> importantTargetCategoryByBatch = basicFeign.getImportantTargetCategoryByBatch(importantTargetModelList);
                if (importantTargetCategoryByBatch != null && importantTargetCategoryByBatch.getData()!= null && !importantTargetCategoryByBatch.getData().isEmpty()) {

                    List<ImportantTargetModel> data = importantTargetCategoryByBatch.getData();

                    list.forEach(map -> {
                        String arcAccount = (String) map.getOrDefault("arcAccount", "");
                        String importantTargetType = (String) map.getOrDefault("important_target_type", "");

                        for (ImportantTargetModel m : data) {
                            if (arcAccount.equals(m.getImportantTarget()) && importantTargetType.equals(m.getImportantTargetType())) {
                                map.put("importantTargetCategory", m.getImportantTargetCategory());
                                map.put("fullArcType", ArcCommonUtils.listToDirectoryPath(m.getImportantTargetCategoryList()));
                            }
                        }
                    });
                }
            } catch (Exception e) {
                log.error("Query import target category error", e);
            }

        }
        HashMap<String, Object> result = new HashMap<>(2);
        result.put("list", list);
        result.put("total", total);

        return result;
    }


    /**
     * 判断给定的日期模型是否命中了月度视图。
     * 命中的场景：
     * 1. 时间选项为“月度”；
     * 2. 开始日期是某一个月的第一天，并且结束日期是当天。
     *
     * @param dateModel 日期模型，包含开始日期和结束日期，以及日期选项。
     * @return 返回一个布尔值，如果命中了月度视图，则返回true；否则返回false。
     */
    protected boolean hitMonthMaterializedView(DateModel dateModel) {
        return dateModel.getDateOption() == 3 || dateModel.getDateOption() == 4 || dateModel.getDateOption() == 5
                || (DateUtils.isFirstDayOfMonth(dateModel.getStartDay()) && DateUtils.isToday(dateModel.getEndDay()));
    }

    protected void buildParams(Map<String, Object> params, ArcContext ctx) {
        throw new UnsupportedOperationException(arcType() + " not support build params method");
    }

    protected void buildKeywordParams(Map<String, Object> params, ArcContext ctx) {

        params.put("keyWord", buildKeywordCondition(ctx));
    }

    abstract String buildKeywordCondition(ArcContext ctx);

    protected String accessTargetListTemplate(ArcContext ctx) {
        throw new UnsupportedOperationException(arcType() + " not support doris sql template name query method");
    }
}
