package com.semptian.archives.web.service.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 公共配置信息
 *
 * <AUTHOR>
 * @date 2021/06/09
 */
@Configuration
@ConfigurationProperties("archive.phone-operator")
@Data
public class PhoneOperatorConfig {

    private String mOperator;

    private String mOperatorHis;

    private String oOperator;

    private String oOperatorHis;

    private String dOperator;

    private String dOperatorHis;
}
