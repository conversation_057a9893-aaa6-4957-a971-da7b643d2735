package com.semptian.archives.web.service.fegin;

import com.semptian.archives.web.service.model.ImportantTargetModel;
import com.semptian.base.service.ReturnModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 基础库feign
 *
 * <AUTHOR> Hu
 * @date 2024/3/28
 */
@FeignClient(name = "deye-basic-library", path = "/basic_library")
public interface BasicFeign {

    @GetMapping("/user/is_important_target.json")
    ReturnModel<Map<String, Object>> isImportantTarget(@RequestParam(name = "arcType") Integer arcType, @RequestParam(name = "arcAccount") String arcAccount);

    @PostMapping("/user/get_important_target_category_by_batch.json")
    ReturnModel<List<ImportantTargetModel>> getImportantTargetCategoryByBatch(@RequestBody List<ImportantTargetModel> importantTargetModelList);
}
