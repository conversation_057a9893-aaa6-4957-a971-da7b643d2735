package com.semptian.archives.web.service.common.enums;

/**
 * <AUTHOR>
 * @date 2024/3/29 10:58
 * Description: 数据源种类（LIS或者NF）
 */
public enum DataSourceEnum {

    LIS("LIS", "LIS"), NF("NF", "NF");

    private final String key;

    private final String value;

    DataSourceEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        DataSourceEnum[] values = DataSourceEnum.values();
        for (DataSourceEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }
}
