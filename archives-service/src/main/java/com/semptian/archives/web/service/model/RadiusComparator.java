package com.semptian.archives.web.service.model;

import java.util.Comparator;

public class RadiusComparator implements Comparator {

    public static final String PARAMALL = "appName,appType,virtualAccount,authAccount,value,strsrcIp,strdstIp,dstCountry,targetAccount,num,phone,registeredAddress,earliestRelationTime" +
            "lastRelationTime,active_day_num,activeNum,arcAccount,arcType,srcCountry,nfNum,prNum,earliestTime,latestTime";

    private String  keys;
    private Integer values;

    @Override
    public int compare(Object o1, Object o2) {
        String a=(String)o1;
        String b=(String)o2;
        RadiusComparator radiusComparator = new RadiusComparator(a);
        RadiusComparator radiusComparator1 = new RadiusComparator(b);
        if (radiusComparator.values>=radiusComparator1.values){
            return 1;
        }
        return -1;
    }

    public RadiusComparator(String key, Integer value) {
        this.keys = key;
        this.values = value;
    }

    public RadiusComparator() {
    }

    RadiusComparator(String key){
        this.keys = key;
        switch (key){
            case "appName":this.values=1;break;
            case "appType":this.values=2;break;
            case "virtualAccount":this.values=3;break;
            case "authAccount":this.values=4;break;
            case "value":this.values=5;break;
//            case "earliestTime":this.values=6;break;
//            case "latestTime":this.values=7;break;

            case "strsrcIp":this.values=6;break;
//            case "srcCountry":this.values=22;break;
            case "strdstIp":this.values=23;break;
            case "dstCountry":this.values=24;break;
            case "targetAccount":this.values=25;break;
//            case "earliestTime":this.values=11;break;
//            case "latestTime":this.values=12;break;
            case "num":this.values=30;break;

            case "phone":this.values=14;break;
            case "registeredAddress":this.values=15;break;
            case "earliestRelationTime":this.values=16;break;
            case "lastRelationTime":this.values=17;break;
            case "active_day_num":this.values=18;break;
            case "activeNum":this.values=19;break;

            case "arcAccount":this.values=20;break;
            case "arcType":this.values=21;break;
            case "srcCountry":this.values=22;break;
            case "nfNum":this.values=26;break;
            case "prNum":this.values=27;break;
            case "earliestTime":this.values=28;break;
            case "latestTime":this.values=29;break;

            default:
                this.values=31;break;
        }
    }
}
