package com.semptian.archives.web.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 用来记录去重后的信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Group {
    public String title;
    public int count;

    /**
     * 计算一个数组去重后的数量和每个去重值的数量
     * @param data 数据源
     * @return 返回的列表的长度代表去重后的数量   Group的字段title，count分别代表去重后的值和数量
     */
    public static List<Group> find(List<String> data) {

        //用来记录运算后的数据
        Map<String,Group> resultMap = new LinkedHashMap<>();
        /**
         * 运算到的位置记录
         */
        int countIndex = 0;

        while (countIndex < data.size()) {
            String s = data.get(countIndex);
            //如果这个值运算过 不再运算
            if (resultMap.get(s) != null) {
                countIndex++;
                continue;
            }
            Group group = new Group();
            group.title = s;
            for (int i = countIndex; i < data.size(); i++) {
                if (data.get(i).equals(s)) {
                    group.count++;
                }
            }
            resultMap.put(s, group);
            countIndex++;
        }
        return new ArrayList<Group>(resultMap.values());

    }
}