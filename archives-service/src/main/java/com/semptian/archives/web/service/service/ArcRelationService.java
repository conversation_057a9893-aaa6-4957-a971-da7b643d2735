package com.semptian.archives.web.service.service;

import com.semptian.archives.web.service.model.AdsModel;
import com.semptian.archives.web.service.model.HasMoreRelationModel;
import com.semptian.archives.web.service.model.RelationExpansionRequestModel;
import com.semptian.archives.web.service.model.UserExpansionConfigModel;

/**
 * <AUTHOR>
 * @date 2024/3/26 14:36
 **/
public interface ArcRelationService  {

    Object relationExpansion(RelationExpansionRequestModel relationExpansionModel, String userId, String lang);

    Object hasMoreRelation(HasMoreRelationModel hasMoreRelationModel);

    Object saveUserExpansionConfig(UserExpansionConfigModel userExpansionConfig);

    Object getUserExpansionConfig(String userId);

}
