package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Service;

/**
 * 网站档案文件服务实现类
 *
 * <AUTHOR>
 * @date 2024/3/21
 */
@Service
public class DomainArcFileService extends AbstractArcFileService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.WEB_SITE;
    }

    @Override
    protected void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx) {
        params.put("account_type_condition", "domain");
        params.put("dataType", "");
    }
}
