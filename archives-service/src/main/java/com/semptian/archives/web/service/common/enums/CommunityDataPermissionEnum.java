package com.semptian.archives.web.service.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2022/5/20 11:52
 * 概要：
 * 群组数据权限枚举
 */
public enum CommunityDataPermissionEnum {
    CALL("call","5"),
    SMS("sms","3"),
    VOICE("voice","2"),
    FAX("fax","1"),
    VOIP("voip","6"),
    EMAIL("email","4");

    private String name;
    private String archiveCode;

    private static Map<String, Integer> permissionMap = new HashMap<>();

    private static Map<String,String> code2ProtocolMap = new HashMap<>();

    static {
        final CommunityDataPermissionEnum[] values = CommunityDataPermissionEnum.values();
        for (CommunityDataPermissionEnum value : values) {
            code2ProtocolMap.put(value.archiveCode,value.name);
        }
    }

    CommunityDataPermissionEnum(String name, String archiveCode) {
        this.name = name;
        this.archiveCode = archiveCode;
    }

    public static Map<String, Integer> getPermissionMap() {
        Map<String, Integer> permissionMap = new HashMap<>();
        final CommunityDataPermissionEnum[] values = CommunityDataPermissionEnum.values();
        for (CommunityDataPermissionEnum value : values) {
            permissionMap.put(value.getName(), 0);
        }
        permissionMap.remove("call");
        return permissionMap;
    }


    public String getName() {
        return name;
    }

    public static String archiveCode2Name(String protocolCode){
        return code2ProtocolMap.get(protocolCode);
    }


}
