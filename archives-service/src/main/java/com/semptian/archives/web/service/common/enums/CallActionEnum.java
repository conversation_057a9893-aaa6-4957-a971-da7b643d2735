package com.semptian.archives.web.service.common.enums;

/**
 * @author: SunQi
 * @create: 2021/07/30
 * desc: Call协议动作枚举类
 **/
public enum CallActionEnum {
    ON("01", "开机"),
    OFF("02", "关机"),
    LOCATION_UPDATE("03", "位置更新"),
    PERIODIC_UPDATE("04", "周期更新"),
    CALLING("05", "主叫"),
    CALLED("06", "被叫"),
    SEND_MESSAGE("07", "发短信"),
    RECEIVE_MESSAGE("08", "收短信"),
    SWITCH("09", "切换"),
    PAGED("10", "被寻呼"),
    ADDITIONAL_BUSINESS("11", "附加业务"),
    FAX("20", "传真");

    private String key;

    private String value;

    CallActionEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        CallActionEnum[] values = CallActionEnum.values();
        for (CallActionEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }
}
