package com.semptian.archives.web.service.service.impl;

import com.semptian.archives.web.service.service.DorisQueryService;
import com.semptian.base.template.TianHeDorisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Doris查询服务实现类
 * <AUTHOR>
 * @date 2024/1/4
 */
@Service
public class DorisQueryServiceImpl implements DorisQueryService {

    @Resource
    private TianHeDorisTemplate tianHeDorisTemplate;

    @Override
    public Long queryForCount(String sql) {
        return tianHeDorisTemplate.queryForCount(sql);
    }

    @Override
    public List<Map<String, Object>> queryForList(String sql) {
        return tianHeDorisTemplate.queryForList(sql);
    }

    @Override
    public Double queryForSum(String sql) {
        return tianHeDorisTemplate.queryForSum(sql);
    }
}
