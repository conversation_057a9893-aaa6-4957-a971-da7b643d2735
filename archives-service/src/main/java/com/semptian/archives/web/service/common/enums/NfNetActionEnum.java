package com.semptian.archives.web.service.common.enums;

/**
 * <AUTHOR>
 * @date 2024/4/3 11:29
 * Description: NF数据网络动作类型枚举
 */
public enum NfNetActionEnum {
    NF_URL_LOG(120110, "log", "nf_url_log"), NF_URL_REFUSE(120120, "reject", "nf_url_refuse"), NF_URL_WARN(120130, "alert", "nf_url_warn"), NF_URL_OTHER(120140, "other", "nf_url_other"),

    NF_EMAIL_LOG(120210, "log", "nf_email_log"), NF_EMAIL_REFUSE(120220, "reject", "nf_email_refuse"), NF_EMAIL_WARN(120230, "alert", "nf_email_warn"), NF_EMAIL_OTHER(120240, "other", "nf_email_other"),

    NF_CHAT_LOG(120310, "log", "nf_chat_log"), NF_CHAT_REFUSE(120320, "reject", "nf_chat_refuse"), NF_CHAT_WARN(120330, "alert", "nf_chat_warn"), NF_CHAT_OTHER(120340, "other", "nf_chat_other"),

    NF_BBS_WEIBO_LOG(120410, "log", "nf_bbs_weibo_log"), NF_BBS_WEIBO_REFUSE(120420, "reject", "nf_bbs_weibo_refuse"), NF_BBS_WEIBO_WARN(120430, "alert", "nf_bbs_weibo_warn"), NF_BBS_WEIBO_OTHER(120440, "other", "nf_bbs_weibo_other"),

    NF_OTHER_LOG_LOG(120510, "log", "nf_other_log_log"), NF_OTHER_LOG_REFUSE(120520, "reject", "nf_other_log_refuse"), NF_OTHER_LOG_WARN(120530, "alert", "nf_other_log_warn"), NF_OTHER_LOG_OTHER(120540, "other", "nf_other_log_other");


    private int netAction;

    private String nfType;

    private String netActionDes;


    NfNetActionEnum(int netAction, String nfType, String netActionDes) {
        this.netAction = netAction;
        this.nfType = nfType;
        this.netActionDes = netActionDes;
    }

    public int getNetAction() {
        return netAction;
    }

    public String getNfType() {
        return nfType;
    }

    public String getNetActionDes() {
        return netActionDes;
    }

    public static String getByKey(int key) {
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getValue();
            }
        }
        return "";
    }

    public static NfNetActionEnum getByCodeEnum(Integer code) {
        NfNetActionEnum[] values = NfNetActionEnum.values();
        for (NfNetActionEnum value : values) {
            if (value.getNetAction()== code) {
                return value;
            }
        }
        return null;
    }

    public static String getTableNameByKey(int key) {
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getTableName();
            }
        }
        return "";
    }

    public static int getKeyByValue(String value) {
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum valueTemp : values) {
            if (valueTemp.getValue().equals(value)) {
                return valueTemp.getKey();
            }
        }
        return 0;
    }
}
