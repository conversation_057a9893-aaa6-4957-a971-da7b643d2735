package com.semptian.archives.web.service.service.arc.virtualaccount;

import cn.hutool.core.io.unit.DataUnit;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class FixIpArcVirtualAccountService extends AbstractArcVirtualAccountService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.FIXED_IP;
    }

    @Override
    protected String countDataTypeTemplate(ArcContext ctx) {
        return BusinessCodeEnum.RADIUS_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE.getValue();
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        params.put("auth_account", ctx.getArcAccount());

        params.put("auth_account_type", AuthTypeEnum.FIXED_IP.getType());

       if (DateUtils.dateBefore(ctx.getStartDay(), ctx.getCreateDay())) {
           params.put("start_day", ctx.getCreateDay());
       }

        if (StringUtils.isNotEmpty(ctx.getKeyWord())) {
            params.put("key_word", " AND  lower(virtual_account) LIKE '%" + ctx.getKeyWord().toLowerCase() + "%' ");
        } else {
            params.put("key_word", "");
        }
    }
}
