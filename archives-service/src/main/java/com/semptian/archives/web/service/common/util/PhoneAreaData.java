package com.semptian.archives.web.service.common.util;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 描述：电话号码区域数据
 * 作者：Chongyike
 * 日期：2021/8/4 17:36.
 * <AUTHOR>
 */
public class PhoneAreaData implements Serializable {
    private final static Map<Integer, String> PHONE_AREA_1 = new HashMap<>(144);
    private final static Map<Integer, String> PHONE_AREA_2 = new HashMap<>(58);
    private final static Map<Integer, String> PHONE_AREA_3 = new HashMap<>(36);
    private final static Map<Integer, String> PHONE_AREA_4 = new HashMap<>(13);
    private final static Map<Integer, String> PHONE_AREA_5 = new HashMap<>(25);
    private final static Map<Integer, String> PHONE_AREA_6 = new HashMap<>(28);
    private final static Map<Integer, String> PHONE_AREA_7 = new HashMap<>(1);
    private final static Map<Integer, String> PHONE_AREA_8 = new HashMap<>(11);
    private final static Map<Integer, String> PHONE_AREA_9 = new HashMap<>(30);

    // 区域数据，初始化
    static {
        // 区域1
        PHONE_AREA_1.put(1268, "AG");
        PHONE_AREA_1.put(1264, "AI");
        PHONE_AREA_1.put(1246, "BB");
        PHONE_AREA_1.put(1441, "BM");
        PHONE_AREA_1.put(1242, "BS");
        PHONE_AREA_1.put(1204, "CA");
        PHONE_AREA_1.put(1250, "CA");
        PHONE_AREA_1.put(1306, "CA");
        PHONE_AREA_1.put(1403, "CA");
        PHONE_AREA_1.put(1416, "CA");
        PHONE_AREA_1.put(1418, "CA");
        PHONE_AREA_1.put(1450, "CA");
        PHONE_AREA_1.put(1506, "CA");
        PHONE_AREA_1.put(1514, "CA");
        PHONE_AREA_1.put(1519, "CA");
        PHONE_AREA_1.put(1519, "CA");
        PHONE_AREA_1.put(1604, "CA");
        PHONE_AREA_1.put(1613, "CA");
        PHONE_AREA_1.put(1705, "CA");
        PHONE_AREA_1.put(1709, "CA");
        PHONE_AREA_1.put(1780, "CA");
        PHONE_AREA_1.put(1807, "CA");
        PHONE_AREA_1.put(1807, "CA");
        PHONE_AREA_1.put(1819, "CA");
        PHONE_AREA_1.put(1867, "CA");
        PHONE_AREA_1.put(1867, "CA");
        PHONE_AREA_1.put(1867, "CA");
        PHONE_AREA_1.put(1902, "CA");
        PHONE_AREA_1.put(1902, "CA");
        PHONE_AREA_1.put(1905, "CA");
        PHONE_AREA_1.put(1890, "DM");
        PHONE_AREA_1.put(1809, "DO");
        PHONE_AREA_1.put(1671, "GU");
        PHONE_AREA_1.put(1876, "JM");
        PHONE_AREA_1.put(1869, "KN");
        PHONE_AREA_1.put(1345, "KY");
        PHONE_AREA_1.put(1758, "LC");
        PHONE_AREA_1.put(1670, "MP");
        PHONE_AREA_1.put(1664, "MS");
        PHONE_AREA_1.put(1787, "PR");
        PHONE_AREA_1.put(1868, "TT");
        PHONE_AREA_1.put(1201, "US");
        PHONE_AREA_1.put(1202, "US");
        PHONE_AREA_1.put(1203, "US");
        PHONE_AREA_1.put(1205, "US");
        PHONE_AREA_1.put(1206, "US");
        PHONE_AREA_1.put(1207, "US");
        PHONE_AREA_1.put(1208, "US");
        PHONE_AREA_1.put(1209, "US");
        PHONE_AREA_1.put(1210, "US");
        PHONE_AREA_1.put(1212, "US");
        PHONE_AREA_1.put(1213, "US");
        PHONE_AREA_1.put(1214, "US");
        PHONE_AREA_1.put(1215, "US");
        PHONE_AREA_1.put(1216, "US");
        PHONE_AREA_1.put(1217, "US");
        PHONE_AREA_1.put(1218, "US");
        PHONE_AREA_1.put(1219, "US");
        PHONE_AREA_1.put(1224, "US");
        PHONE_AREA_1.put(1225, "US");
        PHONE_AREA_1.put(1226, "US");
        PHONE_AREA_1.put(1228, "US");
        PHONE_AREA_1.put(1229, "US");
        PHONE_AREA_1.put(1231, "US");
        PHONE_AREA_1.put(1234, "US");
        PHONE_AREA_1.put(1239, "US");
        PHONE_AREA_1.put(1240, "US");
        PHONE_AREA_1.put(1248, "US");
        PHONE_AREA_1.put(1251, "US");
        PHONE_AREA_1.put(1252, "US");
        PHONE_AREA_1.put(1253, "US");
        PHONE_AREA_1.put(1254, "US");
        PHONE_AREA_1.put(1256, "US");
        PHONE_AREA_1.put(1260, "US");
        PHONE_AREA_1.put(1262, "US");
        PHONE_AREA_1.put(1267, "US");
        PHONE_AREA_1.put(1269, "US");
        PHONE_AREA_1.put(1270, "US");
        PHONE_AREA_1.put(1276, "US");
        PHONE_AREA_1.put(1281, "US");
        PHONE_AREA_1.put(1289, "US");
        PHONE_AREA_1.put(1301, "US");
        PHONE_AREA_1.put(1302, "US");
        PHONE_AREA_1.put(1303, "US");
        PHONE_AREA_1.put(1304, "US");
        PHONE_AREA_1.put(1305, "US");
        PHONE_AREA_1.put(1307, "US");
        PHONE_AREA_1.put(1308, "US");
        PHONE_AREA_1.put(1309, "US");
        PHONE_AREA_1.put(1310, "US");
        PHONE_AREA_1.put(1312, "US");
        PHONE_AREA_1.put(1313, "US");
        PHONE_AREA_1.put(1314, "US");
        PHONE_AREA_1.put(1315, "US");
        PHONE_AREA_1.put(1316, "US");
        PHONE_AREA_1.put(1317, "US");
        PHONE_AREA_1.put(1318, "US");
        PHONE_AREA_1.put(1319, "US");
        PHONE_AREA_1.put(1320, "US");
        PHONE_AREA_1.put(1321, "US");
        PHONE_AREA_1.put(1323, "US");
        PHONE_AREA_1.put(1325, "US");
        PHONE_AREA_1.put(1330, "US");
        PHONE_AREA_1.put(1334, "US");
        PHONE_AREA_1.put(1336, "US");
        PHONE_AREA_1.put(1337, "US");
        PHONE_AREA_1.put(1339, "US");
        PHONE_AREA_1.put(1347, "US");
        PHONE_AREA_1.put(1351, "US");
        PHONE_AREA_1.put(1352, "US");
        PHONE_AREA_1.put(1360, "US");
        PHONE_AREA_1.put(1361, "US");
        PHONE_AREA_1.put(1386, "US");
        PHONE_AREA_1.put(1401, "US");
        PHONE_AREA_1.put(1402, "US");
        PHONE_AREA_1.put(1404, "US");
        PHONE_AREA_1.put(1405, "US");
        PHONE_AREA_1.put(1406, "US");
        PHONE_AREA_1.put(1407, "US");
        PHONE_AREA_1.put(1408, "US");
        PHONE_AREA_1.put(1409, "US");
        PHONE_AREA_1.put(1410, "US");
        PHONE_AREA_1.put(1412, "US");
        PHONE_AREA_1.put(1413, "US");
        PHONE_AREA_1.put(1414, "US");
        PHONE_AREA_1.put(1415, "US");
        PHONE_AREA_1.put(1417, "US");
        PHONE_AREA_1.put(1419, "US");
        PHONE_AREA_1.put(1423, "US");
        PHONE_AREA_1.put(1425, "US");
        PHONE_AREA_1.put(1430, "US");
        PHONE_AREA_1.put(1432, "US");
        PHONE_AREA_1.put(1434, "US");
        PHONE_AREA_1.put(1435, "US");
        PHONE_AREA_1.put(1440, "US");
        PHONE_AREA_1.put(1443, "US");
        PHONE_AREA_1.put(1445, "US");
        PHONE_AREA_1.put(1469, "US");
        PHONE_AREA_1.put(1470, "US");
        PHONE_AREA_1.put(1475, "US");
        PHONE_AREA_1.put(1478, "US");
        PHONE_AREA_1.put(1479, "US");
        PHONE_AREA_1.put(1480, "US");
        PHONE_AREA_1.put(1784, "VC");

        // 区域2
        PHONE_AREA_2.put(20, "EG");
        PHONE_AREA_2.put(210, "EH");
        PHONE_AREA_2.put(211, "SS");
        PHONE_AREA_2.put(212, "MA");
        PHONE_AREA_2.put(213, "DZ");
        PHONE_AREA_2.put(216, "TN");
        PHONE_AREA_2.put(218, "LY");
        PHONE_AREA_2.put(220, "GM");
        PHONE_AREA_2.put(221, "SN");
        PHONE_AREA_2.put(222, "MR");
        PHONE_AREA_2.put(223, "ML");
        PHONE_AREA_2.put(224, "GN");
        PHONE_AREA_2.put(226, "BF");
        PHONE_AREA_2.put(227, "NE");
        PHONE_AREA_2.put(228, "TG");
        PHONE_AREA_2.put(229, "BJ");
        PHONE_AREA_2.put(230, "MU");
        PHONE_AREA_2.put(231, "LR");
        PHONE_AREA_2.put(232, "SL");
        PHONE_AREA_2.put(233, "GH");
        PHONE_AREA_2.put(234, "NG");
        PHONE_AREA_2.put(235, "TD");
        PHONE_AREA_2.put(236, "CF");
        PHONE_AREA_2.put(237, "CM");
        PHONE_AREA_2.put(238, "CV");
        PHONE_AREA_2.put(239, "ST");
        PHONE_AREA_2.put(240, "GQ");
        PHONE_AREA_2.put(241, "GA");
        PHONE_AREA_2.put(242, "CG");
        PHONE_AREA_2.put(243, "CD");
        PHONE_AREA_2.put(244, "AO");
        PHONE_AREA_2.put(245, "GW");
        PHONE_AREA_2.put(248, "SC");
        PHONE_AREA_2.put(249, "SD");
        PHONE_AREA_2.put(250, "RW");
        PHONE_AREA_2.put(251, "ET");
        PHONE_AREA_2.put(252, "SO");
        PHONE_AREA_2.put(253, "DJ");
        PHONE_AREA_2.put(254, "KE");
        PHONE_AREA_2.put(255, "TZ");
        PHONE_AREA_2.put(256, "UG");
        PHONE_AREA_2.put(257, "BI");
        PHONE_AREA_2.put(258, "MZ");
        PHONE_AREA_2.put(260, "ZM");
        PHONE_AREA_2.put(261, "MG");
        PHONE_AREA_2.put(262, "RE");
        PHONE_AREA_2.put(263, "ZW");
        PHONE_AREA_2.put(264, "NA");
        PHONE_AREA_2.put(265, "MW");
        PHONE_AREA_2.put(266, "LS");
        PHONE_AREA_2.put(267, "BW");
        PHONE_AREA_2.put(268, "SZ");
        PHONE_AREA_2.put(269, "KM");
        PHONE_AREA_2.put(27, "ZA");
        PHONE_AREA_2.put(291, "ER");
        PHONE_AREA_2.put(297, "AW");
        PHONE_AREA_2.put(298, "FO");
        PHONE_AREA_2.put(299, "GL");

        // 区域3
        PHONE_AREA_3.put(30, "GR");
        PHONE_AREA_3.put(31, "NL");
        PHONE_AREA_3.put(32, "BE");
        PHONE_AREA_3.put(327, "KZ");
        PHONE_AREA_3.put(33, "FR");
        PHONE_AREA_3.put(34, "ES");
        PHONE_AREA_3.put(350, "GI");
        PHONE_AREA_3.put(351, "PT");
        PHONE_AREA_3.put(352, "LU");
        PHONE_AREA_3.put(353, "IE");
        PHONE_AREA_3.put(354, "IS");
        PHONE_AREA_3.put(355, "AL");
        PHONE_AREA_3.put(356, "MT");
        PHONE_AREA_3.put(357, "CY");
        PHONE_AREA_3.put(358, "FI");
        PHONE_AREA_3.put(359, "BG");
        PHONE_AREA_3.put(36, "HU");
        PHONE_AREA_3.put(370, "LT");
        PHONE_AREA_3.put(371, "LV");
        PHONE_AREA_3.put(372, "EE");
        PHONE_AREA_3.put(373, "MD");
        PHONE_AREA_3.put(374, "AM");
        PHONE_AREA_3.put(375, "BY");
        PHONE_AREA_3.put(376, "AD");
        PHONE_AREA_3.put(377, "MC");
        PHONE_AREA_3.put(378, "SM");
        PHONE_AREA_3.put(379, "VA");
        PHONE_AREA_3.put(380, "UA");
        PHONE_AREA_3.put(381, "RS");
        PHONE_AREA_3.put(381, "XK");
        PHONE_AREA_3.put(382, "ME");
        PHONE_AREA_3.put(385, "HR");
        PHONE_AREA_3.put(386, "SI");
        PHONE_AREA_3.put(387, "BA");
        PHONE_AREA_3.put(389, "MK");
        PHONE_AREA_3.put(39, "IT");

        // 区域4
        PHONE_AREA_4.put(40, "RO");
        PHONE_AREA_4.put(41, "CH");
        PHONE_AREA_4.put(420, "CZ");
        PHONE_AREA_4.put(421, "SK");
        PHONE_AREA_4.put(423, "LI");
        PHONE_AREA_4.put(43, "AT");
        PHONE_AREA_4.put(44, "GB");
        PHONE_AREA_4.put(45, "DK");
        PHONE_AREA_4.put(46, "SE");
        PHONE_AREA_4.put(47, "NO");
        PHONE_AREA_4.put(473, "GD");
        PHONE_AREA_4.put(48, "PL");
        PHONE_AREA_4.put(49, "DE");

        // 区域5
        PHONE_AREA_5.put(501, "BZ");
        PHONE_AREA_5.put(502, "GT");
        PHONE_AREA_5.put(503, "SV");
        PHONE_AREA_5.put(504, "HN");
        PHONE_AREA_5.put(505, "NI");
        PHONE_AREA_5.put(506, "CR");
        PHONE_AREA_5.put(507, "PA");
        PHONE_AREA_5.put(509, "HT");
        PHONE_AREA_5.put(51, "PE");
        PHONE_AREA_5.put(52, "MX");
        PHONE_AREA_5.put(53, "CU");
        PHONE_AREA_5.put(54, "AR");
        PHONE_AREA_5.put(55, "BR");
        PHONE_AREA_5.put(56, "CL");
        PHONE_AREA_5.put(57, "CO");
        PHONE_AREA_5.put(58, "VE");
        PHONE_AREA_5.put(590, "GP");
        PHONE_AREA_5.put(591, "BO");
        PHONE_AREA_5.put(592, "GY");
        PHONE_AREA_5.put(593, "EC");
        PHONE_AREA_5.put(594, "GF");
        PHONE_AREA_5.put(595, "PY");
        PHONE_AREA_5.put(596, "MQ");
        PHONE_AREA_5.put(597, "SR");
        PHONE_AREA_5.put(598, "UY");

        // 区域6
        PHONE_AREA_6.put(60, "MY");
        PHONE_AREA_6.put(61, "AU");
        PHONE_AREA_6.put(619164, "CX");
        PHONE_AREA_6.put(62, "ID");
        PHONE_AREA_6.put(63, "PH");
        PHONE_AREA_6.put(64, "NZ");
        PHONE_AREA_6.put(64672, "AQ");
        PHONE_AREA_6.put(65, "SG");
        PHONE_AREA_6.put(66, "TH");
        PHONE_AREA_6.put(670, "TL");
        PHONE_AREA_6.put(672, "NF");
        PHONE_AREA_6.put(673, "BN");
        PHONE_AREA_6.put(674, "NR");
        PHONE_AREA_6.put(675, "PG");
        PHONE_AREA_6.put(676, "TO");
        PHONE_AREA_6.put(677, "SB");
        PHONE_AREA_6.put(678, "VU");
        PHONE_AREA_6.put(679, "FJ");
        PHONE_AREA_6.put(680, "PW");
        PHONE_AREA_6.put(682, "CK");
        PHONE_AREA_6.put(683, "NU");
        PHONE_AREA_6.put(684, "AS");
        PHONE_AREA_6.put(685, "WS");
        PHONE_AREA_6.put(686, "KI");
        PHONE_AREA_6.put(688, "TV");
        PHONE_AREA_6.put(689, "PF");
        PHONE_AREA_6.put(691, "FM");
        PHONE_AREA_6.put(692, "MH");

        // 区域7
        PHONE_AREA_7.put(7, "RU");

        // 区域8
        PHONE_AREA_8.put(81, "JP");
        PHONE_AREA_8.put(82, "KR");
        PHONE_AREA_8.put(84, "VN");
        PHONE_AREA_8.put(850, "KP");
        PHONE_AREA_8.put(852, "HK");
        PHONE_AREA_8.put(853, "MO");
        PHONE_AREA_8.put(855, "KH");
        PHONE_AREA_8.put(856, "LA");
        PHONE_AREA_8.put(86, "CN");
        PHONE_AREA_8.put(880, "BD");
        PHONE_AREA_8.put(886, "TW");

        // 区域9
        PHONE_AREA_9.put(90, "TR");
        PHONE_AREA_9.put(91, "IN");
        PHONE_AREA_9.put(92, "PK");
        PHONE_AREA_9.put(93, "AF");
        PHONE_AREA_9.put(94, "LK");
        PHONE_AREA_9.put(946, "MN");
        PHONE_AREA_9.put(95, "MM");
        PHONE_AREA_9.put(960, "MV");
        PHONE_AREA_9.put(961, "LB");
        PHONE_AREA_9.put(962, "JO");
        PHONE_AREA_9.put(963, "SY");
        PHONE_AREA_9.put(964, "IQ");
        PHONE_AREA_9.put(965, "KW");
        PHONE_AREA_9.put(966, "SA");
        PHONE_AREA_9.put(967, "YE");
        PHONE_AREA_9.put(968, "OM");
        PHONE_AREA_9.put(970, "PS");
        PHONE_AREA_9.put(971, "AE");
        PHONE_AREA_9.put(972, "IL");
        PHONE_AREA_9.put(973, "BH");
        PHONE_AREA_9.put(974, "QA");
        PHONE_AREA_9.put(975, "BT");
        PHONE_AREA_9.put(977, "NP");
        PHONE_AREA_9.put(98, "IR");
        PHONE_AREA_9.put(992, "TJ");
        PHONE_AREA_9.put(993, "TM");
        PHONE_AREA_9.put(994, "AZ");
        PHONE_AREA_9.put(995, "GE");
        PHONE_AREA_9.put(996, "KG");
        PHONE_AREA_9.put(998, "UZ");
    }

    public static Map<Integer, String> getPhoneArea1() {
        return PHONE_AREA_1;
    }

    public static Map<Integer, String> getPhoneArea2() {
        return PHONE_AREA_2;
    }

    public static Map<Integer, String> getPhoneArea3() {
        return PHONE_AREA_3;
    }

    public static Map<Integer, String> getPhoneArea4() {
        return PHONE_AREA_4;
    }

    public static Map<Integer, String> getPhoneArea5() {
        return PHONE_AREA_5;
    }

    public static Map<Integer, String> getPhoneArea6() {
        return PHONE_AREA_6;
    }

    public static Map<Integer, String> getPhoneArea7() {
        return PHONE_AREA_7;
    }

    public static Map<Integer, String> getPhoneArea8() {
        return PHONE_AREA_8;
    }

    public static Map<Integer, String> getPhoneArea9() {
        return PHONE_AREA_9;
    }
}
