package com.semptian.archives.web.service.model;

import com.semptian.base.service.ReturnModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(
        value = "消息对象",
        description = "返回消息对象"
)
public class DownloadDetailModel<T> extends ReturnModel<T> {

    @ApiModelProperty(
            value = "海量全文Md5",
            name = "massMd5",
            dataType = "String"
    )
    private String massMd5;

    @ApiModelProperty(
            value = "文件中心Md5",
            name = "fileMd5",
            dataType = "String"
    )
    private String fileMd5;

    public static <T> DownloadDetailModel<T> getInstance() {
        return new DownloadDetailModel();
    }

    public DownloadDetailModel setMassMd5(String massMd5) {
        this.massMd5 = massMd5;
        return this;
    }

    public DownloadDetailModel setFileMd5(String fileMd5) {
        this.fileMd5 = fileMd5;
        return this;
    }
}
