package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 档案类型枚举
 */
@Getter
public enum ArcTypeEnum {

    RADIUS("1", "radius"),

    EMAIL("2", "email"),

    WEB_SITE("3", "website"),

    APP("4", "app"),

    PHONE("5", "phone"),

    IM("6", "im"),

    NET_PROTOCOL("7", "protocol"),

    FIXED_IP("8","fixed_ip");

    private String key;

    private String value;

    ArcTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getByKey(String key) {
        ArcTypeEnum[] values = ArcTypeEnum.values();
        for (ArcTypeEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }

    public static ArcTypeEnum getArcTypeByKey(String key) {
        ArcTypeEnum[] values = ArcTypeEnum.values();
        for (ArcTypeEnum arcTypeEnum : values) {
            if (arcTypeEnum.getKey().equals(key)) {
                return arcTypeEnum;
            }
        }
        return null;
    }
}
