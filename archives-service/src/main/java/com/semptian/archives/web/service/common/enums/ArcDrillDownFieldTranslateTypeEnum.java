package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 下钻字段翻译类型枚举
 *
 * <AUTHOR>
 * @date 2024/4/3
 */

@Getter
public enum ArcDrillDownFieldTranslateTypeEnum {

    /**
     * 字典翻译
     */
    DICT(1),

    /**
     * 时间戳转换为日期时间: 1712483507357 --> 04-07-2024 17:51:47
     */
    TIMESTAMP_FORMAT(2),

    /**
     * 日期格式化: 2024-04-07 --> 04-07-2024
     */
    DATE_FORMAT(3),

    /**
     * 国家码翻译
     */
    COUNTRY_CODE(4);

    private final Integer translateType;

    ArcDrillDownFieldTranslateTypeEnum(Integer translateType) {
        this.translateType = translateType;
    }

    public static ArcDrillDownFieldTranslateTypeEnum getTypeEnumByTranslateType(Integer translateType) {
        for (ArcDrillDownFieldTranslateTypeEnum value : ArcDrillDownFieldTranslateTypeEnum.values()) {
            if (value.getTranslateType().equals(translateType)) {
                return value;
            }
        }
        return DICT;
    }
}
