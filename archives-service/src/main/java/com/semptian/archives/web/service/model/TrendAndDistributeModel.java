package com.semptian.archives.web.service.model;

import com.semptian.archives.web.service.common.enums.DataSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * LIS和NF记录趋势及分布模型
 *
 * <AUTHOR>
 * @date 2024/3/25 15:03
 **/
@ApiModel("LIS和NF记录趋势及分布模型")
@Data
public class TrendAndDistributeModel {
    /**
     * 档案ID
     */
    @ApiModelProperty(value = "档案ID")
    private String arcId;

    /**
     * 档案账号
     */
    @ApiModelProperty(value = "档案账号")
    private String arcAccount;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "档案类型")
    private Integer arcAccountType;


    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDay;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDay;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private String createDay;

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /**
     * lis还是nf
     */
    private DataSourceEnum dataSourceEnum;

    /**
     * 语种
     */
    private String lang;

}
