package com.semptian.archives.web.service.service.arc.accessor;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AppArcAccessorService extends AbstractArcAccessorService {

    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.APP;
    }

    @Override
    public void buildParams(Map<String, Object> params, ArcContext ctx) {
        params.put("app_name", EscapeUtil.escapeSingleQuote(ctx.getAppName()));

        if (StringUtils.isNotEmpty(ctx.getAppType())) {
            params.put("app_type", "and app_type = " + "'" + EscapeUtil.escapeSingleQuote(ctx.getAppType()) + "'");
        } else {
            params.put("app_type", "");
        }

        Integer netAction = ctx.getNetAction();
        if (netAction != null) {
            if (netAction == 0) {
                // 全部
                params.put("net_action", "");
            } else if (netAction == 1) {
                // Alert
                params.put("net_action", " and net_action in (120130, 120230, 120330, 120430, 120530)");
            } else if (netAction == 2) {
                // Reject
                params.put("net_action", " and net_action in (120120, 120220, 120320, 120420, 120520)");
            } else if (netAction == 3) {
                // Log
                params.put("net_action", " and net_action in (120110, 120210, 120310, 120410, 120510)");
            }
        } else {
            params.put("net_action", "");
        }
    }

    @Override
    String buildKeywordCondition(ArcContext ctx) {
        if (StringUtils.isNotEmpty(ctx.getKeyWord())) {
            switch (ctx.getVisitorType()) {
                case 2:
                    return " and ip like '%" + ctx.getKeyWord() + "%' ";
                case 3:
                    return " and important_target like '%" + ctx.getKeyWord() + "%' ";
                default:
                    return " and visitor_archive_name like '%" + ctx.getKeyWord() + "%' ";
            }
        }
        return "";
    }


    /**
     * @param ctx 查询上下文
     * @return 返回不同visitorType对应的查询模板
     */
    @Override
    public String accessTargetListTemplate(ArcContext ctx) {

        switch (ctx.getVisitorType()) {
            case 2:
                return BusinessCodeEnum.APP_IP_TARGET_DAY_LIST.getValue();
            case 3:
                return BusinessCodeEnum.APP_IMPORTANT_TARGET_DAY_LIST.getValue();
            default:
                return BusinessCodeEnum.APP_ARC_TARGET_DAY_LIST.getValue();
        }
    }
}
