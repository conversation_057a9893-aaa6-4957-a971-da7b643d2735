package com.semptian.archives.web.service.model;

import lombok.Data;

import java.util.List;

/**
 * 认证记录model
 * <AUTHOR>
 * @date 2024/3/26
 */

@Data
public class AuthRecordModel {

    /**
     * 本周时长
     */
    private Double weekHour;

    /**
     * 总时长
     */
    private Double totalHour;

    /**
     * 认证记录列表信息
     */
    private Detail detail;

    @Data
    public static class Detail {
        /**
         * 认证记录总数
         */
        private Integer total;

        /**
         * 认证记录列表
         */
        private List<AuthRecordDetail> list;
    }

    @Data
    public static class AuthRecordDetail {

        /**
         * 捕获时间
         */
        private Long captureTime;

        /**
         * 动作：login/logout
         */
        private String action;

        /**
         * 在线时长
         */
        private Double onlineHour;

        /**
         * ip地址
         */
        private String ip;

        /**
         * mac地址
         */
        private String mac;

        /**
         * 内网ip
         */
        private String internalIp;

        /**
         * 端口范围
         */
        private String portRange;
    }
}
