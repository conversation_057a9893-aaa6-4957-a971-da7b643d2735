package com.semptian.archives.web.service.service.arc.accessor;

import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.ArcFeatures;
import com.semptian.archives.web.service.service.arc.ArcFunctional;

import java.util.Map;

/**
 * 档案访问者服务接口
 * */
public interface ArcAccessorService extends ArcFunctional {

    @Override
    default ArcFeatures feature() {
        return ArcFeatures.ACCESSOR;
    }

    /**
     * 获取访问者列表
     *
     * @param ctx 查询上下文
     * @return 访问者列表
     */
    Map<String, Object> getAccessTargetList(ArcContext ctx);
}
