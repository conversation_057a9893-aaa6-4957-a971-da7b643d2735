package com.semptian.archives.web.service.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.semptian.archives.web.service.model.ArcInfoModel;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class ArcCommonUtils {

    /**
     * 对arcInfoModelList按照ids中顺序进行排序
     * @param arcInfoModelList arcInfoModelList
     * @param ids ids
     * @return 排序后的结果
     */
    public static List<ArcInfoModel> sortResult(List<ArcInfoModel> arcInfoModelList, List<String> ids) {
        //arcInfoModelList转换为Map<String, ArcInfoModel>
        Map<String, ArcInfoModel> arcInfoModelMap = arcInfoModelList.stream().collect(Collectors.toMap(ArcInfoModel::getArcId, Function.identity(), (existing, replacement) -> existing));
        arcInfoModelList = ids.stream().map(arcInfoModelMap::get).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        return arcInfoModelList;
    }

    /**
     *  将以某个分隔符组合的字符串，去重并保留原来顺序
     * @param commaSeparatedValues
     * @return
     */
    public static String removeDuplicatesPreserveOrder(String commaSeparatedValues, String separator) {
        // 使用 LinkedHashSet 来保存元素，它会保留插入顺序，并且不允许重复元素
        Set<String> uniqueValues = new LinkedHashSet<>();

        // 将输入字符串分割成数组
        String[] values = commaSeparatedValues.split(separator);

        // 将数组中的每个元素添加到 LinkedHashSet 中，自动去重
        for (String value : values) {
            uniqueValues.add(value.trim());  // trim() 去除两端的空白
        }

        // 将去重后的元素重新组装成一个以逗号分隔的字符串
        return String.join(separator, uniqueValues);
    }

    /**
     * 将List<String>转换为目录格式的字符串
     *
     * @param list 输入的字符串列表
     * @return 目录格式的字符串
     */
    public static String listToDirectoryPath(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "/";
        }

        StringBuilder directoryPath = new StringBuilder("/");
        for (String item : list) {
            directoryPath.append(item).append("/");
        }

        // 如果需要的话，可以去掉最后一个斜杠
        if (directoryPath.length() > 1) {
            directoryPath.setLength(directoryPath.length() - 1);
        }

        return directoryPath.toString();
    }

}
