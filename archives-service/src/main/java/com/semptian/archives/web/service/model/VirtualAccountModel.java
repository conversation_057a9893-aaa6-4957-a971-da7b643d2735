package com.semptian.archives.web.service.model;

import lombok.Data;

@Data
public class VirtualAccountModel {

    /**
     * 数据来源类型 1=PR  2=NF
     */
    private Integer sourceType;

    /**
     * 协议类型
     */
    private String dataType;

    /**
     * 用于通联的虚拟账号业务场景, 并且只存认证账户关联的源虚拟账户，多个用逗号分隔
     */
    private String srcVirtualAccounts;

    /**
     * 虚拟账号 源账号
     */
    private String virtualAccount;


    /**
     * 虚拟账号类型
     */
    private String virtualAccountType;

    /**
     * 认证账号
     */
    private String authAccount;

    /**
     * 认证账号类型
     */
    private Integer authType;

    /**
     * 最近关联时间
     */
    private Long latestTime;

    /**
     * 最早关联时间
     */
    private Long earliest;

    /**
     * 活跃天数
     */
    private Integer activeDays;

    /**
     * 活跃次数
     */
    private Integer activeNum;


}
