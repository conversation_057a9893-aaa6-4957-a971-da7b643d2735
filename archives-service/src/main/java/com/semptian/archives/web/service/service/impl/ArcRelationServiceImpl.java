package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.core.common.util.ArcIdUtil;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.entity.ArcUserExpansionConfigEntity;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.common.enums.LinkTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/26 14:36
 **/
@Slf4j
@Service
public class ArcRelationServiceImpl implements ArcRelationService {

    private static final String EXPANSION_RULE_ALL = "0";

    private static final String PHONE_EXPANSION_RULE_ALL = "1,2,3";

    private static final String CONNECT_TYPE_ALL = "0";

    private static final Integer HAS_MORE = 1;

    @Autowired
    ArcCommonServiceImpl arcCommonService;

    @Autowired
    ArcRelationServiceImpl arcRelationService;

    @Autowired
    ArcConnectionTypeServiceImpl arcConnectionTypeService;

    @Autowired
    ArcRemarkServiceImpl arcRemarkService;

    @Autowired
    private ArcUserExpansionConfigServiceImpl arcUserExpansionConfigService;

    @Override
    public Object relationExpansion(RelationExpansionRequestModel relationExpansionModel, String userId, String lang) {
        // 1 基于 relationExpansionModel 构建 arcCommonService.getCommonServiceListResult 查询参数
        // 2 根据档案类型，调用 arcCommonService.getCommonServiceListResult 方法，获取查询结果
        // 3 返回查询结果
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(new DateModel(relationExpansionModel.getStartTime(), relationExpansionModel.getEndTime(), 0));
        paramMap.put("arcAccount", relationExpansionModel.getAccount());

        if (ArcTypeEnum.FIXED_IP.getKey().equals(relationExpansionModel.getArcType())) {
            DateUtils.validateFixedIpStartDay(paramMap, relationExpansionModel.getCreateDay());
        }

        PageWarpEntity pageWarpEntity = new PageWarpEntity(relationExpansionModel.getOnPage(), relationExpansionModel.getSize());
        RelationExpansionResultModel relationExpansionResultModel = null;

        if (ArcTypeEnum.RADIUS.getKey().equals(relationExpansionModel.getArcType())
                || ArcTypeEnum.FIXED_IP.getKey().equals(relationExpansionModel.getArcType())
                || ArcTypeEnum.EMAIL.getKey().equals(relationExpansionModel.getArcType())) {
            if (StringUtils.isBlank(relationExpansionModel.getAuthType())) {
                // email 扩线的时候 auth type 会为空
                relationExpansionResultModel = emailRelationExpansion(relationExpansionModel, pageWarpEntity, paramMap);
            } else {
                relationExpansionResultModel = authAccountRelationExpansion(relationExpansionModel, paramMap, pageWarpEntity);
            }
        } else if (ArcTypeEnum.PHONE.getKey().equals(relationExpansionModel.getArcType())) {
            relationExpansionResultModel = phoneRelationExpansion(relationExpansionModel, pageWarpEntity, paramMap);
        } else if (ArcTypeEnum.IM.getKey().equals(relationExpansionModel.getArcType())) {
            relationExpansionResultModel = imRelationExpansion(relationExpansionModel, pageWarpEntity, paramMap);
        }

        if (relationExpansionResultModel != null) {
            supplyNodeInfo(relationExpansionResultModel.getNodes(), userId);
        }

        return relationExpansionResultModel;
    }

    private RelationExpansionResultModel phoneRelationExpansion(RelationExpansionRequestModel relationExpansionModel, PageWarpEntity pageWarpEntity, CommonParamUtil.ParamMap paramMap) {
        RelationExpansionResultModel relationExpansionResultModel;
        RelationExpansionResultModel phoneResultModel = new RelationExpansionResultModel();
        RelationExpansionResultModel emailResultModel = new RelationExpansionResultModel();

        if (relationExpansionModel.getExpansionRule().contains("1")
                || relationExpansionModel.getExpansionRule().contains("2")
                || relationExpansionModel.getExpansionRule().contains("3")
                || relationExpansionModel.getExpansionRule().contains("0")
        ) {
            phoneResultModel = getPhoneRelationExpansion(relationExpansionModel, pageWarpEntity, paramMap);
        }

        if (relationExpansionModel.getExpansionRule().contains("4")
                || relationExpansionModel.getExpansionRule().contains("0")) {
            emailResultModel = authAccountRelationExpansion(relationExpansionModel, paramMap, pageWarpEntity);
        }

        relationExpansionResultModel = mergeRelationExpansionResult(phoneResultModel, emailResultModel);
        return relationExpansionResultModel;
    }

    private RelationExpansionResultModel imRelationExpansion(RelationExpansionRequestModel relationExpansionModel, PageWarpEntity pageWarpEntity, CommonParamUtil.ParamMap paramMap) {
        RelationExpansionResultModel relationExpansionResultModel;
        relationExpansionResultModel = new RelationExpansionResultModel();
        pageWarpEntity.setSortCondition("total DESC, dst_account DESC");
        paramMap.put("data_type", 103);
        ArrayList<VirtualAccountResultModel> relateVirtualAccounts = new ArrayList<>();
        VirtualAccountResultModel virtualAccountResultModel = new VirtualAccountResultModel();
        virtualAccountResultModel.setVirtualAccount(relationExpansionModel.getAccount());
        virtualAccountResultModel.setVirtualAccountType(relationExpansionModel.getVirtualAccountAppType());
        relateVirtualAccounts.add(virtualAccountResultModel);

        RelationNodeModel srcVirtualNode = new RelationNodeModel();
        srcVirtualNode.setId(ArcIdUtil.getImAccountId(relationExpansionModel.getAccount(), relationExpansionModel.getVirtualAccountAppType()));
        srcVirtualNode.setType(relationExpansionModel.getArcType());
        srcVirtualNode.setAuthAccount("");
        srcVirtualNode.setAccount(relationExpansionModel.getAccount());
        srcVirtualNode.setName(relationExpansionModel.getAccount());
        srcVirtualNode.setVirtualAccountAppType(relationExpansionModel.getVirtualAccountAppType());
        relationExpansionResultModel.getNodes().add(srcVirtualNode);

        virtualAccountExpansion(relationExpansionModel, relationExpansionResultModel, paramMap,
                pageWarpEntity, relateVirtualAccounts,DataTypeEnum.IM);
        return relationExpansionResultModel;
    }

    private RelationExpansionResultModel emailRelationExpansion(RelationExpansionRequestModel relationExpansionModel, PageWarpEntity pageWarpEntity, CommonParamUtil.ParamMap paramMap) {
        RelationExpansionResultModel relationExpansionResultModel;
        relationExpansionResultModel = new RelationExpansionResultModel();
        pageWarpEntity.setSortCondition("total DESC, dst_account DESC");
        paramMap.put("data_type", 101);
        ArrayList<VirtualAccountResultModel> relateVirtualAccounts = new ArrayList<>();
        VirtualAccountResultModel virtualAccountResultModel = new VirtualAccountResultModel();
        virtualAccountResultModel.setVirtualAccount(relationExpansionModel.getAccount());
        virtualAccountResultModel.setVirtualAccountType(relationExpansionModel.getVirtualAccountAppType());
        relateVirtualAccounts.add(virtualAccountResultModel);

        RelationNodeModel srcVirtualNode = new RelationNodeModel();
        srcVirtualNode.setId(ArcIdUtil.getEmailAccountId(relationExpansionModel.getAccount()));
        srcVirtualNode.setType(relationExpansionModel.getArcType());
        srcVirtualNode.setAuthAccount("");
        srcVirtualNode.setAccount(relationExpansionModel.getAccount());
        srcVirtualNode.setName(relationExpansionModel.getAccount());
        relationExpansionResultModel.getNodes().add(srcVirtualNode);

        virtualAccountExpansion(relationExpansionModel, relationExpansionResultModel, paramMap, pageWarpEntity,
                relateVirtualAccounts, DataTypeEnum.EMAIL);
        return relationExpansionResultModel;
    }

    private RelationExpansionResultModel getPhoneRelationExpansion(RelationExpansionRequestModel relationExpansionModel, PageWarpEntity pageWarpEntity, CommonParamUtil.ParamMap paramMap) {
        // 号码扩线排序会有区别
        pageWarpEntity.setSortCondition("total DESC,number DESC ");
        RelationExpansionResultModel phoneResultModel = phoneRelationExpansion(relationExpansionModel, paramMap, pageWarpEntity);

        // 遍历 links 补全关系信息
        supplyLinkInfo(phoneResultModel.getLinks());
        dataFilterByLinkType(relationExpansionModel, phoneResultModel);
        return phoneResultModel;
    }

    private RelationExpansionResultModel mergeRelationExpansionResult(RelationExpansionResultModel result1, RelationExpansionResultModel result2) {
        RelationExpansionResultModel result = new RelationExpansionResultModel();
        // 合并规则如下
        // 1 total 取最大值
        // 2 more 只要有一个为true 即为 true
        // 3 nodes 需要根据 id 去重
        // 4 links 直接合并
        if (result1 != null && result2 != null) {
            if (result1.getTotal() < result2.getTotal()) {
                result.setTotal(result2.getTotal());
            } else {
                result.setTotal(result1.getTotal());
            }

            if (result1.isMore() || result2.isMore()) {
                result.setMore(true);
            }

            result.getLinks().addAll(result1.getLinks());
            result.getLinks().addAll(result2.getLinks());

            HashMap<String, RelationNodeModel> reduceMap = new HashMap<>();
            for (RelationNodeModel node : result1.getNodes()) {
                reduceMap.put(node.getId(), node);
            }

            for (RelationNodeModel node : result2.getNodes()) {
                reduceMap.put(node.getId(), node);
            }

            for (RelationNodeModel node : reduceMap.values()) {
                result.getNodes().add(node);
            }
        }

        return result;
    }

    private Set<String> dataFilterByLinkType(RelationExpansionRequestModel relationExpansionModel, RelationExpansionResultModel relationExpansionResultModel) {
        List<RelationLinkModel> links = relationExpansionResultModel.getLinks();
        List<RelationNodeModel> nodes = relationExpansionResultModel.getNodes();

        Set<String> needDeleteAccountId = new HashSet<>();

        String connectType = relationExpansionModel.getConnectType();
        if (!"0".equals(connectType) && StringUtils.isNotBlank(connectType)) {
            // 需要结合关系类型进行数据过滤
            Set<String> needDeleteNodeIds = new HashSet<>();
            Iterator<RelationLinkModel> iterator = links.iterator();

            while (iterator.hasNext()) {
                RelationLinkModel next = iterator.next();
                if (LinkTypeEnum.EMAIL_LINK.getKey().equals(next.getType())
                    || LinkTypeEnum.IM_LINK.getKey().equals(next.getType())
                    ||LinkTypeEnum.PHONE_LINK.getKey().equals(next.getType())) {
                    if (!next.getPeopleRelationType().equals(connectType)) {
                        needDeleteNodeIds.add(next.getEndNodeId());
                        needDeleteAccountId.add(next.getEndNodeId());
                        iterator.remove();
                    }
                }
            }

            nodes.removeIf(nodeModel -> needDeleteNodeIds.contains(nodeModel.getId()));
        }

        return needDeleteAccountId;
    }

    private RelationExpansionResultModel phoneRelationExpansion(RelationExpansionRequestModel relationExpansionModel, CommonParamUtil.ParamMap paramMap, PageWarpEntity pageWarpEntity) {
        RelationExpansionResultModel relationExpansionResultModel = new RelationExpansionResultModel();
        List<RelationNodeModel> nodes = relationExpansionResultModel.getNodes();
        List<RelationLinkModel> links = relationExpansionResultModel.getLinks();

        String expansionRule = relationExpansionModel.getExpansionRule();
        if (expansionRule.contains("1")|| expansionRule.contains("2") || expansionRule.contains("3") || expansionRule.contains("6") ) {
            String callTags = expansionRule.replace("6", "2");

            String[] expansionRuleArr = callTags.split(",");
            //  只保留数组中的 1,2,3 元素
            expansionRuleArr = ArrayUtils.removeElements(expansionRuleArr, "4", "5");

            paramMap.put("call_tag_condition", " AND call_tag in("+String.join(",", expansionRuleArr)+")");
        }  else {
            paramMap.put("call_tag_condition", "");
        }

        if (relationExpansionModel.getMinLinkCount() >= 0) {
            paramMap.put("minLinkCountCondition",  " AND total >= "+ relationExpansionModel.getMinLinkCount()+ " ");
        } else {
            paramMap.put("minLinkCountCondition",  "");
        }

        // 号码档案扩线流程
        PageResultModel pageResult = arcCommonService.getCommonServicePageResult(BusinessCodeEnum.PHONE_RELATION_EXTENSION.getValue(), paramMap, pageWarpEntity);
        List<Map<String, Object>> commonServiceListResult = pageResult.getList();
        Long total = pageResult.getTotal();

        buildPhoneNodeAndLinks(relationExpansionModel, commonServiceListResult, nodes, links);
        // 需要结合 total 和 onPage、size 计算是否还有更多的关系, 计算方式：total > onPage * size
        relationExpansionResultModel.setMore(total > (long) relationExpansionModel.getOnPage() * relationExpansionModel.getSize());
        relationExpansionResultModel.setTotal(total);

        return relationExpansionResultModel;
    }

    private RelationExpansionResultModel authAccountRelationExpansion(RelationExpansionRequestModel relationExpansionModel,
                                                                      CommonParamUtil.ParamMap paramMap, PageWarpEntity pageWarpEntity) {
        RelationExpansionResultModel relationExpansionResultModel = new RelationExpansionResultModel();
        // RADIUS 档案扩线流程
        // 1 若 authAccount 都不为空, 可认为是需要基于 radius 扩线
        // 1.1 需要查询 radius 关联虚拟账号
        // 1.2 查询 虚拟账号，通联目标虚拟账号
        // 1.3 查询 目标虚拟账号，关联 radius 账号
        // 2 若 account 不为空 ，可认为需要基于 虚拟账号通联扩线
        // 2.1 查询 虚拟账号，通联目标虚拟账号
        // 2.2 查询 目标虚拟账号，关联 radius 账号
        List<RelationNodeModel> nodes = relationExpansionResultModel.getNodes();
        List<RelationLinkModel> links = relationExpansionResultModel.getLinks();
        String authType;

        // 判断档案类型是radius 还是固定ip, 并且设置不同的 authType

        if (StringUtils.isNotBlank(relationExpansionModel.getAuthType())) {
            authType = relationExpansionModel.getAuthType();
        } else {
            // 默认基于 radius 扩线
            authType = "1020001";
        }

        paramMap.put("data_type", DataTypeEnum.EMAIL.getKey());

        paramMap.put("authAccount", relationExpansionModel.getAuthAccount());
        paramMap.put("authType", authType);

        String account = relationExpansionModel.getAccount();
        String authAccount = relationExpansionModel.getAuthAccount();

        if (StringUtils.isNotBlank(authAccount) &&
                (
                        relationExpansionModel.getArcType().equals(ArcTypeEnum.RADIUS.getKey())
                        || relationExpansionModel.getArcType().equals(ArcTypeEnum.PHONE.getKey())
                        || relationExpansionModel.getArcType().equals(ArcTypeEnum.FIXED_IP.getKey())
                )
        )
            // 选中的是认证账号节点
            selectedAuthAccount(relationExpansionModel, paramMap, pageWarpEntity,
                    relationExpansionResultModel, authType, authAccount, nodes, links);
        else if (StringUtils.isNotBlank(account))
            // 选中的是虚拟账号节点
            selectedVirtualAccount(relationExpansionModel, paramMap, pageWarpEntity, account, nodes, relationExpansionResultModel);

        return relationExpansionResultModel;
    }

    private void selectedVirtualAccount(RelationExpansionRequestModel relationExpansionModel, CommonParamUtil.ParamMap paramMap, PageWarpEntity pageWarpEntity, String account, List<RelationNodeModel> nodes, RelationExpansionResultModel relationExpansionResultModel) {
        ArrayList<VirtualAccountResultModel> relateVirtualAccount = new ArrayList<>();
        VirtualAccountResultModel virtualAccountResultModel = new VirtualAccountResultModel();
        virtualAccountResultModel.setVirtualAccount(account);
        virtualAccountResultModel.setVirtualAccountType(relationExpansionModel.getVirtualAccountAppType());
        relateVirtualAccount.add(virtualAccountResultModel);
        RelationNodeModel srcVirtualNode = new RelationNodeModel();

        srcVirtualNode.setId(ArcIdUtil.getEmailAccountId(account));
        srcVirtualNode.setType(ArcTypeEnum.EMAIL.getKey());
        srcVirtualNode.setAuthAccount("");
        srcVirtualNode.setAccount(account);
        srcVirtualNode.setName(account);
        nodes.add(srcVirtualNode);

        // 后续email 扩线可复用此方法，通过传入不同的扩线规则进行区分
        virtualAccountExpansion(relationExpansionModel, relationExpansionResultModel, paramMap, pageWarpEntity, relateVirtualAccount, DataTypeEnum.EMAIL);
    }

    private void selectedAuthAccount(RelationExpansionRequestModel relationExpansionModel, CommonParamUtil.ParamMap paramMap, PageWarpEntity pageWarpEntity, RelationExpansionResultModel relationExpansionResultModel, String authType, String authAccount, List<RelationNodeModel> nodes, List<RelationLinkModel> links) {
        pageWarpEntity.setSortCondition("total DESC,virtual_account DESC");
        String serviceCode;

        if (relationExpansionModel.getArcType().equals(ArcTypeEnum.PHONE.getKey())) {
            serviceCode = BusinessCodeEnum.PHONE_RELATE_VIRTUAL.getValue();
        } else {
            serviceCode = BusinessCodeEnum.RADIUS_RELATE_VIRTUAL.getValue();
        }

        PageResultModel pageResult = arcCommonService.getCommonServicePageResult(serviceCode, paramMap, pageWarpEntity);
        List<Map<String, Object>> radiusRelateVirtualAccount = pageResult.getList();
        Long total = pageResult.getTotal();
        // 返回结果如下
        //auth_account |virtual_account
        //44.66.111.221|********
        // 需要结合 total 和 onPage、size 计算是否还有更多的关系, 计算方式：total > onPage * size
        relationExpansionResultModel.setMore(total > (long) relationExpansionModel.getOnPage() * relationExpansionModel.getSize());
        relationExpansionResultModel.setTotal(total);

        ArrayList<VirtualAccountResultModel> relateVirtualAccounts = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(radiusRelateVirtualAccount)) {
            for (Map<String, Object> map : radiusRelateVirtualAccount) {
                String virtualAccount = map.getOrDefault("virtual_account", "").toString();
                String virtualAccountType = map.getOrDefault("virtual_app_type", "").toString();
                VirtualAccountResultModel virtualAccountResultModel = new VirtualAccountResultModel();
                virtualAccountResultModel.setVirtualAccount(virtualAccount);
                virtualAccountResultModel.setVirtualAccountType(virtualAccountType);
                relateVirtualAccounts.add(virtualAccountResultModel);
            }
        }

        // 基于 authAccount, radiusRelateVirtualAccount 构建 links 和 nodes
        RelationNodeModel node = new RelationNodeModel();
        node.setId(ArcIdUtil.getAuthAccountId(authType, authAccount));
        node.setType(relationExpansionModel.getArcType());
        node.setAuthAccount(authAccount);
        node.setName(authAccount);

        if (relationExpansionModel.getArcType().equals(ArcTypeEnum.PHONE.getKey())) {
            node.setAccount(authAccount);
        } else {
            node.setAccount("");
        }

        nodes.add(node);

        for (VirtualAccountResultModel virtualAccountResultModel : relateVirtualAccounts) {
            String accountId;
            String archiveType;
            String virtualAccount = virtualAccountResultModel.getVirtualAccount();
            accountId = ArcIdUtil.getEmailAccountId(virtualAccount);
            archiveType = ArcTypeEnum.EMAIL.getKey();

            RelationNodeModel srcVirtualNode = new RelationNodeModel();
            srcVirtualNode.setId(accountId);
            srcVirtualNode.setType(archiveType);
            srcVirtualNode.setAuthAccount("");
            srcVirtualNode.setAccount(virtualAccount);
            srcVirtualNode.setName(virtualAccount);
            nodes.add(srcVirtualNode);

            RelationLinkModel link = new RelationLinkModel();
            link.setStartNodeId(node.getId());
            link.setStartAccount(authAccount);
            link.setEndNodeId(accountId);
            link.setEndAccount(virtualAccount);
            link.setTotal(0);
            link.setType(LinkTypeEnum.RELATE.getKey());
            links.add(link);
        }

        virtualAccountExpansion(relationExpansionModel, relationExpansionResultModel, paramMap, pageWarpEntity, relateVirtualAccounts, DataTypeEnum.EMAIL);
    }

    private void virtualAccountExpansion(RelationExpansionRequestModel relationExpansionModel, RelationExpansionResultModel relationExpansionResultModel,
                                         CommonParamUtil.ParamMap paramMap, PageWarpEntity pageWarpEntity,
                                         ArrayList<VirtualAccountResultModel> relateVirtualAccounts, DataTypeEnum dataTypeEnum) {
        List<RelationNodeModel> nodes = relationExpansionResultModel.getNodes();
        List<RelationLinkModel> links = relationExpansionResultModel.getLinks();
        ArrayList<String> virtualAccountArr = new ArrayList<>();
        ArrayList<String> virtualAccountTypeArr = new ArrayList<>();
        // 遍历 relateVirtualAccounts 得到 virtualAccountArr
        for (VirtualAccountResultModel virtualAccountResultModel : relateVirtualAccounts) {
            String virtualAccount = virtualAccountResultModel.getVirtualAccount();
            String virtualAccountType = virtualAccountResultModel.getVirtualAccountType();
            virtualAccountArr.add(virtualAccount);
            virtualAccountTypeArr.add(virtualAccountType);
        }

        // ======== 以下为复用逻辑，待提取 ========
        paramMap.put("relateVirtualAccounts",  "'"+String.join("','",virtualAccountArr)+"'");

        int minLinkCount = relationExpansionModel.getMinLinkCount();

        if (minLinkCount >= 0) {
            paramMap.put("minLinkCountCondition",  " AND total >= "+ minLinkCount+ " ");
        } else {
            paramMap.put("minLinkCountCondition",  "");
        }

        if ((dataTypeEnum.equals(DataTypeEnum.IM)) && StringUtils.isNotBlank(relationExpansionModel.getVirtualAccountAppType())) {
            paramMap.put("virtual_app_type_condition", String.format(" and virtual_app_type in ('%s')",  String.join("','",virtualAccountTypeArr)));
        } else {
            paramMap.put("virtual_app_type_condition", "");
        }

        List<Map<String, Object>> connectVirtualAccount = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(relateVirtualAccounts)) {
            pageWarpEntity.setSortCondition("total DESC, dst_account DESC");
            connectVirtualAccount = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_RELATION_EXTENSION.getValue(), paramMap, pageWarpEntity);
        }
        // 返回结果如下
        // src_account            |dst_account           |total|
        // <EMAIL>|<EMAIL>| 6539|

        //如果是 email 和 im 档案需要计算可扩线标识
        if (ArcTypeEnum.IM.getKey().equals(relationExpansionModel.getArcType())
                || ArcTypeEnum.EMAIL.getKey().equals(relationExpansionModel.getArcType())) {
            Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_RELATION_EXTENSION.getValue(), paramMap);
            // 需要结合 total 和 onPage、size 计算是否还有更多的关系, 计算方式：total > onPage * size
            relationExpansionResultModel.setMore(total > (long) relationExpansionModel.getOnPage() * relationExpansionModel.getSize());
            relationExpansionResultModel.setTotal(total);
        }

        // 遍历 connectVirtualAccount, 获取 dst_account List
        List<VirtualAccountResultModel> virtualAccounts = getVirtualAccountResultModels(relationExpansionModel, relationExpansionResultModel, dataTypeEnum, connectVirtualAccount, nodes, links);

        if (CollectionUtil.isNotEmpty(virtualAccounts) &&
                (ArcTypeEnum.RADIUS.getKey().equals(relationExpansionModel.getArcType()) ||
                        ArcTypeEnum.PHONE.getKey().equals(relationExpansionModel.getArcType()) ||
                        ArcTypeEnum.EMAIL.getKey().equals(relationExpansionModel.getArcType()) ||
                        ArcTypeEnum.FIXED_IP.getKey().equals(relationExpansionModel.getArcType()))) {
            if (ArcTypeEnum.EMAIL.getKey().equals(relationExpansionModel.getArcType())
                    && StringUtils.isBlank(relationExpansionModel.getAuthType()) ) {
                // EMAIL档案 扩线会进入这里 nothing to do
            }  else {
                buildVirtualRelateAuthAccount(relationExpansionModel, paramMap, pageWarpEntity, virtualAccounts, nodes, links);
            }
        }
    }

    private List<VirtualAccountResultModel> getVirtualAccountResultModels(RelationExpansionRequestModel relationExpansionModel,
                                                                          RelationExpansionResultModel relationExpansionResultModel,
                                                                          DataTypeEnum dataTypeEnum, List<Map<String, Object>> connectVirtualAccount,
                                                                          List<RelationNodeModel> nodes, List<RelationLinkModel> links) {
        List<VirtualAccountResultModel> virtualAccounts = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(connectVirtualAccount)) {
            for (Map<String, Object> map : connectVirtualAccount) {
                String srcAccount = map.getOrDefault("src_account", "").toString();
                String dstAccount = map.getOrDefault("dst_account", "").toString();
                String virtualAppType = relationExpansionModel.getVirtualAccountAppType();

                Integer count = Integer.valueOf(map.getOrDefault("total", "").toString());
                String srcNodeId;
                String dstNodeId;
                if (DataTypeEnum.IM.equals(dataTypeEnum)) {
                    srcNodeId = ArcIdUtil.getImAccountId(srcAccount, virtualAppType);
                    dstNodeId = ArcIdUtil.getImAccountId(dstAccount, virtualAppType);
                } else {
                    srcNodeId = ArcIdUtil.getEmailAccountId(srcAccount);
                    dstNodeId = ArcIdUtil.getEmailAccountId(dstAccount);
                }

                VirtualAccountResultModel virtualAccountResultModel = new VirtualAccountResultModel();
                virtualAccountResultModel.setArcId(dstNodeId);
                virtualAccountResultModel.setVirtualAccount(dstAccount);
                virtualAccountResultModel.setVirtualAccountType(virtualAppType);

                virtualAccounts.add(virtualAccountResultModel);

                // 构建 nodes
                RelationNodeModel dstVirtualNode = new RelationNodeModel();

                if (DataTypeEnum.IM.equals(dataTypeEnum)) {
                    dstVirtualNode.setId(ArcIdUtil.getImAccountId(dstAccount, virtualAppType));
                    dstVirtualNode.setType(ArcTypeEnum.IM.getKey());
                    dstVirtualNode.setVirtualAccountAppType(relationExpansionModel.getVirtualAccountAppType());
                } else {
                    dstVirtualNode.setId(ArcIdUtil.getEmailAccountId(dstAccount));
                    dstVirtualNode.setType(ArcTypeEnum.EMAIL.getKey());
                }

                dstVirtualNode.setAuthAccount("");
                dstVirtualNode.setName(dstAccount);
                dstVirtualNode.setAccount(dstAccount);
                dstVirtualNode.setVirtualAccountAppType(virtualAppType);
                nodes.add(dstVirtualNode);

                // 构建 links
                RelationLinkModel link = new RelationLinkModel();
                link.setStartNodeId(srcNodeId);
                link.setStartAccount(srcAccount);
                link.setEndNodeId(dstNodeId);
                link.setEndAccount(dstAccount);
                link.setTotal(count);
                if (DataTypeEnum.IM.equals(dataTypeEnum)) {
                    link.setType(LinkTypeEnum.IM_LINK.getKey());
                } else {
                    link.setType(LinkTypeEnum.EMAIL_LINK.getKey());
                }

                HashMap<Object, Object> typeCountInfo = new HashMap<>();
                if (DataTypeEnum.IM.equals(dataTypeEnum)) {
                    typeCountInfo.put("IM", count);
                } else {
                    typeCountInfo.put("EMAIL", count);
                }

                link.setTypeCountInfo(typeCountInfo);
                links.add(link);
            }

            // 遍历 links 补全关系信息
            supplyLinkInfo(relationExpansionResultModel.getLinks());

            //根据通联关系类型,先过滤出无效的通联虚拟账号，避免后续带出通联虚拟账号的关联radius账号
            Set<String> needDeleteNodeAccountIds = dataFilterByLinkType(relationExpansionModel, relationExpansionResultModel);

            virtualAccounts = virtualAccounts.stream().filter(virtualAccount -> !needDeleteNodeAccountIds.contains(virtualAccount.getArcId())).collect(Collectors.toList());
        }

        return virtualAccounts;
    }

    private void buildVirtualRelateAuthAccount(RelationExpansionRequestModel relationExpansionModel, CommonParamUtil.ParamMap paramMap,
                                               PageWarpEntity pageWarpEntity, List<VirtualAccountResultModel> virtualAccounts, List<RelationNodeModel> nodes, List<RelationLinkModel> links) {
        // 基于 dst_account List 获取虚拟账号关联的 authAccount List
        ArrayList<String> virtualAccountArr = new ArrayList<>();
        virtualAccounts.forEach(virtualAccount -> virtualAccountArr.add(virtualAccount.getVirtualAccount()));
        String virtualAccountCondition = "'" + String.join("','", virtualAccountArr) + "'";
        paramMap.put("relateVirtualAccountCondition", virtualAccountCondition);

        pageWarpEntity.setSortCondition("total DESC, virtual_account DESC, auth_account DESC");
        String serviceCode;
        if (ArcTypeEnum.PHONE.getKey().equals(relationExpansionModel.getArcType()) ||
                AuthTypeEnum.PHONE.getType().toString().equals(relationExpansionModel.getAuthType())
        ) {
            serviceCode = BusinessCodeEnum.PHONE_VIRTUAL_RELATE_RADIUS.getValue();
        } else {
            serviceCode = BusinessCodeEnum.VIRTUAL_RELATE_RADIUS.getValue();
        }
        List<Map<String, Object>> virtualRelateRadius = arcCommonService.getCommonServiceListResult(serviceCode, paramMap);
        // 返回结果如下
        //auth_account | auth_type |virtual_account
        //44.66.111.221 | 1029997 |********

        // 基于  virtualRelateRadius 构建 links 和 nodes
        if (CollectionUtil.isNotEmpty(virtualRelateRadius)) {
            for (Map<String, Object> map : virtualRelateRadius) {
                String relateAuthAccount = map.getOrDefault("auth_account", "").toString();
                String relateAuthType = map.getOrDefault("auth_type", "").toString();
                String virtualAccount = map.getOrDefault("virtual_account", "").toString();

                String virtualAccountId="";
                virtualAccountId = ArcIdUtil.getEmailAccountId(virtualAccount);

                String relateAuthAccountId = ArcIdUtil.getAuthAccountId(relateAuthType, relateAuthAccount);

                RelationNodeModel radiusNode = new RelationNodeModel();
                radiusNode.setId(ArcIdUtil.getAuthAccountId(relateAuthType, relateAuthAccount));

                if (AuthTypeEnum.RADIUS.getType().toString().equals(relateAuthType)) {
                    radiusNode.setType(ArcTypeEnum.RADIUS.getKey());
                } else if (AuthTypeEnum.PHONE.getType().toString().equals(relateAuthType)) {
                    radiusNode.setType(ArcTypeEnum.PHONE.getKey());
                } else if (AuthTypeEnum.FIXED_IP.getType().toString().equals(relateAuthType)) {
                    radiusNode.setType(ArcTypeEnum.FIXED_IP.getKey());
                }

                radiusNode.setAuthAccount(relateAuthAccount);
                radiusNode.setName(relateAuthAccount);
                radiusNode.setAccount(relateAuthAccount);
                nodes.add(radiusNode);

                RelationLinkModel link = new RelationLinkModel();
                link.setStartNodeId(virtualAccountId);
                link.setStartAccount(virtualAccount);
                link.setEndNodeId(relateAuthAccountId);
                link.setEndAccount(relateAuthAccount);
                link.setTotal(0);
                link.setType(LinkTypeEnum.RELATE.getKey());
                links.add(link);
            }
        }
    }

    private void supplyNodeInfo(List<RelationNodeModel> nodes, String userId) {
        try {
            if (CollectionUtil.isNotEmpty(nodes)) {
                ArrayList<String> arcIds = new ArrayList<>();
                for (RelationNodeModel node : nodes) {
                    arcIds.add(node.getId());
                }

                List<ArcRemarkEntity> arcRemarkEntities = arcRemarkService.getArcRemarkByArcIdsAndUserId(arcIds, userId);

                for (ArcRemarkEntity arcRemarkEntity : arcRemarkEntities) {
                    for (RelationNodeModel node : nodes) {
                        if (arcRemarkEntity.getArcId().equals(node.getId())) {
                            node.setName(arcRemarkEntity.getRemark());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("set nick name error ", e);
        }
    }

    private void supplyLinkInfo(List<RelationLinkModel> links) {
        for (RelationLinkModel link : links) {
            try {
                String peopleRelationType = arcConnectionTypeService.getArcConnectionTypeByArcId(link.getStartNodeId(), link.getEndNodeId());
                link.setPeopleRelationType(peopleRelationType);
            } catch (Exception e) {
                log.error("set link relation type error ", e);
            }
        }
    }

    /**
     * 构建号码节点和关系
     * @param relationExpansionModel cc
     * @param commonServiceListResult cc
     * @param nodes  cc
     * @param links cc
     */
    private void buildPhoneNodeAndLinks(RelationExpansionRequestModel relationExpansionModel, List<Map<String, Object>> commonServiceListResult, List<RelationNodeModel> nodes, List<RelationLinkModel> links) {
        // 返回结构如下
        // number|total|call|fax|sms|
        // 120   |    2|   0|  2|  0|
        // 1 遍历查询结果，构建 nodes 和 links
        if (commonServiceListResult != null && !commonServiceListResult.isEmpty()) {
            for (Map<String, Object> map : commonServiceListResult) {
                RelationNodeModel node = new RelationNodeModel();
                String number = map.getOrDefault("number", "").toString();
                node.setId(ArcIdUtil.getPhoneId(number));
                node.setType(ArcTypeEnum.PHONE.getKey());
                node.setAuthAccount(number);
                node.setAccount(number);
                node.setName(number);
                nodes.add(node);
            }

            RelationNodeModel srcNode = new RelationNodeModel();
            srcNode.setId(ArcIdUtil.getPhoneId(relationExpansionModel.getAuthAccount()));
            srcNode.setType(ArcTypeEnum.PHONE.getKey());
            srcNode.setAuthAccount(relationExpansionModel.getAuthAccount());
            srcNode.setAccount(relationExpansionModel.getAuthAccount());
            srcNode.setName(relationExpansionModel.getAuthAccount());
            nodes.add(srcNode);

            for (Map<String, Object> map : commonServiceListResult) {
                RelationLinkModel link = new RelationLinkModel();
                String srcAccount = relationExpansionModel.getAuthAccount();
                String srcId = ArcIdUtil.getPhoneId(srcAccount);

                String targetAccount = map.getOrDefault("number", "").toString();
                String targetId = ArcIdUtil.getPhoneId(targetAccount);

                Integer total = Integer.valueOf(map.getOrDefault("total", 0).toString());
                Integer call = Integer.valueOf(map.getOrDefault("call", 0).toString());
                Integer fax = Integer.valueOf(map.getOrDefault("fax", 0).toString());
                Integer sms = Integer.valueOf(map.getOrDefault("sms", 0).toString());

                HashMap<Object, Object> typeCountInfo = new HashMap<>();
                typeCountInfo.put("SMS", sms);
                typeCountInfo.put("CALL", call);
                typeCountInfo.put("FAX", fax);

                link.setStartNodeId(srcId);
                link.setStartAccount(srcAccount);
                link.setEndNodeId(targetId);
                link.setEndAccount(targetAccount);
                link.setTotal(total);
                link.setTypeCountInfo(typeCountInfo);
                link.setType(LinkTypeEnum.PHONE_LINK.getKey());

                links.add(link);
            }
        }
    }


    @Override
    public Object hasMoreRelation(HasMoreRelationModel hasMoreRelationModel) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> virtualRelateRadius = new ArrayList<>();

        if (CollUtil.isNotEmpty(hasMoreRelationModel.getAccount()) && ArcTypeEnum.PHONE.getKey().equals(hasMoreRelationModel.getArcType())) {
            CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(hasMoreRelationModel.getDateModel());
            paramMap.put("srcAccount", hasMoreRelationModel.getSrcAccount());
            paramMap.put("account", String.join("','", hasMoreRelationModel.getAccount()));

            //扩线规则
            String callTag = "";
            if (StrUtil.isNotEmpty(hasMoreRelationModel.getExpansionRule()) && !EXPANSION_RULE_ALL.equals(hasMoreRelationModel.getExpansionRule())
                    && !PHONE_EXPANSION_RULE_ALL.equals(hasMoreRelationModel.getExpansionRule())) {
                callTag = "AND call_tag IN (" + hasMoreRelationModel.getExpansionRule() + ") ";
            }
            paramMap.put("call_tag", callTag);

            virtualRelateRadius = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.HAS_MORE_RELATION.getValue(), paramMap);
            if (CollUtil.isNotEmpty(virtualRelateRadius)) {
                //查询出所有待查询扩线标识账号与通联账号以及之间通联次数.由于查询结果区分主叫被叫方向,需要对不同方向次数进行合并同时过滤节点类型

                //先根据档案关系类型过滤
                filterByConnectType(hasMoreRelationModel, virtualRelateRadius);

                if (CollUtil.isNotEmpty(virtualRelateRadius)) {
                    //获取所有的待查询号码和对应通联号码以及对应通联次数
                    Map<String, Long> relationMap = new HashMap<>();
                    virtualRelateRadius.forEach(map -> {
                        String account = map.getOrDefault("account", "").toString();
                        String dstAccount = map.getOrDefault("dstAccount", "").toString();
                        Long behaviorNum = Long.valueOf(map.getOrDefault("behaviorNum", 0).toString());

                        relationMap.put(account + "||" + dstAccount, behaviorNum);
                    });

                    //通联次数过滤:通联次数大于等于最小通联次数则保留否则删除
                    for (int i = virtualRelateRadius.size() - 1; i >= 0; i--) {
                        Map<String, Object> map = virtualRelateRadius.get(i);
                        String account = map.getOrDefault("account", "").toString();
                        String dstAccount = map.getOrDefault("dstAccount", "").toString();

                        Long behaviorNum = relationMap.get(account + "||" + dstAccount);
                        //对向可能不存在
                        if (relationMap.containsKey(dstAccount + "||" + account)) {
                            Long dstBehaviorNum = relationMap.get(dstAccount + "||" + account);
                            behaviorNum += dstBehaviorNum;
                        }

                        if (behaviorNum < hasMoreRelationModel.getMinLinkCount()) {
                            virtualRelateRadius.remove(i);
                        }
                    }

                    //获取所有account并返回待判断的源账号
                    if (CollUtil.isNotEmpty(virtualRelateRadius)) {
                        Set<String> accountSet = new HashSet<>();
                        virtualRelateRadius.forEach(map -> {
                            String account = map.getOrDefault("account", "").toString();
                            String dstAccount = map.getOrDefault("dstAccount", "").toString();
                            accountSet.add(account);
                            accountSet.add(dstAccount);
                        });

                        List<String> accountList = hasMoreRelationModel.getAccount();
                        //返回最终结果
                        virtualRelateRadius = accountList.stream().filter(accountSet::contains).map(account -> {
                            Map<String, Object> map = new HashMap<>();
                            map.put("account", account);
                            map.put("hasMore", HAS_MORE);
                            return map;
                        }).collect(Collectors.toList());
                    }
                }
            }
        }

        result.put("list", virtualRelateRadius);
        return result;
    }

    private void filterByConnectType(HasMoreRelationModel hasMoreRelationModel, List<Map<String, Object>> virtualRelateRadius) {
        if (StrUtil.isNotEmpty(hasMoreRelationModel.getConnectType())
                && !CONNECT_TYPE_ALL.equals(hasMoreRelationModel.getConnectType())) {

            for (int i = virtualRelateRadius.size() - 1; i >= 0; i--) {
                Map<String, Object> relateRadius = virtualRelateRadius.get(i);

                String account = relateRadius.getOrDefault("account", "").toString();
                String dstAccount = relateRadius.getOrDefault("dstAccount", "").toString();

                String srcId = ArcIdUtil.getPhoneId(account);
                String dstId = ArcIdUtil.getPhoneId(dstAccount);

                String connectionType = arcConnectionTypeService.getArcConnectionTypeByArcId(srcId, dstId);
                String connectionTypeRevert = arcConnectionTypeService.getArcConnectionTypeByArcId(dstId, srcId);

                if ((StrUtil.isEmpty(connectionType) || !connectionType.equals(hasMoreRelationModel.getConnectType()))
                        && (StrUtil.isEmpty(connectionTypeRevert) || !connectionTypeRevert.equals(hasMoreRelationModel.getConnectType()))) {
                    virtualRelateRadius.remove(i);
                }
            }
        }
    }

    @Override
    public Object getUserExpansionConfig(String userId) {
        QueryWrapper<ArcUserExpansionConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("create_user", userId);
        // 查询当前用户的配置
        ArcUserExpansionConfigEntity userConfig = arcUserExpansionConfigService.getOne(wrapper);

        if (userConfig == null) {
            // 如果当前用户无配置，则查询默认用户的配置
            QueryWrapper<ArcUserExpansionConfigEntity> defaultWrapper = new QueryWrapper<>();
            defaultWrapper.eq("create_user", "default");
            userConfig = arcUserExpansionConfigService.getOne(defaultWrapper);
        }

        return userConfig;
    }

    @Override
    public Object saveUserExpansionConfig(UserExpansionConfigModel userExpansionConfig) {
        QueryWrapper<ArcUserExpansionConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("create_user", userExpansionConfig.getCreateUser());
        ArcUserExpansionConfigEntity entity = arcUserExpansionConfigService.getOne(wrapper);

        if (entity == null) {
            entity = new ArcUserExpansionConfigEntity();
            entity.setConfigJson(userExpansionConfig.getConfigJson());
            entity.setCreateUser(userExpansionConfig.getCreateUser());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(System.currentTimeMillis());

            arcUserExpansionConfigService.save(entity);
        } else {
            entity.setConfigJson(userExpansionConfig.getConfigJson());
            entity.setUpdateTime(System.currentTimeMillis());

            arcUserExpansionConfigService.update(entity, wrapper);
        }

        return entity;
    }
}

