package com.semptian.archives.web.service.common.util;

import java.io.Serializable;
import java.util.Map;

/**
 * 描述：电话号码归属地国家简码获取
 * 作者：Chongyike
 * 日期：2021/8/4 17:53.
 * <AUTHOR>
 */
public class PhoneAreaUtils implements Serializable {

    // 根据电话号码来计算国家简码，默认返回空值
    public static String getIsoCountryCode(String phoneNumber) {
        // 判空，且长度
        if (phoneNumber == null || phoneNumber.isEmpty() || phoneNumber.length() <= 1) {
            return "";
        }

        int areaType = Integer.valueOf(phoneNumber.substring(0, 1));
        String isoCountryCode;

        switch (areaType) {
            case 1:
                isoCountryCode = getIsoCountryCodeFromArea1(phoneNumber);
                break;
            case 2:
                isoCountryCode = getIsoCountryCodeFromArea2(phoneNumber);
                break;
            case 3:
                isoCountryCode = getIsoCountryCodeFromArea3(phoneNumber);
                break;
            case 4:
                isoCountryCode = getIsoCountryCodeFromArea4(phoneNumber);
                break;
            case 5:
                isoCountryCode = getIsoCountryCodeFromArea5(phoneNumber);
                break;
            case 6:
                isoCountryCode = getIsoCountryCodeFromArea6(phoneNumber);
                break;
            case 7:
                isoCountryCode = getIsoCountryCodeFromArea7(phoneNumber);
                break;
            case 8:
                isoCountryCode = getIsoCountryCodeFromArea8(phoneNumber);
                break;
            case 9:
                isoCountryCode = getIsoCountryCodeFromArea9(phoneNumber);
                break;
            default:
                isoCountryCode = "";
        }

        return isoCountryCode;
    }

    // 大部分区域的电话号码国际区号，大部分为3，一部分为2，所以提炼为公共方法
    private static String getIsoCountryCodeCommon(String phoneNumber, Map<Integer, String> areaMap) {
        // 判空，且长度
        if (phoneNumber == null || phoneNumber.isEmpty() || phoneNumber.length() <= 3) {
            return "";
        }

        // 优先判断3位是否匹配
        int phoneNumberAreaIndex = Integer.valueOf(phoneNumber.substring(0, 3));

        String isoCountryCode = areaMap.getOrDefault(phoneNumberAreaIndex, "");

        // 若3位未匹配到，则进行2位匹配
        if (isoCountryCode.isEmpty()) {
            phoneNumberAreaIndex = phoneNumberAreaIndex / 10;

            isoCountryCode = areaMap.getOrDefault(phoneNumberAreaIndex, "");
        }

        return isoCountryCode;
    }

    // 区域1的电话号码国际区号，长度为4
    private static String getIsoCountryCodeFromArea1(String phoneNumber) {
        // 判空，且长度
        if (phoneNumber == null || phoneNumber.isEmpty() || phoneNumber.length() <= 4) {
            return "";
        }

        // 对电话号码截取前4位，作为待匹配的国际区号
        int phoneNumberAreaIndex = Integer.valueOf(phoneNumber.substring(0, 4));

        return PhoneAreaData.getPhoneArea1().getOrDefault(phoneNumberAreaIndex, "");
    }

    // 区域2的电话号码国际区号，大部分为3，一部分为2，调用通用方法
    private static String getIsoCountryCodeFromArea2(String phoneNumber) {
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea2());
    }

    // 区域3的电话号码国际区号，大部分为3，一部分为2，调用通用方法
    private static String getIsoCountryCodeFromArea3(String phoneNumber) {
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea3());
    }

    // 区域4的电话号码国际区号，大部分为3，一部分为2，调用通用方法
    private static String getIsoCountryCodeFromArea4(String phoneNumber) {
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea4());
    }

    // 区域5的电话号码国际区号，大部分为3，一部分为2，调用通用方法
    private static String getIsoCountryCodeFromArea5(String phoneNumber) {
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea5());
    }

    // 区域8的电话号码国际区号，大部分为3，一部分为2，调用通用方法
    private static String getIsoCountryCodeFromArea8(String phoneNumber) {
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea8());
    }

    // 区域9的电话号码国际区号，大部分为3，一部分为2，调用通用方法
    private static String getIsoCountryCodeFromArea9(String phoneNumber) {
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea9());
    }

    // 区域7的电话号码国际区号，只有一位，为俄罗斯
    private static String getIsoCountryCodeFromArea7(String phoneNumber) {
        // 判空，且长度
        if (phoneNumber == null || phoneNumber.isEmpty() || phoneNumber.length() <= 1) {
            return "";
        }

        // 优先判断3位是否匹配
        int phoneNumberAreaIndex = Integer.valueOf(phoneNumber.substring(0, 1));

        return PhoneAreaData.getPhoneArea7().getOrDefault(phoneNumberAreaIndex, "");
    }

    // 区域6的电话号码国际区号以三位和两位为主，其中61和64开头的，既有两位也有大于5位，所以分开处理
    private static String getIsoCountryCodeFromArea6(String phoneNumber) {
        // 判空，且长度
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return "";
        }

        // 默认截取前三位
        int phoneNumberAreaIndexTemp;
        String isoCountryCode;
        // 以61开头
        if (phoneNumber.startsWith("61") && phoneNumber.length() > 6) {
            phoneNumberAreaIndexTemp = Integer.valueOf(phoneNumber.substring(0, 6));
            isoCountryCode = PhoneAreaData.getPhoneArea6().getOrDefault(phoneNumberAreaIndexTemp, "");

            if (!isoCountryCode.isEmpty()) {
                return isoCountryCode;
            }
        }

        // 以64开头
        if (phoneNumber.startsWith("64") && phoneNumber.length() > 5) {
            phoneNumberAreaIndexTemp = Integer.valueOf(phoneNumber.substring(0, 5));
            isoCountryCode = PhoneAreaData.getPhoneArea6().getOrDefault(phoneNumberAreaIndexTemp, "");

            if (!isoCountryCode.isEmpty()) {
                return isoCountryCode;
            }
        }

        // 一般情况下走3位和两位的匹配逻辑
        return getIsoCountryCodeCommon(phoneNumber, PhoneAreaData.getPhoneArea6());
    }

}
