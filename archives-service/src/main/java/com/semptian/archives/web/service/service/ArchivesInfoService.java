package com.semptian.archives.web.service.service;

import com.semptian.archives.web.service.model.ArcCollectionModel;
import com.semptian.archives.web.service.model.ArcInfoModel;
import com.semptian.archives.web.service.model.ArcQueryModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.ExternalArcQueryModel;

import java.util.List;

/**
 * @Author: create by l<PERSON><PERSON>
 * @Date: 2020/10/22
 * @Desc: com.semptian.archives.web.service.service
 **/
public interface ArchivesInfoService {

    Object queryArcInfo(ArcQueryModel arcQueryModel, DateModel dateModel, List<Integer> permissionList);

    /**
     * 查询档案详细信息
     * @param arcId 档案id
     * @param arcAccount 档案账号
     * @param arcType 档案类型
     * @param arcAccountType 档案账号类型
     * @param userId 用户ID
     * @return 档案信息
     */
    ArcInfoModel getArcInfoById(String arcId, String arcAccount, Integer arcType, String arcAccountType, String userId, String lang);

    /**
     * 根据档案id集合和档案类型批量查询档案信息
     * @param arcIds 档案ids
     * @param arcType 档案类型
     * @return 档案信息
     */
    List<ArcInfoModel> getArcInfoByIds(List<String> arcIds, Integer arcType);

    Object arcQuery(ExternalArcQueryModel externalArcQueryModel, List<Integer> permissionList);

    /**
     * 档案收藏状态更新
     * @param  arcCollectionModel arcCollectionModel
     * @param userId 用户id
     * @return 更新结果
     */
    Boolean updateArcCollection(ArcCollectionModel arcCollectionModel, String userId);

    /**
     * 档案收藏清空
     * @param userId 用户id
     * @return 更新结果
     */
    Boolean clearArcCollection(String userId);
}
