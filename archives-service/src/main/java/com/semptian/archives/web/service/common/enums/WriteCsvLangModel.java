package com.semptian.archives.web.service.common.enums;

public enum  WriteCsvLangModel {

    RADIUSZH_CN(1,"序号,名称,应用分类,认证账号,出现次数,最早时间,最近时间"),
    VIRTUALZH_CN(2,"序号,本方IP,国家,对方IP,国家,通联账号,最早通联时间,最近通联时间,次数"),
    DOMAINZH_CN(3,"序号,访问者,类型,访问区域,行为类次数,最早访问时间,最近访问时间"),
    DOMAINIPZH_CN(3,"序号,访问者,访问区域,行为类次数,最早访问时间,最近访问时间"),
    APPZH_CN(4,"序号,访问者,类型,访问区域,行为类次数,最早访问时间,最近访问时间"),
    APPIPZH_CN(4,"序号,访问者,访问区域,行为类次数,最早访问时间,最近访问时间"),
//    PHONEZH_CN(5,"序号,通联号码,归属区域,最早通联时间,最近通联时间,通联天数,次数"),
    PHONEZH_CN(5,"序号,通联号码,归属区域,最早通联时间,最近通联时间,通联天数"),

    RADIUSEN_US(6,"The serial number,name,Application of classification,Verified account,occurrences,In recent time,The first time"),
    VIRTUALEN_US(7,"The serial number,The local IP,The local country,Destination IP,Purpose of the national,Tonly account,Earliest contact time,Last contact time,occurrences"),
    DOMAINEN_US(8,"The serial number,The visitor,type,Visitor area,Number of behavior classes,Earliest access time,Last visit time"),
    DOMAINIPEN_US(8,"The serial number,The visitor,Visitor area,Number of behavior classes,Earliest access time,Last visit time"),
    APPEN_US(9,"The serial number,The visitor,type,Visitor area,Number of behavior classes,Earliest access time,Last visit time"),
    APPIPEN_US(9,"The serial number,The visitor,Visitor area,Number of behavior classes,Earliest access time,Last visit time"),
//    PHONEEN_US(10,"The serial number,Tonly number,Attribution area,Earliest contact time,Last contact time,occurrences"),
    PHONEEN_US(10,"The serial number,Tonly number,Attribution area,Earliest contact time,Last contact time"),

    RADIUSFR_FR(11,"NO.,Nom,Séparation des applications,Compte d`authentification,fois d`apparence,La première heure,La dernière heure"),
    VIRTUALFR_FR(12,"NO.,IP,pays de contrepartie,Notre propriété intellectuelle,notre pays,compte de contact,Heure de contact au plus tôt,Heure du dernier contact,Nombre de fois"),
    DOMAINFR_FR(13,"NO.,visiteur,type,zone opposee,Nombre de comportements,La première heure de visite,La dernière heure de visite"),
    DOMAINIPFR_FR(13,"NO.,visiteur,zone opposee,Nombre de comportements,La première heure de visite,La dernière heure de visite"),
    APPFR_FR(14,"NO.,visiteur,type,zone opposee,Nombre de comportements,La première heure de visite,La dernière heure de visite"),
    APPIPFR_FR(14,"NO.,visiteur,zone opposee,Nombre de comportements,La première heure de visite,La dernière heure de visite"),
//    PHONEFR_FR(15,"NO.,Numéro de communication et de liaison,zone d`appartenance,Heure de contact au plus tôt,Heure du dernier contact,Nombre de jours en liaison,Nombre de fois");
    PHONEFR_FR(15,"NO.,Numéro de communication et de liaison,zone d`appartenance,Heure de contact au plus tôt,Heure du dernier contact,Nombre de jours en liaison");

    private Integer key;

    private String value;

    WriteCsvLangModel(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        WriteCsvLangModel[] values = WriteCsvLangModel.values();
        for (WriteCsvLangModel value : values) {
            if (value.getKey().equals(Integer.valueOf(key))) {
                return value.getValue();
            }
        }
        return "";
    }

    public static String getListStr(String lang,Integer type){
        if ("en_US".equals(lang)) {
            if (type.equals(ListTypeEnum.RADIUS.getValue())){
                return WriteCsvLangModel.RADIUSEN_US.getValue();
            }else if (type.equals(ListTypeEnum.VIRTUAL.getValue())){
                return WriteCsvLangModel.VIRTUALEN_US.getValue();
            }else if (type.equals(ListTypeEnum.WEB_SITE.getValue())){
                return WriteCsvLangModel.DOMAINEN_US.getValue();
            }else if (type.equals(ListTypeEnum.APP.getValue())){
                return WriteCsvLangModel.APPEN_US.getValue();
            }else if (type.equals(ListTypeEnum.PHONE.getValue())){
                return WriteCsvLangModel.PHONEEN_US.getValue();
            }else {
                return "";
            }
        } else if ("zh_CN".equals(lang)) {
            if (type.equals(ListTypeEnum.RADIUS.getValue())){
                return WriteCsvLangModel.RADIUSZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.VIRTUAL.getValue())){
                return WriteCsvLangModel.VIRTUALZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.WEB_SITE.getValue())){
                return WriteCsvLangModel.DOMAINZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.APP.getValue())){
                return WriteCsvLangModel.APPZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.PHONE.getValue())){
                return WriteCsvLangModel.PHONEZH_CN.getValue();
            }else {
                return "";
            }
        } else {
            //法国
            if (type.equals(ListTypeEnum.RADIUS.getValue())){
                return WriteCsvLangModel.RADIUSFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.VIRTUAL.getValue())){
                return WriteCsvLangModel.VIRTUALFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.WEB_SITE.getValue())){
                return WriteCsvLangModel.DOMAINFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.APP.getValue())){
                return WriteCsvLangModel.APPFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.PHONE.getValue())){
                return WriteCsvLangModel.PHONEFR_FR.getValue();
            }else {
                return "";
            }
        }
    }

    public static String getListStr(String lang,Integer type,Boolean flag){
        if ("en_US".equals(lang)) {
            if (type.equals(ListTypeEnum.RADIUS.getValue())){
                return WriteCsvLangModel.RADIUSEN_US.getValue();
            }else if (type.equals(ListTypeEnum.VIRTUAL.getValue())){
                return WriteCsvLangModel.VIRTUALEN_US.getValue();
            }else if (type.equals(ListTypeEnum.WEB_SITE.getValue())){
                if (flag){
                    return WriteCsvLangModel.DOMAINIPEN_US.getValue();
                }
                return WriteCsvLangModel.DOMAINEN_US.getValue();
            }else if (type.equals(ListTypeEnum.APP.getValue())){
                if (flag){
                    return WriteCsvLangModel.APPIPEN_US.getValue();
                }
                return WriteCsvLangModel.APPEN_US.getValue();
            }else if (type.equals(ListTypeEnum.PHONE.getValue())){
                return WriteCsvLangModel.PHONEEN_US.getValue();
            }else {
                return "";
            }
        } else if ("zh_CN".equals(lang)) {
            if (type.equals(ListTypeEnum.RADIUS.getValue())){
                return WriteCsvLangModel.RADIUSZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.VIRTUAL.getValue())){
                return WriteCsvLangModel.VIRTUALZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.WEB_SITE.getValue())){
                if (flag){
                    return WriteCsvLangModel.DOMAINIPZH_CN.getValue();
                }
                return WriteCsvLangModel.DOMAINZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.APP.getValue())){
                if (flag){
                    return WriteCsvLangModel.APPIPZH_CN.getValue();
                }
                return WriteCsvLangModel.APPZH_CN.getValue();
            }else if (type.equals(ListTypeEnum.PHONE.getValue())){
                return WriteCsvLangModel.PHONEZH_CN.getValue();
            }else {
                return "";
            }
        } else {
            //法国
            if (type.equals(ListTypeEnum.RADIUS.getValue())){
                return WriteCsvLangModel.RADIUSFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.VIRTUAL.getValue())){
                return WriteCsvLangModel.VIRTUALFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.WEB_SITE.getValue())){
                if (flag){
                    return WriteCsvLangModel.DOMAINIPFR_FR.getValue();
                }
                return WriteCsvLangModel.DOMAINFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.APP.getValue())){
                if (flag){
                    return WriteCsvLangModel.APPIPFR_FR.getValue();
                }
                return WriteCsvLangModel.APPFR_FR.getValue();
            }else if (type.equals(ListTypeEnum.PHONE.getValue())){
                return WriteCsvLangModel.PHONEFR_FR.getValue();
            }else {
                return "";
            }
        }
    }

}
