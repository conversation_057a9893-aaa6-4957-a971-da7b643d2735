package com.semptian.archives.web.service.common.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/6/18 16:56
 **/
@Slf4j
public class SmsDecodeUtil {


    public static String decode(String hexString) {
        try {
            // Convert hex string to byte array
            byte[] byteArray = hexStringToByteArray(hexString);

            // Decode byte array using UTF-8
            return new String(byteArray, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("decode error, the string is [{}]", hexString, e);
        }

        return hexString;
    }

    public static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }
}
