package com.semptian.archives.web.service.service;

import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.archives.web.dao.archive.entity.DrillDownResultEntity;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.DrillDownSceneEnum;
import com.semptian.archives.web.service.model.*;
import com.semptian.base.builders.QueryBuilder;

import java.util.List;
import java.util.Map;

/**
 * @author: SunQi
 * @create: 2021/06/02
 * desc: 全息档案通用service
 **/
public interface ArcCommonService {

    List<String> getEsResourceNameByArcTypeAndPermission(String arcType, List<Integer> permmisonList);

    List<ArcEsEntity> getArcInfoByIds(List<String> arcIdList, ArcTypeEnum arcTypeEnum);

    List<ArcEsEntity> getArcInfoByIds(List<String> arcIdList);

    ArcEsEntity getArcInfoById(String arcId, Integer arcType);

    ArcEsEntity getArcInfoByArcAccount(String arcAccount, Integer arcType, String arcAccountType);

    Map<String, Object> query(Integer onPage, Integer size, QueryBuilder queryBuilder, Map<String, String> sortFields, Integer archivesType, List<Integer> permmisonList);

    Map<String, Object> query(Integer onPage, Integer size, QueryBuilder queryBuilder, Map<String, String> sortFields, String archivesTypes, List<Integer> permmisonList);

    /**
     * 业务通用查询方法,返回值为list
     *
     * @param serviceCode 业务码
     * @param params      sql参数
     * @return
     */
    List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params);

    /**
     * 推荐使用此方法进行分页排序处理
     *
     * @param serviceCode
     * @param params
     * @param pageWarpEntity
     * @return
     */
    List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, PageWarpEntity pageWarpEntity);

    /**
     * 分页查询排序
     *
     * @param serviceCode
     * @param params
     * @param pageWarpEntity
     * @return
     */
    PageResultModel getCommonServicePageResult(String serviceCode, Map<String, Object> params, PageWarpEntity pageWarpEntity);

    List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needSqlEscape);

    List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Integer onPage, Integer size);

    List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Integer onPage, Integer size, String sortCondition);

    List<Map<String, Object>> getCommonServiceListResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Integer onPage, Integer size, Boolean needSqlEscape, String sortCondition);

    /**
     * 业务通用查询方法,返回值为count值, 默认不自动包装
     *
     * @param serviceCode
     * @param params
     * @return
     */
    Long getCommonServiceCountResult(String serviceCode, Map<String, Object> params);

    Double getCommonServiceSumResult(String serviceCode, Map<String, Object> params);

    Long getCommonServiceCountResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql);

    Long getCommonServiceCountResult(String serviceCode, Map<String, Object> params, Boolean needWarpPageSql, Boolean needSqlEscape);

    /**
     * 根据档案id,接口方法名称,当前小时的开始时间以及可变参数创建redisKey
     *
     * @param arcId
     * @param methodName 接口方法名称
     * @param objects
     * @return
     */
    String getRedisKey(String arcId, String methodName, Object... objects);

    /**
     * 根据档案id,接口方法名称,当前小时的开始时间以及可变参数创建redisKey
     *
     * @param arcId
     * @param methodName 接口方法名称
     * @param objects
     * @return
     */
    String getRedisKeyByDay(String arcId, String methodName, Object... objects);

    /**
     * 根据接口方法名称,当前小时的开始时间以及可变参数创建redisKey
     *
     * @param methodName
     * @param objects
     * @return
     */
    String getRedisKeys(String methodName, Object... objects);


    /**
     * 通过档案首页数据更新动态控制全部档案、档案首页统计
     */
    String getRedisKeyByIndexCount(Boolean containToday, Object... objects);

    /**
     * 数据下钻明细表头列
     *
     * @param dataType
     * @param isAll
     * @param drillDownSceneEnum
     * @param lang               语言
     * @param isDisplay          查询结果是否用于页面展示或者导出
     * @return Object
     */
    List<Map<String, Object>> getHeaders(Integer dataType, Integer isAll, DrillDownSceneEnum drillDownSceneEnum, String lang, boolean isDisplay);

    List<Map<String, Object>> getHeaders(Integer dataType, Integer isAll, DrillDownSceneEnum drillDownSceneEnum, String lang, boolean isDisplay,
                                         Integer isCustomField, String userId);

    /**
     * 数据下钻明细
     *
     * @param drillDownModel
     * @return
     */
    DrillDownResultEntity getDrillDownDataDetail(DrillDownModel drillDownModel);

    DrillDownResultEntity getDrillDownDataDetail(DrillDownModel drillDownModel, String userId);

    /**
     * 明细数据趋势计算
     *
     * @param trendAndDistributeModel lis或nf明细数据趋势
     * @return
     */
    Object getTrend(TrendAndDistributeModel trendAndDistributeModel);

    /**
     * 明细数据分布计算
     *
     * @param trendAndDistributeModel lis或nf明细数据趋势
     * @return
     */
    Object getDistribution(TrendAndDistributeModel trendAndDistributeModel);

    /**
     * 明细数据分布计算
     *
     * @param vpnTrendModel 是否分时
     * @return Object vpn使用排名
     */
    Object vpnUseRank(VpnTrendModel vpnTrendModel);

    /**
     * 明细数据分布计算
     *
     * @param vpnTrendModel vpn参数
     * @return Object vpn使用趋势
     */
    Object vpnUseDayTrend(VpnTrendModel vpnTrendModel);

    /**
     * 明细数据分布计算
     *
     * @param vpnTrendModel 小时
     * @return Object vpn使用趋势
     */
    Object vpnUseHourTrend(VpnTrendModel vpnTrendModel);

    /**
     * 高频词排名
     *
     * @param highFrequencyWordsModel 高频词排名
     * @return Object 高频词排名
     */
    Object getHighFrequencyWordsRank(HighFrequencyWordsModel highFrequencyWordsModel);

    /**
     * 高频词趋势
     *
     * @param highFrequencyWordsModel 高频词排名
     * @return Object 高频词趋势
     */
    Object getHighFrequencyWordsTrend(HighFrequencyWordsModel highFrequencyWordsModel);

    /**
     * 银行卡信息
     *
     * @param highFrequencyWordsModel 档案id
     * @return Object 银行卡信息
     */
    Object getBankCardInfo(HighFrequencyWordsModel highFrequencyWordsModel);

    /**
     * 航班信息
     *
     * @param highFrequencyWordsModel 档案id
     * @return Object 航班信息
     */
    Object getFlightInfo(HighFrequencyWordsModel highFrequencyWordsModel);

    /**
     * 档案更新别名信息
     *
     * @param arcId   档案id
     * @param arcType 档案类型
     * @param name    名称
     * @return Object 档案基础信息
     */
    Object arcUpdateBasicInfo(String userId, String arcId, Integer arcType, String name);

    /**
     * 根据协议分类获取所有协议类型
     *
     * @param type 1=LIS相关协议 2=NF相关协议
     * @return 协议类型列表
     */
    Object getProtocolList(Integer type, String lang);

    /**
     * 判断档案账号是否为重要目标
     *
     * @param arcType    档案类型
     * @param arcAccount 档案账号
     * @return 档案为重要目标，则返回重要目标名称和分类
     */
    Object isImportantTarget(Integer arcType, String arcAccount);

    /**
     * 获取NF数据动作类型分布
     *
     * @param trendAndDistributeModel 分布相关入参
     * @return NF数据动作类型分布
     */

    Object getNfActionDistribution(TrendAndDistributeModel trendAndDistributeModel);

    Object authRecord(String keyword, String type, String virtualAccount, String virtualAppType, String lang, DateModel dateModel, PageWarpEntity pageWarpEntity);

    Object getDataTypeCount(String arcAccount, DateModel dateModel, String lang);

    /**
     * 获取档案标签TOP列表
     *
     * @param arcType    档案类型
     * @param arcAccount 档案账号
     * @return 档案标签TOP列表
     */
    Object getTagTopList(Integer arcType, String arcAccount, String lang);

    /**
     * 获取档案标签列表
     *
     * @return 档案标签列表
     */
    Object getTagList(Integer arcType, String arcAccount, String keyword, DateModel dateModel, PageWarpEntity pageWarpEntity, String lang);


    boolean saveCustomColumns(CustomColumnsModel columns, String userId);
}
