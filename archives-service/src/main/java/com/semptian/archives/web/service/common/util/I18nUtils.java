package com.semptian.archives.web.service.common.util;

import com.semptian.archives.web.service.common.handler.MessageSourceHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 国际化消息公共类
 * <AUTHOR>
 * @date 2020/9/1
 */
@Component
public class I18nUtils {
    private static MessageSourceHandler handler;

    @Autowired
    public I18nUtils(MessageSourceHandler handler) {
        I18nUtils.handler = handler;
    }

    /**
     * 根据Key取国际化的值
     * @param messageKey
     * @return String
     * <AUTHOR>
     * @date 2020/9/1
     */
    public static String getMessage(String messageKey) {
        String message = handler.getMessage(messageKey);
        return message;
    }
}
