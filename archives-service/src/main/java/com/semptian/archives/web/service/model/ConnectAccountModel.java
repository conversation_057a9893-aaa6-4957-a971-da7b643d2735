package com.semptian.archives.web.service.model;

import lombok.Data;

import java.util.Map;

/**
 * --lmz
 */

@Data
public class ConnectAccountModel {


//    /**
//     * 档案id
//     */
//    private String id;
//
//    /**
//     * 通信类型，2表示通话，3表示短信，1表示传真，0表示全部，默认全部
//     */
//
//    private int callTag;
//
//    /**
//     * 号码类型，1表示接听号码，2表示呼出号码，3表示未接号码吧，0表示全部，默认全部
//     */
//    private int callType;

    /**
     * 号码账号
     */
    private String phone;

    /**
     * 关联最多号码
     */
//    private String maxRelationalPhone;

    /**
     * 关联号码个数
     */
//    private int phoneNum;

    /**
     * imsi账号
     */
//    private String imsi;

    /**
     * 户籍所在地/注册地址
     */
    private String registeredAddress;

    /**
     * 最近关联时间
     */
    private Long lastRelationTime;

    /**
     * 最小最近关联时间
     */
//    private Long maxLastRelationTime;

    /**
     * 最早关联时间
     */
    private Long earliestRelationTime;

    /**
     * 最大最早关联时间
     */
//    private Long minEarliestRelationTime;

//    /**
//     * 关联天数= 当前时间-捕获年月份capture_month
//     */
//    private int relationDay;

    /**
     * 关联次数
     */
     private int behaviorNum;

    /**
     * 最大关联次数
     */
//    private int maxBehaviorNum;

    /**
     * 关联天数
     */
    private int activeDayNum;

    /**
     * 最大关联天数
     */
//    private int maxActiveDayNum;


    /**
     * 关联账号明细
     */
    private Map<String,Object> arcInfoModel;

    /**
     * 协议类型
     */
    private int dataType;

    /**
     * start_time---end_time
     */
    private String interval;
}
