package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 领域档案首页服务
 * <AUTHOR>
 */
@Service
public class DomainArcHomePageService extends AbstractArcHomePageService {

    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.WEB_SITE;
    }

    @Override
    protected Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes) {
        return Collections.singletonMap("domain", authAccounts);
    }

    @Override
    protected Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        return new HashMap<>(DateUtils.getPast7Day());
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        String arcAccountType = ctx.getArcAccountType();
        if (StringUtils.isNotBlank(arcAccountType)) {
            params.put("arc_account_type", "AND app_type=" + "'" + EscapeUtil.escapeSingleQuote(arcAccountType) + "'");
        } else {
            params.put("arc_account_type", "");
        }
    }

    @Override
    protected void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.put("domain", EscapeUtil.escapeSingleQuote(ctx.getDomain()));
    }

    @Override
    protected String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        switch (accessTargetTopEnum) {
            case IP:
                return BusinessCodeEnum.DOMAIN_IP_TARGET_TOP.getValue();
            case IMPORTANT:
                return BusinessCodeEnum.DOMAIN_IMPORTANT_TARGET_TOP.getValue();
            default:
                return BusinessCodeEnum.DOMAIN_ARC_TARGET_TOP.getValue();
        }
    }

    @Override
    public String activeTrendTemplate(ArcContext ctx) {
        return BusinessCodeEnum.COMMON_DOMAIN_DAY_TREND_ANALYZE.getValue();
    }

    @Override
    public String periodStatisticsTemplate() {
        return BusinessCodeEnum.COMMON_DOMAIN_DAY_HOUR_ANALYZE.getValue();
    }

    @Override
    public String communicationAreaTemplate() {
        return BusinessCodeEnum.COMMON_DOMAIN_DAY_COMMUNICATION_AREA.getValue();
    }
}
