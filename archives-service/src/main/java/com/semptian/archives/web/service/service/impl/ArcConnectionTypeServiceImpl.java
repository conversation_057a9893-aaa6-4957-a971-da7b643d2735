package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.archives.web.dao.archive.entity.ArcConnectionTypeEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcConnectionTypeMapper;
import com.semptian.archives.web.service.service.ArcConnectionTypeService;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 档案关系服务实现类
 * <AUTHOR>
 * @date 2024/3/26
 */
@Slf4j
@Service
public class ArcConnectionTypeServiceImpl extends ServiceImpl<ArcConnectionTypeMapper, ArcConnectionTypeEntity> implements ArcConnectionTypeService {

    @Value("${spring.redis.cache-prefix}")
    String redisKeyPrefix;
    @Resource
    private RedisOps redisOps;

    private String getRedisKey(String srcArcId, String destArcId) {
        return redisKeyPrefix+"ArcConnectionType:"+ srcArcId+destArcId;
    }

    @PostConstruct
    public void init() {
        log.info("ArcConnectionType init");
        try {
            this.getBaseMapper().selectList(null).forEach(arcConnectionTypeEntity -> {
                String redisKey1 = getRedisKey(arcConnectionTypeEntity.getSrcArcId(), arcConnectionTypeEntity.getTargetArcId());
                String redisKey2 = getRedisKey(arcConnectionTypeEntity.getTargetArcId(), arcConnectionTypeEntity.getSrcArcId());
                redisOps.set(redisKey1, arcConnectionTypeEntity.getConnectionType());
                redisOps.set(redisKey2, arcConnectionTypeEntity.getConnectionType());
            });
        } catch (Exception e) {
            log.error("ArcNickNameServiceImpl init error", e);
        }
    }

    public void updateConnectionTypeCache(String srcArcId, String destArcId, String connectionType) {
        String redisKey1 = getRedisKey(srcArcId, destArcId);
        String redisKey2 = getRedisKey(destArcId, srcArcId);

        if (StringUtils.isBlank(connectionType)) {
            redisOps.del(redisKey1);
            redisOps.del(redisKey2);
        } else {
            redisOps.set(redisKey1, connectionType);
            redisOps.set(redisKey2, connectionType);
        }
    }

    public String getArcConnectionTypeByArcId(String srcArcId, String destArcId) {

        String redisKey = getRedisKey(srcArcId, destArcId);

        Object result = redisOps.get(redisKey);

        if (result != null && StringUtils.isNotBlank(result.toString())) {
            return result.toString();
        } else {
            // 这里不建议查询 db 性能太差
            return "";
        }
    }
}
