package com.semptian.archives.web.service.service.arc.app;

import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.model.RadiusAppTimeListModel;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.ArcFeatures;
import com.semptian.archives.web.service.service.arc.ArcFunctional;

import java.util.List;
import java.util.Map;

/**
 * 档案应用分析服务接口
 * <AUTHOR>
 */
public interface ArcAppAnalysisService extends ArcFunctional {

    /**
     * 该方法定义对应子类提供应用分析的能力
     *
     * @return 能力集枚举
     */
    @Override
    default ArcFeatures feature() {
        return ArcFeatures.APP_ANALYSIS;
    }

    /**
     * 应用分布
     */
    Map<String, Object> getAppImgTable(ArcContext ctx);

    /**
     * 应用时段分析
     */
    List<RadiusAppTimeListModel> getAppTimeInfo(ArcContext ctx);

    /**
     * 应用列表
     */
    JSONObject getAppList(ArcContext ctx);
}
