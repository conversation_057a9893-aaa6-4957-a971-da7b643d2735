package com.semptian.archives.web.service.common.enums;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2021/07/30
 * desc: Call协议动作枚举类
 **/
public enum VoipActionEnum {
    AV("20", "语音视频通信"),
    AV_START("21", "开始语音视频"),
    AV_STOP("22", "停止语音视频"),
    OTHER("99", "其他");
    private String key;
    private String value;

    VoipActionEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        VoipActionEnum[] values = VoipActionEnum.values();
        for (VoipActionEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }
    }
