package com.semptian.archives.web.service.common.util;


import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.regex.Pattern;

/**
 * 描述：电话号码归属地国家简码获取
 * 作者：Chongyike
 * 日期：2021/8/4 17:53.
 * <AUTHOR>
 */
@Slf4j
public class DeyeGetCountryCodeByPhoneNumber implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final String NUMBER_REGEX = "^[0-9]+";
    private static final String ZERO_PREFIX_REGEX = "^[0]+";

    private static final Pattern NUMBER_REGEX_PATTERN = Pattern.compile(NUMBER_REGEX);
    private static final Pattern ZERO_PREFIX_REGEX_PATTERN = Pattern.compile(ZERO_PREFIX_REGEX);

    // 根据电话号码中的国际区号来获取归属地
    public static String getCountryNameByPhoneNumber(String phoneNumber) {
        try {
            if (phoneNumber == null || phoneNumber.isEmpty() || !NUMBER_REGEX_PATTERN.matcher(phoneNumber).matches()) {
                return "";
            }

            String newPhoneNumber = phoneNumber;
            // 去除前导0
            if (phoneNumber.startsWith("0")) {
                newPhoneNumber = ZERO_PREFIX_REGEX_PATTERN.matcher(phoneNumber).replaceAll("");
            }

            // 获取iso country code
            return PhoneAreaUtils.getIsoCountryCode(newPhoneNumber);
        }catch (Exception e) {
            log.error("parse phoneNumber error:{}", phoneNumber, e);
        }

        return "";
    }
}
