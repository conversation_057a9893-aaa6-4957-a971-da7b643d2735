package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 群组挖掘查询条件model
 * @date 2021/6/18
 */
@Data
@ApiModel("群组挖掘查询条件model")
public class GroupMiningQueryParam implements Serializable {

    @ApiModelProperty(value = "资源库类型", allowableValues = "call, voip, email", required = true)
    private String resourceType;

    @ApiModelProperty(value = "对应各个协议的from字段", required = true)
    private String from;

    @ApiModelProperty(value = "对应各个协议的to字段", required = true)
    private String to;

    @ApiModelProperty(value = "开始时间戳，单位毫秒", required = true)
    private String startTimeStr;

    @ApiModelProperty(value = "结束时间戳，单位毫秒", required = true)
    private String endTimeStr;

    @ApiModelProperty(value = "过滤特殊号码")
    private List<String> specialNums;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "查询页数")
    private Integer onPage=1;

    @ApiModelProperty(value = "每页数量")
    private Integer size=20;
}
