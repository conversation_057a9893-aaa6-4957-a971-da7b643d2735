package com.semptian.archives.web.service.service.arc.ip;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ProtocolArcServerIpService extends AbstractArcServerIpService {

    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.NET_PROTOCOL;
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        params.put("app_name", EscapeUtil.escapeSingleQuote(ctx.getAppName()));
        params.put("app_type", PROTOCOL_APP_TYPE);
    }


    /**
     * @param ctx 查询上下文
     * @return 返回不同visitorType对应的查询模板
     */
    @Override
    public String serverIpListTemplate(ArcContext ctx) {

        return BusinessCodeEnum.APP_SERVER_IP_LIST.getValue();
    }
}
