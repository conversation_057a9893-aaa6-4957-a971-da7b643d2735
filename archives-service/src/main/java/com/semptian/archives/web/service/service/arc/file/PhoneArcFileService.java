package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Service;

/**
 * phone档案文件服务实现类
 *
 * <AUTHOR>
 * @date 2024/3/21
 */
@Service
public class PhoneArcFileService extends AbstractArcFileService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.PHONE;
    }

    @Override
    protected void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx) {
        params.put("account_type_condition", " auth_type = " + AuthTypeEnum.PHONE.getType() + " and auth_account ");
        params.put("dataType", "");
    }
}
