package com.semptian.archives.web.service.service;

import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;

/**
 * @author: SunQi
 * @create: 2021/03/26
 * desc:
 **/
public interface ArcAttachService {
    /**
     * 获取附件来源信息
     *
     * @param archiveType    档案类型
     * @param arcAccount     认证账号
     * @param arcAccountType 账号类型
     * @param dataType       协议编码
     * @param attachMd5      附件MD5值
     * @param createDay      固定IP档案建档日期
     * @param dateModel      日期模型
     * @param pageWarpEntity 分页信息
     * @return 附件来源信息
     */
    Object getAttachSourceInfo(Integer archiveType, String arcAccount, String arcAccountType, Integer dataType, String attachMd5, String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity, String lang);
}
