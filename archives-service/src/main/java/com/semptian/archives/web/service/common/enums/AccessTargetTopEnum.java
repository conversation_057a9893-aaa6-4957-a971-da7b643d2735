package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 档案首页访问top枚举
 * <AUTHOR>
 * @date 2024/1/22
 */
@Getter
public enum AccessTargetTopEnum {

    ARC("topArcTargets", "档案访问TOP"),

    IP("topIpTargets", "IP访问TOP"),

    IMPORTANT("topImportantTargets", "重要目标访问TOP"),

    VIRTUAL_ACCOUNT("account", "虚拟账号访问TOP"),

    APP("app", "应用访问TOP"),

    PHONE("phone", "通联号码访问TOP"),

    EMAIL("emails", "常通联邮箱TOP");

    private final String value;

    private final String desc;

    AccessTargetTopEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
