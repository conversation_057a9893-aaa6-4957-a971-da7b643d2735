package com.semptian.archives.web.service.service.arc.virtualaccount;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.model.CountDataTypeModel;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public abstract class AbstractArcVirtualAccountService implements ArcVirtualAccountService {

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private ArcDrillDownFieldTranslateConfigService arcDrillDownFieldTranslateConfigService;


    @Override
    public List<CountDataTypeModel> countDataType(ArcContext ctx) {

        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam();
        params.put("start_day", ctx.getStartDay());
        params.put("end_day", ctx.getEndDay());

        //构造档案特定的参数
        buildArcSpecifiedParams(params, ctx);

        String countTemplate = countDataTypeTemplate(ctx);

        List<Map<String, Object>> dataTypeList = arcCommonService.getCommonServiceListResult(countTemplate, params);

        List<CountDataTypeModel> countDataTypeModelList = new ArrayList<>();
        if (dataTypeList != null && !dataTypeList.isEmpty()) {
            countDataTypeModelList = JSON.parseArray(JSON.toJSONString(dataTypeList), CountDataTypeModel.class);
        }

        countDataTypeModelList.forEach(countDataTypeModel -> {
            if (ObjectUtil.isNotNull(countDataTypeModel.getDataType())) {
                countDataTypeModel.setName(arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, countDataTypeModel.getDataType(), ctx.getLang()).toString());
            }
        });
        int total = 0;
        if (!countDataTypeModelList.isEmpty()) {
            total = countDataTypeModelList.stream().mapToInt(CountDataTypeModel::getCount).sum();
            CountDataTypeModel countDataTypeModel = new CountDataTypeModel(0, "all", total);
            //添加总数
            countDataTypeModelList.add(0, countDataTypeModel);
        }

        return countDataTypeModelList;
    }

    protected abstract String countDataTypeTemplate(ArcContext ctx);

    protected abstract void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx);

}
