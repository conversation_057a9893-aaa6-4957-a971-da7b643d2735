package com.semptian.archives.web.service.common.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 2024/1/9
 */
@Component
@ConfigurationProperties(prefix = "es.resource.name")
@Data
public class EsResourceNameConfig {

    private String website;

    private String app;

    private String fixIp;

    private String phone;

    private String radius;

    private String email;

    private String im;

    private String protocol;

    private String mobilenetradius;

    private String location;
}
