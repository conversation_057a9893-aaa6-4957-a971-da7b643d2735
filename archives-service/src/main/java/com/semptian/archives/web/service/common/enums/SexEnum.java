package com.semptian.archives.web.service.common.enums;


import lombok.Getter;

/**
 * 性别枚举
 * <AUTHOR>
 * @date 2024/3/29
 */
@Getter
public enum SexEnum {

    MALE(1, "male"),

    Female(2, "female");

    private final Integer code;

    private final String description;

    SexEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getDescByCode(Integer code) {
        SexEnum[] values = SexEnum.values();
        for (SexEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return "";
    }
}
