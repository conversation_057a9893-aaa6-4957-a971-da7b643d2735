package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.archives.web.dao.archive.entity.ArcCollectionEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcCollectionMapper;
import com.semptian.archives.web.service.service.ArcCollectionService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ArcCollectionServiceImpl extends ServiceImpl<ArcCollectionMapper, ArcCollectionEntity> implements ArcCollectionService {

    @Override
    public List<ArcCollectionEntity> queryCareArcWithArcType(Long userId, String arcType) {
        return baseMapper.queryCareArcWithArcType(userId, arcType);
    }
}
