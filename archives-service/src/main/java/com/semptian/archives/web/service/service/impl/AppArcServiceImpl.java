package com.semptian.archives.web.service.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.AppArcService;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: SunQi
 * @create: 2021/06/08
 * desc:
 **/
@Service
@Slf4j
public class AppArcServiceImpl implements AppArcService {

    @Resource
    private ArcCommonService arcCommonService;
    @Resource
    private RedisOps redisOps;

    @Override
    public Object getAppTypeInfo() {
        String redisKey = arcCommonService.getRedisKey("getAppTypeInfo", "appTypes");

        if (redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return redisOps.get(redisKey);
        }

        HashMap<String, Object> paramMap = new HashMap<>();

        JSONArray res = new JSONArray();

        List<Map<String, Object>> appTypes = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.COMMON_APP_TYPE_LIST.getValue(), paramMap);

        for (Map<String, Object> target : appTypes) {
            JSONObject t = new JSONObject();
            String appType = (String) target.get("appType");

            if (StringUtils.isNotEmpty(appType)) {
                t.put("id", appType);
                t.put("label", appType);
                res.add(t);
            }
        }

        redisOps.set(redisKey, res, DateUtils.getRedisExpireTime());
        return res;
    }
}
