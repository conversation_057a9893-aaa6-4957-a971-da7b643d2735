package com.semptian.archives.web.service.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2021/01/30
 * @Des 结果页定制
 */
@Data
public class ArchiveResultTableColumnModel implements Comparable<ArchiveResultTableColumnModel>{
    @ApiModelProperty(value = "字段ID")
    private String id;
    @ApiModelProperty(value = "字段名称")
    private String fieldName;
    @ApiModelProperty(value = "字段别名")
    private String fieldAlias;
    @ApiModelProperty(value = "是否选择")
    private boolean selected;
    @ApiModelProperty(value = "排序")
    private int displayLevel;

    /**
     * 显示标签
     * 0 没有选择;
     * 1 标题;
     * 2 摘要;
     * 3 时间;
     * 4 快照路径;
     * 5 ip;
     * 6 邮箱;
     * 7 账号
     */
    @ApiModelProperty(value = "displayTag")
    private String displayTag;

    @Override
    public int compareTo(ArchiveResultTableColumnModel o) {
        return Integer.valueOf(displayLevel).compareTo(Integer.valueOf(o.getDisplayLevel()));
    }
}
