package com.semptian.archives.web.service.service.arc.app;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 号码档案的应用分析服务
 * <AUTHOR>
 */
@Service
public class PhoneArcAppAnalysisService extends AbstractArcAppAnalysisService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.PHONE;
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        params.put("auth_account", ctx.getArcAccount().replaceAll("\\+", "").replaceAll("-", ""));

        params.put("auth_account_type", AuthTypeEnum.PHONE.getType());

        if (StringUtils.isNotEmpty(ctx.getKeyWord())) {
            params.put("key_word", " AND ( lower(app_name) LIKE '%" + ctx.getKeyWord().toLowerCase() + "%'" + " OR lower(app_type) LIKE '%" + ctx.getKeyWord().toLowerCase() + "%')");
        } else {
            params.put("key_word", "");
        }
    }


    /**
     * @param ctx 查询上下文
     * @return 返回不同visitorType对应的查询模板
     */
    @Override
    public String appAnalyzeTemplate(ArcContext ctx, String type) {
        if (ctx.getIsBlock()) {
            if (PIE_CHART.equals(type)) {
                return BusinessCodeEnum.PHONE_QUERY_BLOCK_APP_TABLE_PIE_INFO.getValue();
            } else if (LINE_CHART.equals(type)) {
                return BusinessCodeEnum.PHONE_QUERY_BLOCK_APP_TABLE_LINE_CHART_INFO.getValue();
            }
        } else {
            if (PIE_CHART.equals(type)) {
                return BusinessCodeEnum.PHONE_QUERY_APP_TABLE_PIE_INFO.getValue();
            } else if (LINE_CHART.equals(type)) {
                return BusinessCodeEnum.PHONE_QUERY_APP_TABLE_LINE_CHART_INFO.getValue();
            }
        }
        return "";
    }

    @Override
    protected String appPeriodTemplate(ArcContext ctx) {
        if (ctx.getIsBlock()) {
            return BusinessCodeEnum.PHONE_QUERY_APP_TIME_BY_BLOCK_DAY_INFO.getValue();
        } else {
            return BusinessCodeEnum.PHONE_QUERY_APP_TIME_DAY_INFO.getValue();
        }
    }

    @Override
    protected String appListTemplate(ArcContext ctx) {
        if (ctx.getIsBlock()) {
            return BusinessCodeEnum.PHONE_QUERY_APP_LIST_BY_BLOCK_DAY_INFO.getValue();
        } else {
            return BusinessCodeEnum.PHONE_QUERY_APP_LIST_DAY_INFO.getValue();
        }
    }
}
