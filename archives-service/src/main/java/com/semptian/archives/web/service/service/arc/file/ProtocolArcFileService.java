package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class ProtocolArcFileService extends AbstractArcFileService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.NET_PROTOCOL;
    }

    @Override
    protected void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx) {
        String account;
        if (StringUtils.isNotBlank(ctx.getArcAccountType())) {
            account = " app_type='" + PROTOCOL_APP_TYPE + "' and app_name";
        } else {
            account = "app_type='' and  app_name";
        }
        params.put("account_type_condition",account);
        params.put("dataType", "");
    }
}
