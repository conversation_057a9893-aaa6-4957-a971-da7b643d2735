package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.dao.archive.entity.ArchivePhoneOwnerInfoEntity;
import com.semptian.archives.web.dao.archive.mapper.ArchivePhoneOwnerInfoMapper;
import com.semptian.archives.web.service.common.config.EsResourceNameConfig;
import com.semptian.archives.web.service.common.config.PhoneOperatorConfig;
import com.semptian.archives.web.service.common.config.TianheRequestConfig;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.common.util.MapToObjUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.base.model.TianheRequestModel;
import com.semptian.base.model.TianheReturnModel;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.service.access.TianheDataRequestService;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.semptian.archives.web.core.common.constent.CommonConstent.*;

/**
 * <AUTHOR>
 * @date 2021/6/3 10:47
 **/
@Slf4j
@Service
public class PhoneArcServiceImpl {

    private static final String NETWORK_TYPE_FIELD = "network_type";
    /**
     * 实时轨迹展示条数
     */
    @Value("${archive.realtime.trajectory.size:1000}")
    private Long realtimeTrajectorySize;

    @Value("${archive.hisTrajectory.stayNumLimit:3}")
    private Integer stayNumLimit;

    @Resource
    ArcCommonServiceImpl arcCommonService;

    @Resource
    private RedisOps redisOps;

    @Resource
    TianheRequestConfig tianheRequestConfig;

    @Resource
    TianheDataRequestService tianheDataRequestService;

    @Resource
    private EsResourceNameConfig esResourceNameConfig;

    @Resource
    private ArchivePhoneOwnerInfoMapper archivePhoneOwnerInfoMapper;

    @Resource
    private ArcDrillDownFieldTranslateConfigService arcDrillDownFieldTranslateConfigService;

    @Resource
    PhoneOperatorConfig phoneOperatorConfig;

    @Value("${archive.dbName.dws:dws}")
    private String dwsDbName;

    /**
     * 通联归属区域
     *
     * @param arcAccount
     * @return
     */
    public Object getCommunicationArea(String arcAccount, DateModel dateModel, String lang) {
        String redisKey = arcCommonService.getRedisKeyByDay(arcAccount, dateModel.getStartDay(), dateModel.getEndDay(), lang);
        //TODO 存在bug 后续解决
//        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
//            return redisOps.get(redisKey);
//        }

        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam(dateModel);
        params.put("arcAccount", arcAccount);

        List<Map<String, Object>> phoneTopNList = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_CONNECT_AREA_BELONG.getValue(), params);
        // 返回结构为
        // src_attribution|connected_attribution|total|
        // china          |ame                  |    2|
        // 遍历 phoneTopNList, 进行如下处理
        // 1 total 进行累加, 作为总数
        // 2 将 connected_attribution 作为 key, total 作为 value, 存入 map 中

        JSONObject data = new JSONObject();

        int srcAttributionTotal = 0;
        String srcAttribution = "";
        ArrayList<HashMap<String, Object>> lists = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(phoneTopNList)) {
            for (Map<String, Object> stringObjectMap : phoneTopNList) {
                srcAttributionTotal += Integer.parseInt(stringObjectMap.getOrDefault("total", 0).toString());
                srcAttribution = stringObjectMap.getOrDefault("src_attribution", "").toString();

                HashMap<String, Object> dst = new HashMap<String, Object>() {{
                    Object connectedAttribution = stringObjectMap.getOrDefault("connected_attribution", "");
                    put("name", arcDrillDownFieldTranslateConfigService.fieldTranslate(COUNTRY_CODE_FIELD, connectedAttribution, lang));
                    put("num", stringObjectMap.getOrDefault("total", 0));
                }};

                lists.add(dst);
            }
        }

        HashMap<String, Object> src = new HashMap<>();
        src.put("name", arcDrillDownFieldTranslateConfigService.fieldTranslate(COUNTRY_CODE_FIELD, srcAttribution, lang));
        src.put("num", srcAttributionTotal);
        data.put("src", src);
        data.put("dsts", lists);

        if (StringUtils.isNotEmpty(redisKey) && CollectionUtil.isNotEmpty(phoneTopNList)) {
            redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        }

        return data;
    }

    public static byte[] hexString2Byte(String hex) {
        int len = (hex.length() / 2);
        byte[] result = new byte[len];
        char[] charArr = hex.toCharArray();
        for (int i = 0; i < len; i++) {
            int pos = i * 2;
            result[i] = (byte) (toByte(charArr[pos]) << 4 | toByte(charArr[pos + 1]) & 0xff);
        }
        return result;
    }

    private static byte toByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    public Object getPhoneConnectAreaDetailInfo(String phoneNumber, String country, String startDay, String endDay, Integer onPage, Integer size) {

        String redisKey = arcCommonService.getRedisKey("getPhoneConnectAreaDetailInfo", phoneNumber, country, startDay, endDay, onPage, size);
        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return redisOps.get(redisKey);
        }
        Map<String, Object> paramsMap = new HashMap<>(16);
        paramsMap.put("phoneNumber", phoneNumber);
        paramsMap.put("country", country);
        paramsMap.put("startDay", startDay);
        paramsMap.put("endDay", endDay);
        //查询分页数据
        List<Map<String, Object>> connectDetailList = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_CONNECT_AREA_DETAIL_INFO.getValue(), paramsMap, true, onPage, size);
        //查询总数
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_CONNECT_AREA_DETAIL_INFO.getValue(), paramsMap, true);
        if (ObjectUtil.isNull(total) || total < 0L) {
            total = 0L;
        }
        //model封装
        List<PhoneConnectDetailModel> phoneConnectDetailModels = JSONArray.parseArray(JSON.toJSONString(connectDetailList), PhoneConnectDetailModel.class);
        if (phoneConnectDetailModels != null && phoneConnectDetailModels.size() > 0) {
            for (PhoneConnectDetailModel phoneConnectDetailModel : phoneConnectDetailModels) {
                if (StringUtils.isNotBlank(phoneConnectDetailModel.getSmsText())) {
                    phoneConnectDetailModel.setSmsText(new String(hexString2Byte(phoneConnectDetailModel.getSmsText().toUpperCase())));
                }
                phoneConnectDetailModel.setAction(CallActionEnum.getByKey(phoneConnectDetailModel.getAction()));
                phoneConnectDetailModel.setCallTag(CallTagEnum.getByKey(phoneConnectDetailModel.getCallTag()));
            }
        }
        Map<String, Object> result = new HashMap<>(16);
        result.put("list", phoneConnectDetailModels);
        result.put("total", total);
        return result;
    }


    /**
     * 手机号码动态参数构建
     *
     * @param phone
     * @param imsi
     * @param type  1 代表 包含来源和目标的参数构建 2 来源查询参数构建 3 目标查询参数构建
     * @return
     */
    private static String phoneQueryConditionBuild(String phone, String imsi, String type) {
        String queryCondtion = "";
        String phoneCondition = "";
        String imsiCondtion = "";


        if (StringUtils.isNotBlank(phone)) {
            if ("1".equals(type)) {
                phoneCondition = "(src_number = '" + phone + "'  OR dst_number = '" + phone + "')";
            } else if ("2".equals(type)) {
                phoneCondition = "src_number = '" + phone + "'";
            } else if ("3".equals(type)) {
                phoneCondition = "dst_number  = '" + phone + "'";
            } else if ("4".equals(type)) {
                phoneCondition = "(src_number = '" + phone + "'  OR dst_number = '" + phone + "')";
            }
        }

        if (StringUtils.isNotBlank(imsi)) {
            if ("1".equals(type)) {
                imsiCondtion = "(imsi = '" + imsi + "' OR called_imsi = '" + imsi + "')";
            } else if ("2".equals(type)) {
                imsiCondtion = "imsi = '" + imsi + "'";
            } else if ("3".equals(type)) {
                imsiCondtion = "called_imsi = '" + imsi + "'";
            } else if ("4".equals(type)) {
                imsiCondtion = "(imsi = '" + imsi + "' OR called_imsi = '" + imsi + "')";
            }
        }


        if (StringUtils.isNotBlank(phone) && StringUtils.isNotBlank(imsi)) {
            queryCondtion = "(" + phoneCondition + " OR " + imsiCondtion + ")";
        } else if (StringUtils.isBlank(phone) && StringUtils.isNotBlank(imsi)) {
            queryCondtion = imsiCondtion;
        } else if (StringUtils.isNotBlank(phone) && StringUtils.isBlank(imsi)) {
            queryCondtion = phoneCondition;
        }

        return queryCondtion;
    }

    public Object connectAreaBelong(String arcAccount, DateModel dateModel, PageWarpEntity pageWarpEntity, String lang) {
        //设置缓存
        String methodName = BusinessCodeEnum.PHONE_CONNECT_AREA_BELONG_DETAIL.getValue();
        String redisKey = arcCommonService.getRedisKeyByDay(methodName, arcAccount, dateModel.getStartDay(), dateModel.getEndDay(), pageWarpEntity.getSortField(), pageWarpEntity.getSortType(), pageWarpEntity.getOnPage(), pageWarpEntity.getSize(), lang);

        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }

        Map<String, Object> res = new HashMap<>();
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam(dateModel);
        params.put("arcAccount", arcAccount);

        if (StringUtils.isBlank(pageWarpEntity.getSortCondition())) {
            pageWarpEntity.setSortCondition("addressBehavior desc");
        }

        List<Map<String, Object>> phoneList = arcCommonService.getCommonServiceListResult(methodName, params, pageWarpEntity);
        Long count = arcCommonService.getCommonServiceCountResult(methodName, params, true);

        if (CollUtil.isNotEmpty(phoneList)) {
            for (Map<String, Object> objectMap : phoneList) {
                // 获取address键对应的值
                Object addressValue = objectMap.get("address");
                // 添加countryCode字段
                objectMap.put("countryCode", addressValue);

                if (addressValue != null && StrUtil.isNotEmpty(addressValue.toString())) {
                    // 对address字段进行国家码翻译
                    Object translatedValue = arcDrillDownFieldTranslateConfigService.fieldTranslate(COUNTRY_CODE_FIELD, addressValue, lang);

                    if (translatedValue != null) {
                        // 如果翻译后值不为空，则更新address字段
                        objectMap.put("address", translatedValue);
                    }
                }
            }
        }

        res.put("total", count);
        res.put("list", phoneList);

        redisOps.set(redisKey, res, DateUtils.getRedisExpireTime());
        return ReturnModel.getInstance().ok(res);
    }

    public Object connectAreaBelongDrillDown(String phoneNumber, String countryCode, PageWarpEntity pageWarpEntity, DateModel dateModel) {
        String methodName = BusinessCodeEnum.PHONE_CONNECT_AREA_BELONG_DETAIL_DRILL_DOWN.getValue();
        String redisKey = arcCommonService.getRedisKeyByDay(methodName, phoneNumber, countryCode, pageWarpEntity, dateModel.getStartDay(), dateModel.getEndDay());

        if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }

        if (StringUtils.isBlank(pageWarpEntity.getSortCondition())) {
            pageWarpEntity.setSortCondition("behaviorNum DESC,connectedNumber DESC");
        } else {
            pageWarpEntity.appendSortCondition(",connectedNumber DESC");
        }

        Map<String, Object> res = new HashMap<>();
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam(dateModel);
        params.put("phoneNumber", phoneNumber);
        params.put("countryCode", countryCode);

        List<Map<String, Object>> phoneList = arcCommonService.getCommonServiceListResult(methodName, params, pageWarpEntity);
        Long count = arcCommonService.getCommonServiceCountResult(methodName, params, true);

        res.put("total", count);
        res.put("list", phoneList);

        redisOps.set(redisKey, res, DateUtils.getRedisExpireTime());
        return ReturnModel.getInstance().ok(res);
    }

    public Object getFrequentActiveArea(String arcAccount, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        String redisKey = arcCommonService.getRedisKey("getFrequentActiveArea", dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption(), pageWarpEntity.getOnPage(), pageWarpEntity.getSize());
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        HashMap<String, Object> data = new HashMap<>();
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);

        if (StringUtils.isNotBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("onlineHour");
            pageWarpEntity.setSortType(1);
        }

        String tableName = getHisTableFromPhonePrefix(arcAccount);
        paramMap.put("ads_archive_phone_location_his_table", tableName);

        List<Map<String, Object>> frequentActiveArea = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_FREQUENT_ACTIVE_AREA.getValue(), paramMap, pageWarpEntity);
        Long count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_FREQUENT_ACTIVE_AREA.getValue(), paramMap, true);

        translateNetWorkTypeField(frequentActiveArea, new String[]{"netWorkType"});


        //将停留时长从秒转化为天时分秒
        frequentActiveArea.forEach(o -> {
            Object key = o.getOrDefault("onlineHour", "0");
            o.put("onlineHour", DateUtils.transferSeconds((int) Float.parseFloat(key.toString())));
        });

        data.put("list", frequentActiveArea);
        data.put("total", count);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

//    @Deprecated
//    public Object activityTrajectoryMap(String arcAccount, String keyword, String trajectoryType, String startHour, String endHour, String baseStationNo, DateModel dateModel, PageWarpEntity pageWarpEntity) {
//        String redisKey = arcCommonService.getRedisKey("activityTrajectoryMap", trajectoryType, keyword, dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption(), pageWarpEntity.getOnPage(), pageWarpEntity.getSize(), pageWarpEntity.getSortType(), pageWarpEntity.getSortField());
//        Object result = redisOps.get(redisKey);
//        if (result != null && ArcCommonServiceImpl.isOpen) {
//            return ReturnModel.getInstance().ok(result);
//        }
//
//        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
//            if ("1".equals(trajectoryType) || "3".equals(trajectoryType)) {
//                // 驻留次数降序
//                pageWarpEntity.setSortField("stayNum");
//                pageWarpEntity.setSortType(1);
//            } else {
//                // 次数降序
//                pageWarpEntity.setSortField("trajectoryCount");
//                pageWarpEntity.setSortType(1);
//            }
//        }
//
//        HashMap<String, Object> data = new HashMap<>();
//
//        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
//        paramMap.put("arcAccount", arcAccount);
//        if (StringUtils.isBlank(keyword)) {
//            paramMap.put("keyword_condition", "");
//        } else {
//            if ("1".equals(trajectoryType) || "3".equals(trajectoryType)) {
//                paramMap.put("keyword_condition", String.format(" and (address like '%%%s%%' or base_station_no like '%%%s%%')", keyword, keyword));
//            } else {
//                paramMap.put("keyword_condition",String.format(" and (src_address like '%%%s%%' or dst_address like '%%%s%%' or src_station_no like '%%%s%%' or dst_station_no like '%%%s%%')",
//                        keyword, keyword, keyword, keyword));
//            }
//        }
//
//        if (StringUtils.isNotBlank(startHour)) {
//            paramMap.put("hour_condition", String.format(" and capture_hour >= %s and capture_hour <= %s", startHour, endHour));
//        } else {
//            paramMap.put("hour_condition", "");
//        }
//
//        if (StringUtils.isNotBlank(baseStationNo)) {
//            paramMap.put("station_condition", String.format(" and base_station_no = '%s'", baseStationNo));
//        } else {
//            paramMap.put("station_condition", "");
//        }
//
//        if ("1".equals(trajectoryType) || "3".equals(trajectoryType)) {
//            // 实时轨迹 PHONE_REAL_TIME_TRAJECTORY
//            Long count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_REAL_TIME_TRAJECTORY.getValue(), paramMap, true);
//
//            List<Map<String, Object>> frequentActiveArea = new ArrayList<>();
//            if (ObjectUtil.isNotNull(count) && count > 0L) {
//
//                //限制实时轨迹查询条数
//                if (count > realtimeTrajectorySize) {
//                    count = realtimeTrajectorySize;
//                }
//
//                frequentActiveArea = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_REAL_TIME_TRAJECTORY.getValue(), paramMap, pageWarpEntity);
//
//                translateNetWorkTypeField(frequentActiveArea, new String[]{"netWorkType"});
//            }
//
//            data.put("list", frequentActiveArea);
//            data.put("total", count);
//        } else if ("2".equals(trajectoryType)) {
//            // 规律轨迹 PHONE_REGULAR_TRAJECTORY
//            // 规律轨迹查询范围近12月,按月查询
//            paramMap.put("start_month", dateModel.getStartMonth());
//            paramMap.put("end_month", dateModel.getEndMonth());
//
//            Long count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_HIS_DAY_TRAJECTORY.getValue(), paramMap, true);
//            List<Map<String, Object>> frequentActiveArea = new ArrayList<>();
//            if (ObjectUtil.isNotNull(count) && count > 0L) {
//                //限制规律轨迹查询条数
//                if (count > regularTrajectorySize) {
//                    count = regularTrajectorySize;
//                }
//                frequentActiveArea = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_HIS_DAY_TRAJECTORY.getValue(), paramMap, pageWarpEntity);
//                getTop3TrajectoryPeriod(frequentActiveArea);
//
//                translateNetWorkTypeField(frequentActiveArea, new String[]{"srcNetWorkType", "dstNetWorkType"});
//            }
//
//            data.put("list", frequentActiveArea);
//            data.put("total", count);
//        }
//
//        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
//        return data;
//    }

    public Object getVirtualAccountInfo(String arcAccount, String arcAccountType, String dataType, String keyword, ArcTypeEnum arcTypeEnum, String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity, String lang) {
        String redisKey = arcCommonService.getRedisKey("getVirtualAccountInfo", arcAccount, dataType, keyword, arcTypeEnum.getKey(), dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption(), pageWarpEntity.getOnPage(), pageWarpEntity.getSize(), pageWarpEntity.getSortType(), pageWarpEntity.getSortField(), lang);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortCondition("`behaviorNum` DESC, virtual_account DESC");
        }

        HashMap<String, Object> data = new HashMap<>();
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);

        if (AuthTypeEnum.FIXED_IP.getType().toString().equals(arcAccountType)) {
            DateUtils.validateFixedIpStartDay(paramMap, createDay);
        }

        paramMap.put("arcAccount", arcAccount);
        paramMap.put("arcAccountType", arcAccountType);

        if (StringUtils.isBlank(keyword)) {
            paramMap.put("keyword_condition", "");
        } else {
            paramMap.put("keyword_condition", " and lower(virtual_account) like '%" + keyword.toLowerCase() + "%'");
        }

        if (StringUtils.isBlank(dataType)) {
            paramMap.put("data_type_condition", "");
        } else {
            paramMap.put("data_type_condition", " and norm_data_type =" + dataType);
        }

        List<Map<String, Object>> list = new ArrayList<>();
        Long count = 0L;

        if (ArcTypeEnum.PHONE.getKey().equals(arcTypeEnum.getKey())) {
            list = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_VIRTUAL_ACCOUNT_TAB.getValue(), paramMap, pageWarpEntity);
            count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_VIRTUAL_ACCOUNT_TAB.getValue(), paramMap, true);
        } else {
            list = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.RADIUS_VIRTUAL_ACCOUNT_TAB.getValue(), paramMap, pageWarpEntity);
            count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.RADIUS_VIRTUAL_ACCOUNT_TAB.getValue(), paramMap, true);
        }

        if (CollectionUtil.isNotEmpty(list)) {
            // 遍历 list
            for (Map<String, Object> oneData : list) {
                Object dataTypeObj = oneData.getOrDefault("dataType", 0);

                String dataTypeName = "";
                if (dataTypeObj != null) {
                    dataTypeName = arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, dataTypeObj, lang).toString();
                }

                Object appTypeObj = oneData.getOrDefault("appType", 0);
                String appTypeName = "";
                if (appTypeObj != null) {
                    appTypeName = arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.CHILD_TYPE, appTypeObj, lang).toString();
                }

                oneData.put("dataTypeName", dataTypeName);
                oneData.put("appTypeName", appTypeName);
            }
        }

        data.put("list", list);
        data.put("total", count);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    public Object communicationTrend(String arcAccount, String callTag, DateModel dateModel) {
        String redisKey = arcCommonService.getRedisKey("communicationTrend", callTag, dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption());
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);

        //未选择动作类型或者动作类型为全部则不过滤
        if (StringUtils.isBlank(callTag) || "0".equals(callTag)) {
            paramMap.put("call_tag_condition", "");
        } else {
            paramMap.put("call_tag_condition", " and call_tag = " + callTag);
        }

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_COMMUNICATION_TREND.getValue(), paramMap);
        // 返回结果 time|behaviorNum
        //        2024-04-06|1
        // 基于 data 返回结果构建 timeMap
        // 遍历 data, 将 time 作为 key, behaviorNum 作为 value, 存入 timeMap
        Map<String, Object> timeMap = new HashMap<>();
        for (Map<String, Object> item : data) {
            timeMap.put(item.getOrDefault("time", "").toString(), item.getOrDefault("behaviorNum", 0));
        }

        ArrayList<Map<String, Object>> returnData = new ArrayList<>();
        DateRange range = DateUtil.range(DateUtil.parse(dateModel.getStartDay()), DateUtil.parse(dateModel.getEndDay()), DateField.DAY_OF_YEAR);

        for (DateTime time : range) {
            // 返回前端展示的时间格式
            String day = time.toString("dd-MM-yyyy");
            // 从查询结果中获取对应行为次数的时间格式
            String dayKey = time.toString("yyyy-MM-dd");
            Object behaviorNumObj = timeMap.get(dayKey);

            HashMap<String, Object> returnModel = new HashMap<>();
            if (behaviorNumObj != null && StringUtils.isNotBlank(behaviorNumObj.toString())) {
                returnModel.put("time", day);
                returnModel.put("behaviorNum", behaviorNumObj);
            } else {
                returnModel.put("time", day);
                returnModel.put("behaviorNum", 0);
            }

            returnData.add(returnModel);
        }

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object communicationRank(String arcAccount, String callTag, DateModel dateModel) {
        String redisKey = arcCommonService.getRedisKey("communicationRank", callTag, dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption());
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);

        //未选择动作类型或者动作类型为全部则不过滤
        if (StringUtils.isBlank(callTag) || "0".equals(callTag)) {
            paramMap.put("call_tag_condition", "");
        } else {
            paramMap.put("call_tag_condition", " and call_tag = " + callTag);
        }

        PageWarpEntity pageWarpEntity = new PageWarpEntity();
        pageWarpEntity.setOnPage(1);
        pageWarpEntity.setSize(10);
        pageWarpEntity.setSortField("total");
        pageWarpEntity.setSortType(1);

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_COMMUNICATION_RANK.getValue(), paramMap, pageWarpEntity);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    /**
     * 查询活跃地城市信息
     */
    public Object getActiveCityInfo() {
        List<PhoneActiveCountryModel> result = Lists.newArrayList();

        String redisKey = arcCommonService.getRedisKey("getActiveCityInfo", "activeCityInfo");
        Object cacheResult = redisOps.get(redisKey);
        if (ObjectUtil.isNotNull(cacheResult) && ArcCommonServiceImpl.isOpen) {
            return cacheResult;
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam();
        List<Map<String, Object>> dataList = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.COMMON_PHONE_ACTIVE_CITY_MAP.getValue(), paramMap);
        if (CollectionUtil.isEmpty(dataList)) {
            return result;
        }

        Map<String, PhoneActiveCountryModel> countryMap = new LinkedHashMap<>();

        for (Map<String, Object> data : dataList) {
            String country = (String) data.get("country");
            String city = (String) data.get("city");

            if (StringUtils.isEmpty(country)) {
                continue;
            }

            if (!countryMap.containsKey(country)) {
                PhoneActiveCountryModel activeCountryModel = new PhoneActiveCountryModel();

                activeCountryModel.setValue(country);
                activeCountryModel.setLabel(country);
                activeCountryModel.setChildren(Lists.newArrayList());
                countryMap.put(country, activeCountryModel);
            }

            // 移除按城市的编码查询
//            if (StringUtils.isNotEmpty(city)) {
//                PhoneActiveCityModel phoneActiveCityModel = new PhoneActiveCityModel();
//                phoneActiveCityModel.setLabel(city);
//                phoneActiveCityModel.setValue(city);
//
//                List<PhoneActiveCityModel> children = countryMap.get(country).getChildren();
//
//                children.add(phoneActiveCityModel);
//            }
        }

        result = Lists.newArrayList(countryMap.values());
        redisOps.set(redisKey, result, DateUtils.getRedisExpireTime());
        return result;
    }

    public Object networkTypeDistribution(String arcAccount, DateModel dateModel) {
        String redisKey = arcCommonService.getRedisKey("networkTypeDistribution", dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption());
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_NETWORK_TYPE_DISTRIBUTION.getValue(), paramMap);
        // 返回结果为
        //netWorkTypeName|behaviorNum|
        //4G             |          1|
        // 需要遍历 data 基于 behaviorNum 计算总和, 同时计算 rate=behaviorNum/total, 并且写回到 data 中
        long total = 0;
        for (Map<String, Object> item : data) {
            total += Long.parseLong(item.getOrDefault("behaviorNum", "0").toString());
        }
        for (Map<String, Object> item : data) {
            long behaviorNum = Long.parseLong(item.getOrDefault("behaviorNum", "0").toString());
            // 计算 rate 保留四位小数
            double rate = 0.0;
            if (total > 0) {
                rate = behaviorNum * 1.0 / total;
            }
            item.put("rate", NumberUtil.formatPercent(rate, 4));
        }

        translateNetWorkTypeField(data, new String[]{"netWorkTypeName"});

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    public Object useDeviceRank(String arcAccount, DateModel dateModel) {
        String redisKey = arcCommonService.getRedisKey("useDeviceRank", dateModel.getStartDay(), dateModel.getEndDay(), dateModel.getDateOption());
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);
        PageWarpEntity pageWarpEntity = new PageWarpEntity();
        pageWarpEntity.setOnPage(1);
        pageWarpEntity.setSize(10);
        pageWarpEntity.setSortField("behaviorNum");
        pageWarpEntity.setSortType(1);

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_USE_DEVICE_RANK.getValue(), paramMap, pageWarpEntity);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    /**
     * 号码档案鉴权计费记录-计费明细数据
     *
     * @param authBillingDetailModel 用户id
     * @param lang                   语言
     * @return 返回结果
     */
    public Object getCommunicationDataDetail(AuthBillingDetailModel authBillingDetailModel, String lang) {
        //查询资源库 deye_v64_mobilenetradius
        Set<String> resourceNames = new HashSet<>(Collections.singletonList(esResourceNameConfig.getMobilenetradius()));
        StringBuilder sb = new StringBuilder();
        //根据档案账号计算queryString信息
        if (StringUtils.isNotBlank(authBillingDetailModel.getArcAccount())) {
            sb.append("auth_accountField:").append(authBillingDetailModel.getArcAccount())/*.append(" AND ")*/;
        }
        //根据网络类型计算queryString信息
        Map<Integer, String> networkTypeMap = new HashMap<>();
        networkTypeMap.put(1, "1");
        networkTypeMap.put(2, "2");
        networkTypeMap.put(3, "3");
        networkTypeMap.put(4, "4");
        networkTypeMap.put(5, "5");// mix
        networkTypeMap.put(0, "0");// unkonw
        networkTypeMap.put(99, "*");
        String networkType = networkTypeMap.get(authBillingDetailModel.getNetworkType());
        sb.append(" AND network_typeField:").append(networkType);
        if (StringUtils.isNotBlank(authBillingDetailModel.getBaseStationNo())) {
            sb.append(" AND base_station_numberField:").append(authBillingDetailModel.getBaseStationNo());
        }

        if (authBillingDetailModel.getAction() == 1) {
            sb.append(" AND action_mapField: login ");
        } else if (authBillingDetailModel.getAction() == 2) {
            sb.append(" AND action_mapField: logout ");
        }

        //根据关键字计算queryString信息
        if (StringUtils.isNotBlank(authBillingDetailModel.getKeyWord())) {
            sb.append(" AND (base_station_addressField:").append(authBillingDetailModel.getKeyWord()).append(" OR base_station_locationField:").append(authBillingDetailModel.getKeyWord()).append(" OR base_station_numberField:").append(authBillingDetailModel.getKeyWord()).append(" OR imeiField:").append(authBillingDetailModel.getKeyWord()).append(")");
        }

        String sortString = "";

        if (authBillingDetailModel.getSortType() != null && StringUtils.isNotBlank(authBillingDetailModel.getSortField())) {
            JSONObject jsonObject = new JSONObject();
            if (authBillingDetailModel.getSortType() == 1) {
                // 降序
                jsonObject.put(authBillingDetailModel.getSortField(), 1);
            } else {
                // 升序
                jsonObject.put(authBillingDetailModel.getSortField(), 0);
            }

            sortString = jsonObject.toJSONString();
        }

        AdvancedSearchParamModel advancedSearchParamModel = new AdvancedSearchParamModel();
        advancedSearchParamModel.setResourceNames(resourceNames);
        advancedSearchParamModel.setStartTime(DateUtils.getOneDayStart(authBillingDetailModel.getStartDay()));
        advancedSearchParamModel.setEndTime(DateUtils.getOneDayEnd(authBillingDetailModel.getEndDay()));
        advancedSearchParamModel.setOnPage(authBillingDetailModel.getOnPage());
        advancedSearchParamModel.setPageSize(authBillingDetailModel.getSize());
        advancedSearchParamModel.setContainsDetail(true);
        advancedSearchParamModel.setQuery(sb.toString());
        advancedSearchParamModel.setMultiSortField(sortString);
        TianheRequestModel advancedSearchRequestModel = tianheRequestConfig.getAdvancedSearchRequestModel(advancedSearchParamModel);
        TianheReturnModel access = tianheDataRequestService.access(advancedSearchRequestModel);

        List dataList;
        Map<String, Object> searchMap = convert(access);
        if (searchMap.get("data") != null && searchMap.get("data") instanceof List) {
            dataList = (List) searchMap.get("data");
        } else {
            return null;
        }
        Map dataMap = (Map) dataList.get(0);
        DetailQueryResultModel detailQueryResultModel = new DetailQueryResultModel();
        try {
            detailQueryResultModel = MapToObjUtils.mapToObject(dataMap, DetailQueryResultModel.class);
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("mapToObject error", e);
        }

        HashMap<String, Object> data = new HashMap<>();
        List list = new ArrayList<>();
        if (detailQueryResultModel.getRecords() != null && !detailQueryResultModel.getRecords().isEmpty()) {
            detailQueryResultModel.getRecords().forEach(record -> {
                list.add(record.get("resource"));
            });
        }

        data.put("total", detailQueryResultModel.getTotal());
        data.put("list", list);

        //对结果数据进行渲染
        arcDrillDownFieldTranslateConfigService.fieldTranslate(list, lang, -2);
        return data;
    }

    public static Map<String, Object> convert(Object entity) {
        Map<String, Object> map = new HashMap<>();

        // 获取实体类的所有字段
        Field[] fields = entity.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);

            try {
                // 将字段名和字段值存入Map
                map.put(field.getName(), field.get(entity));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return map;
    }

    /**
     * 根据档案ID查询机主信息
     *
     * @param arcInfoModel 参数模型
     * @return 机主信息
     */
    public Object getPhoneOwnerInfo(ArcInfoModel arcInfoModel) {
        // 机主信息
        String methodName = BusinessCodeEnum.OWNER_INFO.getValue();

        Map<String, Object> params = new HashMap<>();

        params.put("owner_phone_number", arcInfoModel.getArcAccount());
        // top10
        params.put("size", 10);
        params.put("dws", dwsDbName);
        List<Map<String, Object>> ownerRelationShip = arcCommonService.getCommonServiceListResult(methodName, params);
        // 待查询的手机号集合
        List<String> phoneNumbers = Lists.newArrayList();
        // 最终返回的结果map
        List<Map<String, Object>> relationships = Lists.newArrayList();
        for (Map<String, Object> stringObjectMap : ownerRelationShip) {
            Map<String, Object> relationship = new HashMap<>();
            // 关系人的号码或人名
            relationship.put("relationship_info", MapUtil.getStr(stringObjectMap, "relationshipPersonPhone"));
            // 关系人与机主的关系
            relationship.put("relationship", MapUtil.getStr(stringObjectMap, "relationship"));
            // 需要查询号码关联姓名的手机号
            phoneNumbers.add(MapUtil.getStr(stringObjectMap, "relationshipPersonPhone"));
            relationships.add(relationship);
        }
        if (!phoneNumbers.isEmpty()) {
            String methodRelationPhoneInfoName = BusinessCodeEnum.OWNER_RELATION_PHONE_INFO.getValue();
            Map<String, Object> phoneRelationInfoParams = new HashMap<>();
            phoneRelationInfoParams.put("phone_number", String.join("','", phoneNumbers));
            phoneRelationInfoParams.put("dws", dwsDbName);
            List<Map<String, Object>> ownerRelationPhoneInfo = arcCommonService.getCommonServiceListResult(methodRelationPhoneInfoName, phoneRelationInfoParams);
            // 得到号码与账号的映射关系
            Map<String, Object> phoneNumberMapping = ownerRelationPhoneInfo.stream().collect(Collectors.toMap(key ->
                            MapUtil.getStr(key, "phone_number"),
                    value -> value.get("name")));

            relationships.forEach(relationshipMap -> {
                // 号码
                String relationshipInfo = (String) relationshipMap.get("relationship_info");
                if (phoneNumberMapping.containsKey(relationshipInfo)) {
                    relationshipMap.put("relationship_info", phoneNumberMapping.get(relationshipInfo));
                }
            });
        }
        String methodPhoneInfoName = BusinessCodeEnum.OWNER_PHONE_INFO.getValue();
        Map<String, Object> phoneInfoParams = new HashMap<>();
        String ownerInfo = arcInfoModel.getArcAccount();
//        String phoneNumberParam = "'%s'";
        phoneInfoParams.put("phone_number", ownerInfo);
        phoneInfoParams.put("dws", dwsDbName);
        List<Map<String, Object>> ownerPhoneInfo = arcCommonService.getCommonServiceListResult(methodPhoneInfoName, phoneInfoParams);
        List<String> ownerNames = ownerPhoneInfo.stream().map(ownerPhoneInfoMap -> MapUtil.getStr(ownerPhoneInfoMap, "name"))
                .distinct().collect(Collectors.toList());
        String ownerInfoName = "";
        if (!ownerNames.isEmpty()) {
            ownerInfoName = String.join(" / ", ownerNames);
        }
        // 若都为空则返回null。前端会隐藏入口
        if (StringUtils.isEmpty(ownerInfoName) && relationships.isEmpty()) {
            return null;
        }
        Map<String, Object> res = new HashMap<>();
        // 如果有则返回人名否则返回为空

        res.put("owner_info", ownerInfoName);
        res.put("relationships", relationships);
        return res;
    }


    private void translateNetWorkTypeField(List<Map<String, Object>> frequentActiveArea, String[] translateFields) {
        if (CollUtil.isNotEmpty(frequentActiveArea)) {
            for (Map<String, Object> objectMap : frequentActiveArea) {
                for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
                    //对network_type字段进行翻译
                    for (String translateField : translateFields) {
                        if (translateField.equals(entry.getKey())) {
                            Object value = entry.getValue();
                            if (ObjectUtil.isNotNull(value) && StrUtil.isNotEmpty(value.toString())) {
                                objectMap.put(entry.getKey(), arcDrillDownFieldTranslateConfigService.fieldTranslate(NETWORK_TYPE_FIELD, value));
                            }
                        }
                    }
                }
            }
        }
    }


    public Object timeStatistics(String arcAccount, String hourInterval, String statisticsType, String top, String keyword, DateModel dateModel) {
        String redisKey = arcCommonService.getRedisKey("timeStatistics", arcAccount, hourInterval, statisticsType, dateModel);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        buildTimeStatisticsParam(arcAccount, hourInterval, statisticsType, top, keyword, paramMap);
        HashMap<String, Object> returnData = new HashMap<>();

        String tableName = getHisTableFromPhonePrefix(arcAccount);
        paramMap.put("ads_archive_phone_location_his_table", tableName);
        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_AREA_HOT_TIME_STATISTICS.getValue(), paramMap);
        // 返回结构如下
        // timeRange|stayCount|areaCount|stayHour|
        // ---------+---------+---------+--------+
        // 10-12    |        2|        1|     2.0|
        LinkedList<Object> resultData = new LinkedList<>();

        if (StringUtils.isBlank(top)) {
            resultCompletion(hourInterval, statisticsType, data, resultData);
            returnData.put("list", resultData);
        } else {
            returnData.put("list", data);
        }

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    private void resultCompletion(String hourInterval, String statisticsType, List<Map<String, Object>> data, LinkedList<Object> resultData) {
        // 需要进行如下业务处理
        // 1 遍历 data, 基于 timeRange 构建 timeRangeMap, key 为 timeRange, value 为 data
        // 2 hourInterval 代表小时间间隔, 需要结合 hourInterval, 基于 hutool 工具类 ArrayUtil.range, 生成时间段区间数组 ArrayList<String> timeRangeList
        // 3 遍历 timeRangeList, 查找 timeRangeMap 中是否存在对应时段数据, 如果存在则将 timeRangeMap 的value 加入 resultData

        HashMap<String, Object> timeRangeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(data)) {
            for (Map<String, Object> map : data) {
                String timeRange = map.get("timeRange").toString();
                timeRangeMap.put(timeRange, map);
            }
        }

        List<String> timeRangeList = new LinkedList<>();
        int[] range = ArrayUtil.range(0, 24, Integer.valueOf(hourInterval));
        // 遍历 range, 获取值, 将值 + Integer.valueOf(hourInterval), 并且拼接成 xx-xx 加入 timeRangeList
        for (int i : range) {
            int start = i;
            int end = i + Integer.valueOf(hourInterval);
            String timeRange = start + "-" + end;
            timeRangeList.add(timeRange);
        }

        for (String timeRange : timeRangeList) {
            if (timeRangeMap.containsKey(timeRange)) {
                resultData.add(timeRangeMap.get(timeRange));
            } else {
                //  timeRange|stayCount|areaCount|stayHour|
                HashMap<Object, Object> emptyData = new HashMap<>();
                emptyData.put("timeRange", timeRange);
                emptyData.put("stayCount", 0);
                emptyData.put("areaCount", 0);
                emptyData.put("stayHour", 0);
                resultData.add(emptyData);
            }
        }

        // statisticsType 1-时长统计,2-次数统计
        // 根据 statisticsType 对 resultData 进行排序, 若 statisticsType=1 则按照 stayHour 降序 , 若 statisticsType=2 则按照 stayCount 降序
        if (statisticsType.equals("1")) {
            resultData.sort(Comparator.comparing(o -> {
                Object key = ((Map) o).getOrDefault("stayHour", "0.0");
                return -Double.parseDouble(key.toString());
            }));
        } else {
            resultData.sort(Comparator.comparing(o -> {
                Object key = ((Map) o).getOrDefault("stayCount", "0.0");
                return -Double.parseDouble(key.toString());
            }));
        }
    }

    private static void buildTimeStatisticsParam(String arcAccount, String hourInterval, String statisticsType, String top, String keyword, CommonParamUtil.ParamMap paramMap) {
        paramMap.put("phone", arcAccount);
        paramMap.put("interval", hourInterval);

        if (statisticsType.equals("1")) {
            paramMap.put("sort", "order by stayCount desc");
        } else {
            paramMap.put("sort", "order by stayHour desc");
        }

        if (StringUtils.isNotBlank(top)) {
            paramMap.put("top", " limit " + top);
        } else {
            paramMap.put("top", "");
        }

        if (StringUtils.isNotBlank(keyword)) {
            keyword = EscapeUtil.escapeSingleQuote(keyword);
            paramMap.put("keyword_condition", String.format(" and (address like '%%%s%%' or station_no like '%%%s%%')", keyword, keyword));
        } else {
            paramMap.put("keyword_condition", "");
        }
    }

    public Object stayRecord(String arcAccount, String stationNo, String startHour, String endHour, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        String redisKey = arcCommonService.getRedisKey("stayRecord", arcAccount, stationNo, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("outTime");
            pageWarpEntity.setSortType(1);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("phone", arcAccount);

        if (StringUtils.isBlank(stationNo)) {
            stationNo = "";
        }

        paramMap.put("stationNo", stationNo);

        if (StringUtils.isNotBlank(startHour) && StringUtils.isNotBlank(endHour)) {
            paramMap.put("hour_condition", String.format(" and capture_hour >= %s and capture_hour < %s", startHour, endHour));
        } else {
            paramMap.put("hour_condition", "");
        }

        HashMap<String, Object> returnData = new HashMap<>();

        String tableName = getHisTableFromPhonePrefix(arcAccount);
        paramMap.put("ads_archive_phone_location_his_table", tableName);
        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_AREA_HOT_STAY_RECORD.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_AREA_HOT_STAY_RECORD.getValue(), paramMap, true);

        returnData.put("list", data);
        returnData.put("total", total);

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    private String getHisTableFromPhonePrefix(String arcAccount) {
        if (StringUtils.isNotBlank(arcAccount) && arcAccount.length() >= 4) {
//            2136：表示M运营商；
//            2135：表示O运营商；
//            2137：表示D运营商；- D 运营商只有上下线数据，且未接入全息
            if (arcAccount.startsWith("2136")) {
                return phoneOperatorConfig.getMOperatorHis();
            } else if (arcAccount.startsWith("2135")) {
                return phoneOperatorConfig.getOOperatorHis();
            } else if (arcAccount.startsWith("2137")) {
                return phoneOperatorConfig.getDOperatorHis();
            } else {
                return phoneOperatorConfig.getMOperatorHis();
            }
        }

        return phoneOperatorConfig.getMOperatorHis();
    }

    private String getDayTableFromPhonePrefix(String arcAccount) {
        if (StringUtils.isNotBlank(arcAccount) && arcAccount.length() >= 4) {
//            2136：表示M运营商；
//            2135：表示O运营商；
//            2137：表示D运营商；- D 运营商只有上下线数据，且未接入全息
            if (arcAccount.startsWith("2136")) {
                return phoneOperatorConfig.getMOperator();
            } else if (arcAccount.startsWith("2135")) {
                return phoneOperatorConfig.getOOperator();
            } else if (arcAccount.startsWith("2137")) {
                return phoneOperatorConfig.getDOperator();
            } else {
                return phoneOperatorConfig.getMOperator();
            }
        }

        return phoneOperatorConfig.getMOperator();
    }

    public Object getImeiInfo(String arcAccount, String keyword, PageWarpEntity pageWarpEntity) {
        String redisKey = arcCommonService.getRedisKey("getImeiInfo", arcAccount, keyword);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam();
        paramMap.put("arcAccount", arcAccount);

        if (StringUtils.isNotBlank(keyword)) {
            paramMap.put("keyword_condition", String.format(" and device_id like '%%%s%%'", keyword));
        } else {
            paramMap.put("keyword_condition", "");
        }

        if (StringUtils.isBlank(pageWarpEntity.getSortCondition())) {
            pageWarpEntity.setSortCondition("latestTime desc");
        }

        paramMap.put("ads_archive_phone_location_his_table", getHisTableFromPhonePrefix(arcAccount));

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_IMEI_LIST.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_IMEI_LIST.getValue(), paramMap);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("list", data);
        resultMap.put("total", total);

        redisOps.set(redisKey, resultMap, DateUtils.getRedisExpireTime());
        return resultMap;
    }

    public Object realTimeTrajectory(String arcAccount, String keyword, String baseStationNo, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        String redisKey = arcCommonService.getRedisKey("realTimeTrajectory", arcAccount, keyword, baseStationNo, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            // 驻留次数降序
            pageWarpEntity.setSortField("earliestRelationTime");
            pageWarpEntity.setSortType(1);
        }

        HashMap<String, Object> data = new HashMap<>();

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);
        if (StringUtils.isBlank(keyword)) {
            paramMap.put("keyword_condition", "");
        } else {
            keyword = EscapeUtil.escapeSingleQuote(keyword);
            paramMap.put("keyword_condition", String.format(" and (address like '%%%s%%' or station_no like '%%%s%%')", keyword, keyword));
        }

        if (StringUtils.isNotBlank(baseStationNo)) {
            paramMap.put("station_condition", String.format(" and station_no = '%s'", baseStationNo));
        } else {
            paramMap.put("station_condition", "");
        }

        String tableName = getDayTableFromPhonePrefix(arcAccount);
        paramMap.put("ads_archive_phone_location_table", tableName);

        Long count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_REAL_TIME_TRAJECTORY.getValue(), paramMap, true);

        List<Map<String, Object>> frequentActiveArea = new ArrayList<>();
        if (ObjectUtil.isNotNull(count) && count > 0L) {

            //限制实时轨迹查询条数
            if (count > realtimeTrajectorySize) {
                count = realtimeTrajectorySize;
            }

            frequentActiveArea = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_REAL_TIME_TRAJECTORY.getValue(), paramMap, pageWarpEntity);

            translateNetWorkTypeField(frequentActiveArea, new String[]{"netWorkType"});
        }

        // 遍历 frequentActiveArea 获取 latestRelationTime 如果 latestRelationTime=********* 则将 latestRelationTime置为0 , 同时将 onlineHour 设置为 0
        for (Map<String, Object> map : frequentActiveArea) {
            if (ObjectUtil.isNotNull(map.get("latestRelationTime")) && "*********".equals(map.get("latestRelationTime").toString())) {
                map.put("latestRelationTime", 0);
                map.put("onlineHour", 0);
            }
        }

        //将停留时长从秒转化为天时分秒
        frequentActiveArea.forEach(o -> {
            Object key = o.getOrDefault("onlineHour", "0");
            o.put("onlineHour", DateUtils.transferSeconds((int) Float.parseFloat(key.toString())));
        });


        data.put("list", frequentActiveArea);
        data.put("total", count);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    public Object hisTrajectory(String arcAccount, String keyword, String baseStationNo, String trajectoryType, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        String serviceCode = "";
        if ("1".equals(trajectoryType)) {
            // 日轨迹
            serviceCode = BusinessCodeEnum.PHONE_HIS_DAY_TRAJECTORY.getValue();

            if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
                // 驻留次数降序
                pageWarpEntity.setSortField("earliestRelationTime");
                pageWarpEntity.setSortType(1);
            }
        } else if ("2".equals(trajectoryType)) {
            // 周轨迹轨迹
            serviceCode = BusinessCodeEnum.PHONE_HIS_WEEK_TRAJECTORY.getValue();

            if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
                // 驻留次数降序
                pageWarpEntity.setSortField("onlineHour");
                pageWarpEntity.setSortType(1);
            }
        } else if ("3".equals(trajectoryType)) {
            // 月轨迹轨迹
            serviceCode = BusinessCodeEnum.PHONE_HIS_MONTH_TRAJECTORY.getValue();

            if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
                // 驻留次数降序
                pageWarpEntity.setSortField("onlineHour");
                pageWarpEntity.setSortType(1);
            }
        }

        String redisKey = arcCommonService.getRedisKey("hisTrajectory", serviceCode, arcAccount, keyword, baseStationNo, trajectoryType, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        HashMap<String, Object> data = new HashMap<>();

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);
        if (StringUtils.isBlank(keyword)) {
            paramMap.put("keyword_condition", "");
        } else {
            keyword = EscapeUtil.escapeSingleQuote(keyword);
            paramMap.put("keyword_condition", String.format(" and (address like '%%%s%%' or station_no like '%%%s%%')", keyword, keyword));
        }

        paramMap.put("start_month", dateModel.getStartMonth());
        paramMap.put("end_month", dateModel.getEndMonth());

        paramMap.put("stayNumLimit", stayNumLimit);

        if (StringUtils.isNotBlank(baseStationNo)) {
            paramMap.put("station_condition", String.format(" and station_no = '%s'", baseStationNo));
        } else {
            paramMap.put("station_condition", "");
        }

        String tableName = getHisTableFromPhonePrefix(arcAccount);
        paramMap.put("ads_archive_phone_location_his_table", tableName);

        Long count = arcCommonService.getCommonServiceCountResult(serviceCode, paramMap, true);

        List<Map<String, Object>> frequentActiveArea = new ArrayList<>();
        if (ObjectUtil.isNotNull(count) && count > 0L) {
            frequentActiveArea = arcCommonService.getCommonServiceListResult(serviceCode, paramMap, pageWarpEntity);
            translateNetWorkTypeField(frequentActiveArea, new String[]{"netWorkType"});
        }

        //将停留时长从秒转化为天时分秒
        frequentActiveArea.forEach(o -> {
            Object key = o.getOrDefault("onlineHour", "0");
            o.put("onlineHour", DateUtils.transferSeconds((int) Float.parseFloat(key.toString())));
        });


        data.put("list", frequentActiveArea);
        data.put("total", count);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }

    public Object areaHotGraph(String arcAccount, String keyword, String startHour, String endHour, String baseStationNo, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        String redisKey = arcCommonService.getRedisKey("areaHotGraph", arcAccount, keyword, startHour, endHour, baseStationNo, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            // 驻留次数降序
            pageWarpEntity.setSortField("stayNum");
            pageWarpEntity.setSortType(1);
        }

        HashMap<String, Object> data = new HashMap<>();

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("arcAccount", arcAccount);
        if (StringUtils.isBlank(keyword)) {
            paramMap.put("keyword_condition", "");
        } else {
            keyword = EscapeUtil.escapeSingleQuote(keyword);
            paramMap.put("keyword_condition", String.format(" and (address like '%%%s%%' or station_no like '%%%s%%')", keyword, keyword));
        }

        if (StringUtils.isNotBlank(startHour)) {
            paramMap.put("hour_condition", String.format(" and capture_hour >= %s and capture_hour < %s", startHour, endHour));
        } else {
            paramMap.put("hour_condition", "");
        }

        if (StringUtils.isNotBlank(baseStationNo)) {
            paramMap.put("station_condition", String.format(" and station_no = '%s'", baseStationNo));
        } else {
            paramMap.put("station_condition", "");
        }


        String tableName = getHisTableFromPhonePrefix(arcAccount);
        paramMap.put("ads_archive_phone_location_his_table", tableName);
        Long count = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.PHONE_AREA_HOT_TRAJECTORY.getValue(), paramMap, true);

        List<Map<String, Object>> frequentActiveArea = new ArrayList<>();
        if (ObjectUtil.isNotNull(count) && count > 0L) {
            frequentActiveArea = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_AREA_HOT_TRAJECTORY.getValue(), paramMap, pageWarpEntity);

            translateNetWorkTypeField(frequentActiveArea, new String[]{"netWorkType"});
        }

        //将停留时长从秒转化为天时分秒
        frequentActiveArea.forEach(o -> {
            Object key = o.getOrDefault("onlineHour", "0");
            o.put("onlineHour", DateUtils.transferSeconds((int) Float.parseFloat(key.toString())));
        });

        data.put("list", frequentActiveArea);
        data.put("total", count);

        redisOps.set(redisKey, data, DateUtils.getRedisExpireTime());
        return data;
    }
}

