package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/29 14:02
 * Description: 鉴权计费表头模型
 */
@ApiModel("获取明细表头模型")
@Data
public class AuthBillingHeaderModel {


    /**
     * 计费动作
     * 1.开始计费 2.结束计费 3.更新计费 99。全部动作
     */
    @ApiModelProperty(value = "计费动作")
    private Integer action = 99;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "是否全部")
    private Integer isAll = 1;
}
