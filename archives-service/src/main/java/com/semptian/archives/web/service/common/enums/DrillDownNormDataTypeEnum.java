package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/19 15:30
 */
@Getter
public enum DrillDownNormDataTypeEnum {
    CALL(109, "call", "dwd_call"),

    SMS(200, "sms", "dwd_sms"),

    FAX(210, "fax", "dwd_fax"),

//    VOIP(2109, "voip", "dwd_lis_voip"),

    EMAIL(101, "email", "dwd_lis_email"),

    RADIUS(990, "radius", "dwd_radius"),

    // 弃用
    LOCATION(994, "location", "dwd_location"),

    VPN(121, "vpn", "dwd_lis_vpn"),

    IM(103, "im", "dwd_lis_im"),

    SOCIAL(119, "social", "dwd_lis_others"),

    TRAVEL(2001, "travel", "dwd_lis_others"),

    TOOL(2004, "tool", "dwd_lis_tool"),

    TERMINAL(142, "terminal", "dwd_lis_others"),

    REMOTE(113, "remote", "dwd_lis_remote"),

    LBS(146, "lbs", "dwd_lis_others"),

    HTTP(100, "http", "dwd_lis_http"),

    FTP(105, "ftp", "dwd_lis_ftp"),

    TELNET(108, "telnet", "dwd_lis_others"),

    ENGINE(125, "engine", "dwd_lis_others"),

    MULTIMEDIA(2002, "multimedia", "dwd_lis_others"),

    INFORMATION(138, "information", "dwd_lis_others"),

    ENTERTAINMENT(2003, "entertainment", "dwd_lis_others"),

    SHOP(122, "shop", "dwd_lis_others"),

    FINANCE(963, "finance", "dwd_lis_others"),

    OTHER(999, "other", "dwd_lis_others"),

    NF_URL(1201, "nf_url", "dwd_nf_url"),

    NF_EMAIL(1202, "nf_email", "dwd_nf_others"),

    NF_CHAT(1203, "nf_chat", "dwd_nf_others"),

    NF_BBS_WEIBO(1204, "nf_bbs_weibo", "dwd_nf_others"),

    NF_OTHER_LOG(1205, "nf_other_log", "dwd_nf_other_log"),

    /**
     * 目前测试只有nf_url，nf_other_log有VPN数据
     */
    NF_VPN_URL(1222, "nf_vpn_url", "dwd_vpn_url"),

    NF_VPN_OTHER_LOG(1223, "nf_vpn_other_log", "dwd_vpn_other_log");


    private final int key;

    private final String value;

    private final String tableName;

    DrillDownNormDataTypeEnum(int key, String value, String tableName) {
        this.key = key;
        this.value = value;
        this.tableName = tableName;
    }

    public static String getByKey(int key) {
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getValue();
            }
        }
        return "";
    }

    public static String getTableNameByKey(int key) {
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getTableName();
            }
        }
        return "";
    }

    public static int getKeyByValue(String value) {
        DrillDownNormDataTypeEnum[] values = DrillDownNormDataTypeEnum.values();
        for (DrillDownNormDataTypeEnum valueTemp : values) {
            if (valueTemp.getValue().equals(value)) {
                return valueTemp.getKey();
            }
        }
        return 0;
    }
}
