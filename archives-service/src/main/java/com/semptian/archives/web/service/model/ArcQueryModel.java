package com.semptian.archives.web.service.model;

import lombok.Data;

/**
 * 全部档案查询参数model
 *
 * <AUTHOR>
 * @date 2024/3/18
 */
@Data
public class ArcQueryModel {

    // 是否关注0=否1=是
    private Integer isCare = 0;

    // 每页大小
    private Integer size = 10;

    // 当前页
    private Integer onPage = 1;

    // 档案搜索关键词，支持档案账号、档案名称模糊查询
    private String keyWord;

    // 档案类型，默认值为0，0=全部档案，1=radius档案，2=email档案，3=网站档案，4=应用档案，5=号码档案，6=im档案，7=协议档案，8=固定ip档案
    private Integer arcType;

    // 排序字段，默认按活跃次数排序，活跃次数=activeRate，采集时间= latestRelationTime，创建时间=createTime
    private String sortField;

    // 排序方式，默认为降序，1=降序；2=升序
    private Integer sortType;

    // 活跃地-国家级，号码档案查询条件
    private String latestRelationCountry;

    // 活跃地-城市级，号码档案查询条件
    private String latestRelationCity;

    // 是否移动上网，默认为全部，0=否1=是99=全部
    private Integer isRadius;

    // 是否有传真，默认为全部，0=否1=有99=全部
    private Integer hasFax;

    // 应用类型，应用档案查询条件
    private String appType;

    // 应用名称，IM档案查询条件，预留字段
    private String appName;

    // 是否有文件，默认为全部，0=无1=有99=全部
    private Integer fileFlag;

    // 账号类型，默认为全部，1 个人 2 政企99=全部
    private Integer accountType;

    //用户id
    private String userId;

    private String lang;
}

