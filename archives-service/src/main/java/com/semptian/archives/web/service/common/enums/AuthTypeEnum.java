package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 认证账号认证类型枚举类
 *
 * <AUTHOR>
 * @date 2024/3/21
 */
@Getter
public enum AuthTypeEnum {

    /**
     * 固网radius
     */
    RADIUS("radius", 1020001),

    /**
     * 移动网radius
     */
    PHONE("phone", 1020004),

    /**
     * 固定IP
     */
    FIXED_IP("fixed_ip", 1029997),

    /**
     * 其他
     */
    OTHER("other", 1029999);

    private final String value;

    private final Integer type;

    AuthTypeEnum(String value, Integer type) {
        this.value = value;
        this.type = type;
    }

    /**
     * 根据档案名称获取档案认证类型
     * @return 认证类型编码
     */
    public static Integer getAuthTypeByName(String name) {
        for (AuthTypeEnum authTypeEnum : AuthTypeEnum.values()) {
            if (authTypeEnum.getValue().equals(name)) {
                return authTypeEnum.getType();
            }
        }
        return null;
    }
}
