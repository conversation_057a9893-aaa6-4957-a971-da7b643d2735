package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/8/8 16:44
 **/
@Data
@ApiModel(value = "时间传参实体类")
public class PageWarpEntity {

    /**
     * 开始页
     */
    @ApiModelProperty(value = "开始页")
    private Integer onPage = 1;

    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小")
    private Integer size = 10;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String sortField;

    /**
     * 1 - DESC , 0 - ASC
     */
    @ApiModelProperty(value = "1 - DESC , 0 - ASC")
    private Integer sortType;


    /**
     * 用于在默认排序条件上进行排序条件追加，用于解决 Doris 排序返回结果不确定问题, 会被 sortCondition 覆盖
     */
    private String appendSortCondition = "";

    /**
     * 排序条件, 例: create_time desc, 多个用逗号分隔, 用于支持多字段排序, 如果没有指定该字段，则会以 sortField 和 sortType 为准, 如果指定了, 则会以当前字段为准
     */
    @ApiModelProperty(value = "多字段排序条件, 例: create_time desc, 多个用逗号分隔, 用于支持多字段排序, 如果没有指定该字段，则会以 sortField 和 sortType 为准, 如果指定了, 则会以当前字段为准")
    private String sortCondition;

    public PageWarpEntity() {
    }

    public PageWarpEntity(Integer onPage, Integer size) {
        this.onPage = onPage;
        this.size = size;
    }

    public PageWarpEntity(Integer onPage, Integer size, String sortField, Integer sortType) {
        this.onPage = onPage;
        this.size = size;
        this.sortField = sortField;
        this.sortType = sortType;
    }

    public PageWarpEntity(Integer onPage, Integer size, String sortField, Integer sortType, String sortCondition) {
        this.onPage = onPage;
        this.size = size;
        this.sortField = sortField;
        this.sortType = sortType;
        this.sortCondition = sortCondition;
    }

    public static PageWarpEntity build() {
        return new PageWarpEntity();
    }

    public static PageWarpEntity build(Integer onPage, Integer size) {
        return build(onPage, size, null, null);
    }

    public static PageWarpEntity build(Integer onPage, Integer size, String sortField, Integer sortType) {
        return new PageWarpEntity(onPage, size, sortField, sortType, null);
    }

    public static PageWarpEntity build(Integer onPage, Integer size, String sortCondition) {
        return new PageWarpEntity(onPage, size, null, null, sortCondition);
    }

    public String getSortCondition() {
        if (StringUtils.isNotBlank(sortCondition)) {
            return " ORDER BY " + sortCondition;
        } else {
            if (StringUtils.isNotBlank(sortField)) {
                if (sortType == null) {
                    return " ORDER BY `"+ sortField + "` DESC " + appendSortCondition;
                } else {
                    if (sortType == 1) {
                        return " ORDER BY `" + sortField + "` DESC " + appendSortCondition;
                    } else {
                        return " ORDER BY `" + sortField + "` ASC " + appendSortCondition;
                    }
                }
            } else {
                return null;
            }
        }
    }

    public void appendSortCondition(String appendSortCondition) {
        String oldCondition = getSortCondition();

        if (StringUtils.isNotBlank(oldCondition)) {
            oldCondition = oldCondition.replace("ORDER BY", "");
            sortCondition = oldCondition + " " + appendSortCondition;
        }
    }
}
