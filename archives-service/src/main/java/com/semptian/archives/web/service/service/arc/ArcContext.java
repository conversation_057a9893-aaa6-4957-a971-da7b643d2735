package com.semptian.archives.web.service.service.arc;

import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ArcContext {

    // 档案类型
    private String arcType;

    // 档案ID
    private String arcId;

    //档案账号
    private String arcAccount;

    //档案账号类型
    private String arcAccountType;

    // 域名信息
    private String domain;

    private String appName;

    private String appType;

    //IM 档案
    private String virtualAppType;

    // 分页信息
    private Integer topSize;

    // 协议类型
    private String dataType;

    //页码
    private Integer onPage;

    //每页条数
    private Integer size;

    //阻断标志位
    private Integer netAction;

    //是否是ip档案访问者,默认为false：档案访问者
    private Boolean isIp;

    // 是否阻断标志位
    private Boolean isBlock;

    //查询关键字
    private String keyWord;

    //查询日期类型0=按天，1=按月
    private Integer dateType;

    //开始日期
    private String startDay;

    //结束日期
    private String endDay;

    //排序字段
    private String sortField;

    //排序类型0=升序，1=降序
    private Integer sortType;

    //是否活跃区域，0：通联，1：活跃
    private Integer isActive;

    //文件类型
    private String fileType;

    //创建时间
    private String createDay;

    private Integer behaviorType;

    // 访问者类型 1 = 档案访问者 2 = IP地址 3 = 重要目标
    private Integer visitorType;

    // 应用分析的展示维度
    private Integer showDimension;

    // 通用日期查询参数模型
    private DateModel dateModel;

    // 通用分页查询参数模型
    private PageWarpEntity pageWarpEntity;

    // 时段统计间隔
    private Integer interval;

    // 语言
    private String lang;

    // IM档案搜索类型 0 = 全部 1 = 重要目标
    private Integer type;

    // 查询的目标账号
    private String targetAccount;
}
