package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcRemarkMapper;
import com.semptian.archives.web.service.service.ArcRemarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-02-07 12:13:23
 */
@Slf4j
@Service("arcRemarkService")
public class ArcRemarkServiceImpl extends ServiceImpl<ArcRemarkMapper, ArcRemarkEntity> implements ArcRemarkService {

    @Override
    public List<ArcRemarkEntity> getArcRemarkByArcIdsAndUserId(List<String> arcId, String userId) {
        return this.getBaseMapper().getArcRemarkByArcIdsAndUserId(arcId);
    }
}
