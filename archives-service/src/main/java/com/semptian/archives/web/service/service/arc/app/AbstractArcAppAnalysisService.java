package com.semptian.archives.web.service.service.arc.app;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.model.RadiusAppIncrementalItemModel;
import com.semptian.archives.web.model.RadiusAppIncrementalItemTimeModel;
import com.semptian.archives.web.model.RadiusAppTimeListModel;
import com.semptian.archives.web.model.RadiusAppTimeModel;
import com.semptian.archives.web.service.common.enums.SortTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractArcAppAnalysisService implements ArcAppAnalysisService {

    @Resource
    private ArcCommonService arcCommonService;

    protected static final String PIE_CHART = "pie";

    protected static final String LINE_CHART = "LINE";

    @Override
    public List<RadiusAppTimeListModel> getAppTimeInfo(ArcContext ctx) {
        //时间参数
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam();
        params.put("start_day", ctx.getStartDay());
        params.put("end_day", ctx.getEndDay());

        // 统计维度
        if (ctx.getShowDimension() == 0) {
            params.put("group_field", "app_type");
        } else {
            params.put("group_field", "app_name");
        }

        // 过滤条件
        if (StringUtils.isNotEmpty(ctx.getAppName())) {
            Set<String> appNames = Arrays.stream(ctx.getAppName().split(",")).filter(StringUtils::isNotEmpty)
                    .map(name -> "'" + name + "'").collect(Collectors.toSet());
            if (!appNames.isEmpty()) {
                params.put("app_name", "and app_name in ( " +  String.join(",", appNames) + " )");
            } else {
                params.put("app_name", "");
            }
            params.put("app_type", "");
        } else if (StringUtils.isNotEmpty(ctx.getAppType())) {

            Set<String> appTypes = Arrays.stream(ctx.getAppType().split(",")).filter(StringUtils::isNotEmpty)
                    .map(type -> "'" + type + "'").collect(Collectors.toSet());
            if (!appTypes.isEmpty()) {
                params.put("app_type", "and app_type in ( " + String.join(",", appTypes) + " )");
            } else {
                params.put("app_type", "");
            }

            params.put("app_name", "");
        } else {
            params.put("app_name", "");
            params.put("app_type", "");
        }

        params.put("top_size", ctx.getTopSize());

        params.put("limit_size", " limit " + ctx.getTopSize() + " offset 0 ");

        //构造档案特定的参数
        buildArcSpecifiedParams(params, ctx);

        if (ctx.getInterval() != null && ctx.getInterval() > 0) {
            Integer interval = ctx.getInterval();
            String partitionSQL = String.format(" CONCAT(FLOOR(capture_hour / %d) * %d, ' - ', (FLOOR(capture_hour / %d) * %d + %d)) ", interval, interval, interval, interval, interval - 1);
            params.put("partition_sql", partitionSQL);
        }

        //获取doris sql模板
        String appPeriodTemplate = appPeriodTemplate(ctx);

        List<Map<String, Object>> list = arcCommonService.getCommonServiceListResult(appPeriodTemplate, params, true);

        list = list.stream().peek(m -> {
            String time = (String) m.get("time");
            m.put("time", Integer.valueOf(time.split("-")[0].trim()));
        }).collect(Collectors.toList());
        List<RadiusAppTimeModel> periodList = JSONObject.parseArray(JSONObject.toJSONString(list), RadiusAppTimeModel.class);

        Map<Integer, List<RadiusAppTimeModel>> periodMap = periodList.stream().collect(Collectors.groupingBy(RadiusAppTimeModel::getTime));

        List<Integer> timeFrameInterval = getTimeFrameInterval(ctx.getInterval());
        List<RadiusAppTimeListModel> resultList = new ArrayList<>();
        for (int i = 0; i < timeFrameInterval.size() - 1; i++) {
            List<RadiusAppTimeModel> intervalRadiusAppTimeModelList = new ArrayList<>();
            RadiusAppTimeListModel radiusAppTimeListModel = new RadiusAppTimeListModel();
            int startHour = timeFrameInterval.get(i);
            radiusAppTimeListModel.setTime(startHour);
            int endHour = timeFrameInterval.get(i + 1);
            //获取时间间隔内的访问的app和次数
            while (startHour < endHour) {
                List<RadiusAppTimeModel> radiusAppTimeModels = periodMap.get(startHour);
                if (radiusAppTimeModels != null && !radiusAppTimeModels.isEmpty()) {
                    intervalRadiusAppTimeModelList.addAll(radiusAppTimeModels);
                }
                startHour++;
            }
            //将时间间隔内的的小时访问量按照名称相加并取topN
            List<RadiusAppTimeModel> timeList = getTimeList(intervalRadiusAppTimeModelList, ctx.getTopSize());
            radiusAppTimeListModel.setDetail(timeList);
            resultList.add(radiusAppTimeListModel);
        }

        return resultList.stream().sorted(Comparator.comparing(RadiusAppTimeListModel::getTime).reversed()).collect(Collectors.toList());
    }

    /**
     * @param ctx 查询上下文
     * @return 返回应用时段分析的模板业务码
     */
    protected abstract String appPeriodTemplate(ArcContext ctx);

    private List<RadiusAppTimeModel> getTimeList(List<RadiusAppTimeModel> timeList, Integer topSize) {
        if (timeList.isEmpty()) {
            return timeList;
        }
        Map<String, List<RadiusAppTimeModel>> collect = timeList.stream().collect(Collectors.groupingBy(RadiusAppTimeModel::getName));
        List<RadiusAppTimeModel> newTimeList = Lists.newArrayList();
        for (Map.Entry<String, List<RadiusAppTimeModel>> stringListEntry : collect.entrySet()) {
            RadiusAppTimeModel radiusAppTimeModel = new RadiusAppTimeModel();
            radiusAppTimeModel.setName(stringListEntry.getKey());

            List<RadiusAppTimeModel> value = stringListEntry.getValue();
            radiusAppTimeModel.setTime(value.get(0).getTime());
            long sum = value.stream().collect(Collectors.summarizingLong(RadiusAppTimeModel::getValue)).getSum();
            radiusAppTimeModel.setValue(sum);
            newTimeList.add(radiusAppTimeModel);
        }
        newTimeList = newTimeList.stream().sorted(Comparator.comparing(RadiusAppTimeModel::getValue).reversed()).collect(Collectors.toList());
        if (newTimeList.size() > topSize) {
            return newTimeList.subList(0, topSize);
        } else {
            return newTimeList;
        }
    }

    @Override
    public Map<String, Object> getAppImgTable(ArcContext ctx) {
        //时间参数
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam();
        params.put("start_day", ctx.getStartDay());
        params.put("end_day", ctx.getEndDay());

        // 统计维度
        if (ctx.getShowDimension() == 0) {
            params.put("group_field", "app_type");
        } else {
            params.put("group_field", "app_name");
        }

        // 过滤条件
        if (StringUtils.isNotEmpty(ctx.getAppName())) {
            Set<String> appNames = Arrays.stream(ctx.getAppName().split(",")).filter(StringUtils::isNotEmpty)
                    .map(name -> "'" + name + "'").collect(Collectors.toSet());
            if (!appNames.isEmpty()) {
                params.put("app_name", "and app_name in ( " +  String.join(",", appNames) + " )");
            } else {
                params.put("app_name", "");
            }
            params.put("app_type", "");
        } else if (StringUtils.isNotEmpty(ctx.getAppType())) {
            Set<String> appTypes = Arrays.stream(ctx.getAppType().split(",")).filter(StringUtils::isNotEmpty)
                    .map(type -> "'" + type + "'").collect(Collectors.toSet());
            if (!appTypes.isEmpty()) {
                params.put("app_type", "and app_type in ( " + String.join(",", appTypes) + " )");
            } else {
                params.put("app_type", "");
            }
            params.put("app_name", "");
        } else {
            params.put("app_name", "");
            params.put("app_type", "");
        }

        //构造档案特定的参数
        buildArcSpecifiedParams(params, ctx);

        //获取doris sql模板
        String pieTemplate = appAnalyzeTemplate(ctx, PIE_CHART);

        List<Map<String, Object>> list = arcCommonService.getCommonServiceListResult(pieTemplate, params, true, 1, ctx.getTopSize());
        List<RadiusAppIncrementalItemModel> outAllDataList = JSONObject.parseArray(JSONObject.toJSONString(list), RadiusAppIncrementalItemModel.class);

        HashMap<String, Object> result = new HashMap<>(2);

        if (outAllDataList == null || outAllDataList.isEmpty()) {
            result.put("outer", new ArrayList<>());
            result.put("lineChart", new ArrayList<>());
            return result;
        }

        calculateRate(outAllDataList);
        result.put("outer", outAllDataList);

        // 获取分组查询的过滤条件
        List<String> names = outAllDataList.stream().map(item -> "'" + item.getName() + "'").collect(Collectors.toList());

        if (ctx.getShowDimension() == 0) {
            params.put("app_type", " and app_type in ( " + String.join("," , names) + ") ");
        } else {
            params.put("app_name", " and app_name in ( " + String.join("," , names) + ") ");
        }

        String lineChartTemplate = appAnalyzeTemplate(ctx, LINE_CHART);
        List<Map<String, Object>> lineCharts = arcCommonService.getCommonServiceListResult(lineChartTemplate, params, true);
        List<RadiusAppIncrementalItemModel> lineChartList = JSONObject.parseArray(JSONObject.toJSONString(lineCharts), RadiusAppIncrementalItemModel.class);

        List<RadiusAppIncrementalItemTimeModel> lineChartData = formatLineChart(lineChartList, ctx.getStartDay(), ctx.getEndDay());

        lineChartData.forEach(line -> {
            line.getDetail().forEach(detail -> {
                String time = detail.getTime();
                detail.setTime(time.substring(6, 8) + "-" + time.substring(4, 6) + "-"  + time.substring(0, 4));
            });
        });

        result.put("lineChart", lineChartData);

        return result;
    }

    private List<RadiusAppIncrementalItemTimeModel> formatLineChart(List<RadiusAppIncrementalItemModel> lineChartData, String startDay, String endDay) {

        if (lineChartData == null || lineChartData.isEmpty()) {
            return new ArrayList<>();
        }

        List<RadiusAppIncrementalItemTimeModel> lienChartDetail = Lists.newArrayList();
        List<Integer> timeList = DateUtils.getDayList(startDay, endDay);

        Map<String, List<RadiusAppIncrementalItemModel>> collects = lineChartData.stream().collect(Collectors.groupingBy(RadiusAppIncrementalItemModel::getName, Collectors.mapping(line -> line, Collectors.toList())));

        for (String key : collects.keySet()) {

            RadiusAppIncrementalItemTimeModel radiusAppIncrementalItemTimeModel = new RadiusAppIncrementalItemTimeModel();
            radiusAppIncrementalItemTimeModel.setName(key);

            List<RadiusAppIncrementalItemModel> radiusAppIncrementalItemModels = new ArrayList<>();
            Map<Integer, RadiusAppIncrementalItemModel> timeCollects = collects.get(key).stream().collect(Collectors.toMap(line -> Integer.valueOf(String.valueOf(line.getTime())), line -> line));
            for (Integer timePoint: timeList) {
                if (timeCollects.containsKey(timePoint)) {
                    radiusAppIncrementalItemModels.add(timeCollects.get(timePoint));
                } else {
                    RadiusAppIncrementalItemModel model = new RadiusAppIncrementalItemModel();
                    model.setName(key);
                    model.setTime(timePoint + "");

                    radiusAppIncrementalItemModels.add(model);
                }
            }
            Collections.sort(radiusAppIncrementalItemModels);
            radiusAppIncrementalItemTimeModel.setDetail(radiusAppIncrementalItemModels);
            lienChartDetail.add(radiusAppIncrementalItemTimeModel);
        }
        return lienChartDetail;
    }

    private void calculateRate(List<RadiusAppIncrementalItemModel> outAllDataList) {

        long total = outAllDataList.stream().mapToLong(RadiusAppIncrementalItemModel::getValue).sum();
        for (RadiusAppIncrementalItemModel radiusAppIncrementalItemModel : outAllDataList) {
            double v = (double) radiusAppIncrementalItemModel.getValue() * 100/ (double) total;
            radiusAppIncrementalItemModel.setRate(getDouble(v));
        }
    }

    private Double getDouble(Double rate) {
        double result = 0.0;
        Formatter formatter = null;
        try {
            formatter = new Formatter();
            result = Double.parseDouble(formatter.format("%.2f", rate).toString());
        }catch (Exception e){

        }finally {
            IOUtils.closeQuietly(formatter);
        }

        return result;
    }

    protected abstract void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx);


    /**
     * @param ctx 查询上下文
     * @param type 查询的统计图表类型，分为pie和lineChart
     * @return 查询的模板业务码
     */
    protected abstract String appAnalyzeTemplate(ArcContext ctx, String type);

    public List<Integer> getTimeFrameInterval(Integer interval) {
        Integer[] timeFrameIntervalArr;
        switch (interval) {
            case 1:
                timeFrameIntervalArr = new Integer[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24};
                break;
            case 2:
                timeFrameIntervalArr = new Integer[]{0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24};
                break;
            case 3:
                timeFrameIntervalArr = new Integer[]{0, 3, 6, 9, 12, 15, 18, 21, 24};
                break;
            case 4:
                timeFrameIntervalArr = new Integer[]{0, 4, 8, 12, 16, 20, 24};
                break;
            case 6:
                timeFrameIntervalArr = new Integer[]{0, 6, 12, 18, 24};
                break;
            case 8:
                timeFrameIntervalArr = new Integer[]{0, 8, 16, 24};
                break;
            case 12:
                timeFrameIntervalArr = new Integer[]{0, 12, 24};
                break;
            default:
                timeFrameIntervalArr = new Integer[]{0, 24};
                break;
        }
        return Arrays.asList(timeFrameIntervalArr);
    }

    @Override
    public JSONObject getAppList(ArcContext ctx) {

        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam();
        params.put("start_day", ctx.getStartDay());
        params.put("end_day", ctx.getEndDay());

        //排序字段
        String orderType = SortTypeEnum.getNameByCode(ctx.getSortType());
        params.put(CommonConstent.ORDER_TYPE, orderType);
        params.put(CommonConstent.ORDER_FIELD, ctx.getSortField() + " " + orderType + ", arcAccount ");


        //构造档案特定的参数
        buildArcSpecifiedParams(params, ctx);

        //获取doris sql模板
        String appListTemplate = appListTemplate(ctx);
        Long total = 0L;

        List<Map<String, Object>> commonServiceListResult = arcCommonService.getCommonServiceListResult(appListTemplate, params, PageWarpEntity.build(ctx.getOnPage(), ctx.getSize(), ctx.getSortField(), ctx.getSortType()));
        total = arcCommonService.getCommonServiceCountResult( appListTemplate, params, true);


        if (ObjectUtil.isNull(total) || total < 0L){
            total = 0L;
        }
        JSONObject resultJson = new JSONObject();
        if (CollectionUtil.isEmpty(commonServiceListResult)) {
            resultJson.put("total", 0);
        } else {
            resultJson.put("total", total);
        }
        resultJson.put("list", commonServiceListResult);

        return resultJson;
    }

    protected abstract String appListTemplate(ArcContext ctx);

}
