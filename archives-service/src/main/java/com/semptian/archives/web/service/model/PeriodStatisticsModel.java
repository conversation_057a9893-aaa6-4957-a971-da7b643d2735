package com.semptian.archives.web.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 时段分析模型
 * <AUTHOR>
 * @Date 2021/06/13
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PeriodStatisticsModel implements Comparable{

    /**
     * 时段
     */
    private Integer period;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        PeriodStatisticsModel virtualAccountCountModel = (PeriodStatisticsModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
