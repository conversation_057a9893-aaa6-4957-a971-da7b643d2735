package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class RadiusArcHomePageService extends AbstractArcHomePageService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.RADIUS;
    }

    @Override
    protected Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("authAccount", authAccounts);
        params.put("authAccountType", AuthTypeEnum.RADIUS.getType());
        return params;
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        params.put("arc_account_type", AuthTypeEnum.RADIUS.getType());
    }

    @Override
    protected Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        return new HashMap<>(DateUtils.getLatest7Day());
    }

    @Override
    protected String behaviorKey(Integer behaviorType) {
        if (behaviorType == 2) {
            return "pr";
        }
        return "nf";
    }

    @Override
    public String activeTrendTemplate(ArcContext ctx) {
        return BusinessCodeEnum.COMMON_RADIUS_DAY_TREND_ANALYZE.getValue();
    }

    @Override
    public String periodStatisticsTemplate() {
        return BusinessCodeEnum.COMMON_RADIUS_DAY_HOUR_ANALYZE.getValue();
    }

    @Override
    public void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.put("auth_account", EscapeUtil.escapeSingleQuote(ctx.getArcAccount()));
        params.put("auth_account_type", AuthTypeEnum.RADIUS.getType());
    }

    @Override
    public String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        switch (accessTargetTopEnum) {
            case VIRTUAL_ACCOUNT:
                return BusinessCodeEnum.RADIUS_TOP_VIRTUAL_ACCOUNT.getValue();
            case APP:
                return BusinessCodeEnum.RADIUS_TOP_APP.getValue();
        }
        return null;
    }
}
