package com.semptian.archives.web.service.service;


import com.semptian.base.builders.QueryBuilder;
import com.semptian.base.model.TianhePageModel;

import java.util.List;
import java.util.Map;

/**
 * ES查询服务接口
 * <AUTHOR>
 * @date 2024/3/19
 */
public interface EsQueryService {

    /**
     * 根据ID集合查询记录
     *
     * @param indexs 索引名
     * @param field  id字段名称
     * @param ids    id
     * @return Map类型的结果集
     */
    List<Map<String, Object>> getRecordsByIds(String field, List<String> ids, String... indexs);

    /**
     * 分页查询
     * @param onPage 页码
     * @param size 条数
     * @param queryBuilder es查询条件
     * @param sortFields 排序字段
     * @param timeOut 超时时间
     * @param terminateAfter 查询截断条数
     * @param indexs 索引
     * @return 分页结果
     */
    @SuppressWarnings("rawtypes")
    TianhePageModel query(Integer onPage, Integer size, QueryBuilder queryBuilder, Map<String, String> sortFields, long timeOut, Integer terminateAfter, String... indexs);
}
