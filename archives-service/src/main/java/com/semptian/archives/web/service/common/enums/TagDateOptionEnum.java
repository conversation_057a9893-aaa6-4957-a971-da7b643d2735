package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 标签时间快捷选项枚举
 * <AUTHOR>
 * @date 2024/9/27
 */

@Getter
public enum TagDateOptionEnum {
    // 近7天
    LAST_7_DAYS(1),

    // 本月
    CURRENT_MONTH(2),

    // 上月
    LAST_MONTH(3),

    // 近三月
    LAST_THREE_MONTHS(4),

    // 自定义选项
    CUSTOM(0);

    private final Integer value;

    TagDateOptionEnum(Integer value) {
        this.value = value;
    }

    /**
     * 根据值获取枚举
     * @param value 值
     * @return 枚举
     */
    public static TagDateOptionEnum getDateOptionByValue(Integer value) {
        for (TagDateOptionEnum dateOptionEnum : TagDateOptionEnum.values()) {
            if (dateOptionEnum.getValue().equals(value)) {
                return dateOptionEnum;
            }
        }
        return CUSTOM;
    }
}
