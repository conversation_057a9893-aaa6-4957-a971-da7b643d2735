package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PhoneArcHomePageService extends AbstractArcHomePageService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.PHONE;
    }

    @Override
    protected Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("phone", authAccounts.replaceAll("\\+", "").replaceAll("-", ""));

        params.put("authAccountType", AuthTypeEnum.PHONE.getType());
        return params;
    }

    @Override
    protected Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {

        if (accessTargetTopEnum.equals(AccessTargetTopEnum.VIRTUAL_ACCOUNT)) {
            return new HashMap<>(DateUtils.getPast7Day());
        }

        return new HashMap<>(DateUtils.getLatest7Day());
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {

    }

    @Override
    protected String behaviorKey(Integer behaviorType) {
        if (behaviorType == 1) {
            return "call";
        } else if (behaviorType == 2) {
            return "pr";
        }
        return "nf";
    }

    @Override
    public String activeTrendTemplate(ArcContext ctx) {
        return BusinessCodeEnum.COMMON_PHONE_DAY_TREND_ANALYZE.getValue();
    }

    @Override
    public String periodStatisticsTemplate() {
        return BusinessCodeEnum.COMMON_PHONE_DAY_HOUR_ANALYZE.getValue();
    }

    @Override
    public void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.put("auth_account", EscapeUtil.escapeSingleQuote(ctx.getArcAccount().replaceAll("\\+", "").replaceAll("-", "")));
    }

    @Override
    public String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        switch (accessTargetTopEnum) {
            case VIRTUAL_ACCOUNT:
                return BusinessCodeEnum.PHONE_TOP_VIRTUAL_ACCOUNT.getValue();
            case APP:
                return BusinessCodeEnum.PHONE_TOP_APP.getValue();
            case PHONE:
                return BusinessCodeEnum.PHONE_TOP_PHONE.getValue();

        }
        return null;
    }
}
