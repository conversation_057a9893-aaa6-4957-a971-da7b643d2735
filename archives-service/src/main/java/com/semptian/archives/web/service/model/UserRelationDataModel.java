package com.semptian.archives.web.service.model;


import lombok.Data;

import java.util.List;

/**
 * 档案关系扩线版本信息model
 *
 * <AUTHOR>
 * @date 2024/3/28
 */
@Data
public class UserRelationDataModel{

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户关系扩线版本号
     */
    private Integer version;

    /**
     * 保存的扩线的json数据
     */
    private String relationData;

    /**
     * 表单选择输入的参数json
     */
    private String params;

    /**
     * 档案id
     */
    private String arcId;

    /**
     * 档案id
     */
    private String arcAccount;

    /**
     * 档案类型
     */
    private Integer arcType;

    /**
     * 新建时间
     */
    private Long createTime;

    /**
     * 保存的扩线档案名称
     */
    private String relationName;

    /**
     * 已扩线档案id
     */
    private List<String> expansionArcIds;

    //以下是档案相关信息
    /**
     * 档案类型:1=radius、2=email、3=website、4=app、5=phone、6=im、7=protocol、8=fixed_ip
     */
    private Integer archivesType;

    /**
     * 档案账号类型，对于radius=1020001、固定IP档案=1029997
     */
    private String arcAccountType;

    /**
     * 档案创建时间
     */
    private Long arcCreateTime;
}
