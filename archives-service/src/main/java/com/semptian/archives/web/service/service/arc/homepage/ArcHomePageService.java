package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.ArcFeatures;
import com.semptian.archives.web.service.service.arc.ArcFunctional;

import java.util.List;
import java.util.Map;

/**
 * 档案概览服务
 */
public interface ArcHomePageService extends ArcFunctional {

    default ArcFeatures feature() {
        return ArcFeatures.HOME_PAGE;
    }

    /**
     * 查询档案概览中的维度统计信息
     * @param archiveModels 档案信息
     * @param isAll 是否仅查询维度的总数统计信息，0 = 同时查询近7天的数据，1 = 仅查询总数统计信息
     * @param index 统计维度。 1 = 行为次数，2 = 应用数， 3 = 通联区域数/访问区域数， 4 = 虚拟账号数， 5 = 阻断行为数， 6 = 文件个数， 7 = 阻断应用数
     *              8 = 通话次数， 9 = 传真次数， 10 = 短信条数， 11 = 重点目标访问数， 12 = 访问档案个数， 13 = 占用IP个数
     * @param userId 用户ID
     * @return 各维度对应的统计信息。默认包含近7天的数据
     */
    Object getStatisticalDimensions(ArcTypeEnum arcType, List<ArchiveModel> archiveModels,
                                    Integer isAll,
                                    Integer index,
                                    String userId);

    /**
     * 获取首页访问者top
     *
     * @param ctx 查询上下文
     * @param accessTargetTopEnum 访问目标top枚举
     * @return 首页访问者top
     */
    Map<String, Object> getAccessTargetTop(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum);

    /**
     * 获取活跃趋势分析
     * @param ctx 查询上下文
     * @return 活跃趋势
     */
    Map<String,List<ActiveTrendModel>> getActiveTrend(ArcContext ctx);

    /**
     * 获取时段分析
     * @param ctx 查询上下文
     * @return 时段分析
     */
    Map<String, List<PeriodStatisticsModel>> getPeriodStatistics(ArcContext ctx);

    /**
     * 获取通联区域
     * @param ctx 查询上下文
     * @return 通联区域
     */
    CommunicationAreaStatisticsModel getCommunicationArea(ArcContext ctx);
}
