package com.semptian.archives.web.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.entity.UserRelationDataEntity;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.DeleteModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.model.UserRelationDataModel;
import com.semptian.base.service.ReturnModel;

import java.util.List;
import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-01-12 19:48:39
 */
public interface UserRelationDataService extends IService<UserRelationDataEntity> {

    /**
     * 获取最大版本号
     * @param arcId 档案id
     * @param userId 用户id
     * @return 版本号
     */
    Integer getMaxVersion(String arcId,String userId);

    /**
     * 根据档案id,用户Id，和版本号查询
     * @param userId 用户id
     * @param relationName 关系名称
     * @return 关系信息
     */
    UserRelationDataEntity queryExistByRelationName(String userId,String arcId, String relationName);

    /**
     * 根据条件查询用户保存的版本信息
     * @param arcId 档案id
     * @param userId 用户id
     * @param dateModel 日期模型
     * @param keyWord 关键字
     * @param pageWarpEntity 分页参数
     * @param permissionList 权限列表
     * @return 版本信息
     */
    Map<String, Object> getRelationInfoVersion(String arcId, String userId, DateModel dateModel, String keyWord, PageWarpEntity pageWarpEntity, List<Integer> permissionList);

    /**
     * 保存用户关系扩线数据
     * @param userRelationDataModel 用户关系扩线数据
     * @return 用户关系扩线数据
     */
    ReturnModel<?> saveRelationData(UserRelationDataModel userRelationDataModel);

    /**
     * 保存档案扩线节点关系
     * @param arc1Id 开始节点档案ID
     * @param arc2Id 结束节点档案ID
     * @param connectType 关系类型
     * @param expansionArcId 当前档案ID
     * @param lang 语种
     * @return 保存结果
     */
    Object connectCreate(String arc1Id, String arc2Id, String connectType, String expansionArcId, String lang);

    /**
     * 保存档案扩线节点备注
     */
    ReturnModel<?> remarkSave(ArcRemarkEntity arcRemark, String userId);

    /**
     * 批量删除扩线版本信息
     * @param deleteModel 删除参数
     */
    void delete(DeleteModel deleteModel);
}

