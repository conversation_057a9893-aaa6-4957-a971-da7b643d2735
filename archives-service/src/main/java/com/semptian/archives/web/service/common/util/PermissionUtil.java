package com.semptian.archives.web.service.common.util;

import com.semptian.archives.web.core.common.enums.ArcTypeEnum;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: SunQi
 * @create: 2022/05/26
 * desc:
 **/
public class PermissionUtil {
    public static List<Integer> getArcPermission(HttpServletRequest request){
        return  (List<Integer>)request.getAttribute("permission");
    }
    public static boolean hasAllPermission(List<Integer> permissionList){
        return permissionList.containsAll(ArcTypeEnum.keys());
    }
}
