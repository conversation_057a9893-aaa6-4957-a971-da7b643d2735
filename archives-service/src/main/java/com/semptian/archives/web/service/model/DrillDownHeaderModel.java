package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/3/29 14:02
 * Description: 获取明细表头模型
 */
@ApiModel("获取明细表头模型")
@Data
public class DrillDownHeaderModel {


    /**
     * 档案ID
     */
    @ApiModelProperty(value = "数据类型")
    @NotEmpty
    private Integer dataType;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "是否全部")
    private Integer isAll = 1;

    @ApiModelProperty(value = "是否自定义列 1-是 0-否")
    private Integer isCustomField = 0;
}
