package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.dao.archive.entity.ArcDictEntity;
import com.semptian.archives.web.dao.archive.entity.ArcDrillDownFieldTranslateConfigEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcDictMapper;
import com.semptian.archives.web.dao.archive.mapper.ArcDrillDownFieldTranslateConfigMapper;
import com.semptian.archives.web.service.common.enums.ArcDrillDownFieldTranslateTypeEnum;
import com.semptian.archives.web.service.common.enums.LanguageEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.fegin.ResourceFeign;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.base.service.ReturnModel;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 明细下钻字段翻译配置服务实现
 *
 * <AUTHOR> Hu
 * @date 2024/4/2
 */
@Service
@Slf4j
public class ArcDrillDownFieldTranslateConfigServiceImpl extends ServiceImpl<ArcDrillDownFieldTranslateConfigMapper, ArcDrillDownFieldTranslateConfigEntity> implements ArcDrillDownFieldTranslateConfigService {

    @Resource
    private RedisOps redisOps;

    @Resource
    private ArcDictMapper arcDictMapper;

    @Resource
    private ResourceFeign resourceFeign;

    @Value("${archive.field.translate.cache.init:true}")
    private Boolean open;

    private String getRedisKeyByDictKeyAndDictCode(String dictKey, String code) {
        return "drillDownFieldTranslateConfigDict:" + dictKey + ":" + code;
    }

    private String getTagRedisKeyByTagNameLatitude(String tagNameLatitude) {
        return "tagNameFieldTranslateConfigDict:" + tagNameLatitude;
    }

    private String getTagRedisKeyByAllTagNameLatitude() {
        return "tagNameFieldTranslateConfigDict:allTagNameLatitude";
    }

    private String getRedisKeyByAllField() {
        return "drillDownFieldTranslateConfig:allFieldAndTranslateType";
    }

    @PostConstruct
    public void tagNameTranslateConfigInit() {
        try {
            for (LanguageEnum languageEnum : LanguageEnum.values()) {

                ReturnModel returnModel = resourceFeign.tagValueList(null, languageEnum.getLang());
                if (null != returnModel.getData()) {
                    List<Map<String, Object>> records = (List<Map<String, Object>>) returnModel.getData();
                    List<String> allTagNameList = Lists.newArrayList();
                    for (Map<String, Object> record : records) {
                        if (record.containsKey("asName") && record.containsKey("name")) {
                            String redisKey = getTagRedisKeyByTagNameLatitude(record.getOrDefault("asName", "").toString());
                            redisOps.hset(redisKey, languageEnum.getLang(), record.getOrDefault("name", "").toString());
                            allTagNameList.add(record.getOrDefault("asName", "").toString() + ":" + record.getOrDefault("name", "").toString());
                        }
                    }
                    redisOps.hset(getTagRedisKeyByAllTagNameLatitude(), languageEnum.getLang(), String.join(",", allTagNameList));
                }
            }
        } catch (Exception e) {
            log.error("tagNameTranslateConfigInit error", e);
        }
    }

    @PostConstruct
    public void drillDownFieldTranslateConfigInit() {
        if (open) {
            log.info("ArcDrillDownFieldTranslateConfig init");
            try {
                List<Map.Entry<String, List<ArcDictEntity>>> dictList = new ArrayList<>(arcDictMapper.selectList(null).stream().collect(Collectors.groupingBy(ArcDictEntity::getDictKey)).entrySet());

                //将字典key+字典code作为key,语种以及对应翻译值放入Redis中
                for (Map.Entry<String, List<ArcDictEntity>> entry : dictList) {
                    String dictKey = entry.getKey();
                    List<ArcDictEntity> valueList = entry.getValue();

                    for (ArcDictEntity arcDict : valueList) {
                        String redisKey = getRedisKeyByDictKeyAndDictCode(dictKey, arcDict.getDictCode());

                        redisOps.hset(redisKey, LanguageEnum.ZH_CN.getLang(), arcDict.getDictNameZh());
                        redisOps.hset(redisKey, LanguageEnum.FR_DZ.getLang(), arcDict.getDictNameFr());
                        redisOps.hset(redisKey, LanguageEnum.EN_US.getLang(), arcDict.getDictNameEn());
                    }
                }

            } catch (Exception e) {
                log.error("drillDownFieldTranslateConfig init error", e);
            }

            try {
                //获取所有待翻译的字段、对应协议ID、翻译类型、以及字典key, 放入Redis中
                List<ArcDrillDownFieldTranslateConfigEntity> allFieldAndTranslateType = this.list();

                for (ArcDrillDownFieldTranslateConfigEntity arcDrillDownFieldTranslateConfig : allFieldAndTranslateType) {
                    String field = arcDrillDownFieldTranslateConfig.getField() + ";" + arcDrillDownFieldTranslateConfig.getProtocolId();
                    String translateType = String.valueOf(arcDrillDownFieldTranslateConfig.getTranslateType());

                    if (ArcDrillDownFieldTranslateTypeEnum.DICT.getTranslateType().equals(arcDrillDownFieldTranslateConfig.getTranslateType())) {
                        translateType = translateType + ";" + arcDrillDownFieldTranslateConfig.getDictKey();
                    }

                    redisOps.hset(getRedisKeyByAllField(), field, translateType);
                }
            } catch (Exception e) {
                log.error("drillDownFieldTranslateConfig init error", e);
            }
        }
    }

    /**
     * 根据下钻字段名称、下钻字段值、以及语种获取对应字典翻译值
     *
     * @param result 下钻结果数据包含数据的字段名称、字段值
     * @param lang   语种
     * @return 字典翻译后值
     */
    @Override
    public List<Map<String, Object>> fieldTranslate(List<Map<String, Object>> result, String lang, Integer protocolId) {
        try {
            log.info("field translate start");
            long start = System.currentTimeMillis();

            //补充Redis数据异常丢失情况下，重新读取数据加载到缓存
            if (!redisOps.hasKey(getRedisKeyByAllField())) {
                this.drillDownFieldTranslateConfigInit();
            }

            if (CollUtil.isEmpty(result)) {
                log.info("result is empty ,No translation required");
                return Lists.newArrayList();
            }

            //为减小循环中查询Redis次数，遍历完成第一条数据后就保存需要翻译的字段和翻译类型
            Map<String, Object> fieldMap = new HashMap<>(16);
            boolean isFirstLoop = true;

            for (Map<String, Object> objectMap : result) {

                for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
                    String field = entry.getKey();
                    Object value = entry.getValue();

                    //判断数据值非空
                    if (ObjectUtil.isNull(value) || StrUtil.isEmpty(value.toString())) {
                        if (!isFirstLoop) {
                            continue;
                        }
                    }

                    //判断字段是否需要翻译
                    Object translateType;
                    if (isFirstLoop) {
                        //从redis中获取判断当前字段是否需要翻译
                        translateType = getTranslateTypeByFieldAndProtocolId(field, protocolId, fieldMap);
                    } else {
                        //从map中获取判断当前字段是否需要翻译
                        translateType = getTranslateTypeByFieldAndProtocolIdByMap(field, protocolId, fieldMap);
                    }
                    if (ObjectUtil.isNull(translateType)) {
                        continue;
                    }

                    Object translateValue = value;

                    //根据翻译类型对字段翻译
                    String[] translateTypeArr = translateType.toString().split(";");
                    if (ArrayUtil.isEmpty(translateTypeArr)) {
                        continue;
                    }

                    ArcDrillDownFieldTranslateTypeEnum typeEnumByTranslateType = ArcDrillDownFieldTranslateTypeEnum.getTypeEnumByTranslateType(Integer.valueOf(translateTypeArr[0]));
                    switch (typeEnumByTranslateType) {
                        case DICT:
                            if (translateTypeArr.length == 2) {
                                String dictKey = translateTypeArr[1];
                                if (value != null) {
                                    Object newValue = redisOps.hget(getRedisKeyByDictKeyAndDictCode(dictKey, value.toString()), lang);
                                    if (ObjectUtil.isNotNull(newValue)) {
                                        translateValue = newValue;
                                    }
                                }
                            }

                            break;
                        case TIMESTAMP_FORMAT:
                            if (value instanceof Long || value instanceof String) {
                                if (!"0".equals(value)) {
                                    translateValue = DateUtils.format(DateUtil.date(Long.parseLong(value.toString())), "dd-MM-yyyy HH:mm:ss");
                                } else {
                                    translateValue = "-";
                                }
                            }
                            break;
                        case DATE_FORMAT:
                            if (value instanceof String) {
                                translateValue = DateUtils.format(DateUtil.parse((String) value, DateUtils.dateFormat), DateUtils.DATE_TO_DATE_FORMAT);
                            }
                            break;
                        case COUNTRY_CODE:
                            if (value != null) {
                                Object countryValue = redisOps.hget(getRedisKeyByDictKeyAndDictCode(CommonConstent.COUNTRY_CODE_FIELD, value.toString()), lang);
                                if (ObjectUtil.isNotNull(countryValue)) {
                                    translateValue = countryValue;
                                }
                            }
                            break;
                    }
                    entry.setValue(translateValue);
                }

                isFirstLoop = false;
            }

            log.info("field translate end, cost:{}ms", (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("fieldTranslate error ", e);
        }
        return result;
    }

    /**
     * @param fieldName 字段名称
     * @param code      字典编码
     * @param lang      语种
     * @return 字典翻译后值
     */
    @Override
    public Object fieldTranslate(String fieldName, Object code, String lang) {
        if (ObjectUtil.isNull(code) || StrUtil.isEmpty(code.toString()) || StrUtil.isEmpty(lang)) {
            return code;
        }

        Object newValue = redisOps.hget(getRedisKeyByDictKeyAndDictCode(fieldName, code.toString()), lang);
        if (ObjectUtil.isNotNull(newValue)) {
            return newValue;
        }

        return code;
    }

    @Override
    public Object fieldTranslate(String fieldName, Object code) {
        return this.fieldTranslate(fieldName, code, LanguageEnum.FR_DZ.getLang());
    }

    @Override
    public Object tagNameTranslate(String tagNameLatitude, String lang) {

        if (ObjectUtil.isNull(tagNameLatitude) || StrUtil.isEmpty(tagNameLatitude)) {
            return tagNameLatitude;
        }

        if (tagNameLatitude.contains(",")) {
            String[] tagNameArr = tagNameLatitude.split(",");
            List<String> tagNameList = Lists.newArrayList();
            for (String tagName : tagNameArr) {
                Object newValue = redisOps.hget(getTagRedisKeyByTagNameLatitude(tagName), lang);
                if (ObjectUtil.isNotNull(newValue)) {
                    tagNameList.add(newValue.toString());
                }
            }
            return String.join(",", tagNameList);
        } else {
            Object newValue = redisOps.hget(getTagRedisKeyByTagNameLatitude(tagNameLatitude), lang);
            if (ObjectUtil.isNotNull(newValue)) {
                return newValue;
            }
        }

        return tagNameLatitude;
    }

    @Override
    public Object getAllTagNameTranslate(String lang) {

        Object newValue = redisOps.hget(getTagRedisKeyByAllTagNameLatitude(), lang);
        if (ObjectUtil.isNotNull(newValue)) {
            return newValue;
        }
        return "";
    }

    /**
     * 根据字段和协议ID获取字段翻译类型
     *
     * @param field      字段
     * @param protocolId 协议ID
     * @return 字段翻译类型
     */
    private Object getTranslateTypeByFieldAndProtocolId(String field, Integer protocolId, Map<String, Object> fieldMap) {
        Object translateType;
        String fieldAndProtocolId = field + ";" + protocolId;
        //优先取对应协议的字段翻译类型
        translateType = redisOps.hget(getRedisKeyByAllField(), fieldAndProtocolId);

        if (ObjectUtil.isNull(translateType)) {
            //取不到时再按照通用协议取字段翻译类型
            fieldAndProtocolId = field + ";" + CommonConstent.COMMON_ALL_PROTOCOL_ID;
            translateType = redisOps.hget(getRedisKeyByAllField(), fieldAndProtocolId);
        }

        if (ObjectUtil.isNotNull(translateType)) {
            fieldMap.put(fieldAndProtocolId, translateType);
        }
        return translateType;
    }

    /**
     * 根据字段和协议ID获取字段翻译类型
     *
     * @param field      字段
     * @param protocolId 协议ID
     * @return 字段翻译类型
     */
    private Object getTranslateTypeByFieldAndProtocolIdByMap(String field, Integer protocolId, Map<String, Object> fieldMap) {
        Object translateType;
        String fieldAndProtocolId = field + ";" + protocolId;
        //优先取对应协议的字段翻译类型
        translateType = fieldMap.get(fieldAndProtocolId);

        if (ObjectUtil.isNull(translateType)) {
            //取不到时再按照通用协议取字段翻译类型
            fieldAndProtocolId = field + ";" + CommonConstent.COMMON_ALL_PROTOCOL_ID;
            translateType = fieldMap.get(fieldAndProtocolId);
        }

        return translateType;
    }
}