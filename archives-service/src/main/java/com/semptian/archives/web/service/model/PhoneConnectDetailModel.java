package com.semptian.archives.web.service.model;

import lombok.Data;

/**
 * @author: SunQi
 * @create: 2021/07/30
 * desc: 手机号码通联详情模型
 **/
@Data
public class PhoneConnectDetailModel {

    /**
     * 主叫手机号
     */
    private String srcNumber;
    /**
     * 被叫手机号
     */
    private String dstNumber;
    /**
     * 主叫国家
     */
    private String srcCountry;
    /**
     * 被叫国家
     */
    private String dstCountry;
    /**
     * 子协议
     */
    private String callTag;
    /**
     * 短信文本
     */
    private String smsText;
    /**
     * 动作
     */
    private String action;
    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 时长
     */
    private Long continueTime;

    /**
     * 语音文件路径
     */
    private String filePath;
}
