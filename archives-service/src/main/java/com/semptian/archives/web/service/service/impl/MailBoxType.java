package com.semptian.archives.web.service.service.impl;

import lombok.Getter;

/**
 * 邮箱类型
 * <AUTHOR>
 */
@Getter
public enum MailBoxType {

    // 收件箱
    INBOX("inbox"),

    // 发件箱
    OUTBOX("outbox"),

    // 草稿箱
    DRAFT("draft");

    private final String name;

    MailBoxType(String name) {
        this.name = name;
    }

    public static MailBoxType getByName(String emailType) {
        for (MailBoxType mailBoxType : MailBoxType.values()) {
            if (mailBoxType.getName().equals(emailType)) {
                return mailBoxType;
            }
        }

        return null;
    }
}
