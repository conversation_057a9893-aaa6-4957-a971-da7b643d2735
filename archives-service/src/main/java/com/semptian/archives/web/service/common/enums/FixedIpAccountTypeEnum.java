package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 固定IP账号类型枚举
 *
 * <AUTHOR>
 * @date 2024/3/29
 */
@Getter
public enum FixedIpAccountTypeEnum {

    INDIVIDUAL(1, "individual"),

    GOVERNMENT_ENTERPRISE(2, "government_enterprise");

    private final Integer code;

    private final String name;

    FixedIpAccountTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        FixedIpAccountTypeEnum[] values = FixedIpAccountTypeEnum.values();
        for (FixedIpAccountTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return "";
    }
}
