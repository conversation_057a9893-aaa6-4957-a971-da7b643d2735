package com.semptian.archives.web.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RadiusAppIncrementalItemModel implements Comparable<RadiusAppIncrementalItemModel> {
    private String name;
    private String typeName;
    private long value;
    private double rate;
    private String index;
    private String time;

    @Override
    public int compareTo(RadiusAppIncrementalItemModel o) {
        return (int) (Long.parseLong(this.time)- Long.parseLong(o.time));
    }
}
