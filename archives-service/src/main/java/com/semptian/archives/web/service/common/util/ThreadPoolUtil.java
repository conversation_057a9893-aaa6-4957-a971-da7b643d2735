package com.semptian.archives.web.service.common.util;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;

import static java.util.concurrent.Executors.newFixedThreadPool;

/**
 * <AUTHOR>
 * Date: 2020/09/10 09:31
 * Modified:
 * Description:
 */
public class ThreadPoolUtil {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolUtil.class);

    private static ExecutorService THREAD_POOL_EXECUTOR = newFixedThreadPool(
            getCPUCore(),
            new ThreadFactoryBuilder().setNamePrefix("common thread pool").build());

    private ThreadPoolUtil() {
    }

    /**
     *
     *
     * @return
     */
    public static ExecutorService getDefaultExecutorInstance() {
        return THREAD_POOL_EXECUTOR;
    }

    /**
     *
     *
     * @return
     */
    private static int getCPUCore() {
        int availProcess = Runtime.getRuntime().availableProcessors();
        logger.info("availProcess : {}", availProcess);
        return 2 * availProcess + 1;
    }

}
