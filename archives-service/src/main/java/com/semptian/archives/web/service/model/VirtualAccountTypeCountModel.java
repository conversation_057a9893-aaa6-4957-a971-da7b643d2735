package com.semptian.archives.web.service.model;

import lombok.Data;

/**
 * @Description 虚拟账号类型统计模型
 * <AUTHOR>
 * @Date 2020/11/10
 */

@Data
public class VirtualAccountTypeCountModel implements Comparable{

    /**
     * 虚拟账号类型
     */
    private String accountType;

    /**
     * 最后关联时间
     */
    private long lastFindTime;

    /**
     * 关联次数
     */
    private Integer count;

    @Override
    public int compareTo(Object o) {
        VirtualAccountTypeCountModel virtualAccountTypeCountModel = (VirtualAccountTypeCountModel) o;
        int times = virtualAccountTypeCountModel.count - this.count;
        return times;
    }
}
