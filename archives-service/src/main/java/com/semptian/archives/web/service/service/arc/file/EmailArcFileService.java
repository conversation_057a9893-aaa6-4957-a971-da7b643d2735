package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Service;

@Service
public class EmailArcFileService extends AbstractArcFileService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.EMAIL;
    }

    @Override
    protected void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx) {
        params.put("account_type_condition", "virtual_account");
        params.put("dataType", " AND data_type = " + DataTypeEnum.EMAIL.getKey() + " ");
    }

    @Override
    protected void buildKeywordCondition(CommonParamUtil.ParamMap params, String keyword) {
        params.put("keyword", " AND (lower(attach_name) LIKE CONCAT('%','" + keyword + "','%') OR  " +
                "lower(auth_account) LIKE CONCAT('%','" + keyword + "','%') )");
    }
}
