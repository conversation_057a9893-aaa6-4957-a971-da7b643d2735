package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 档案ES检索条件字段名称枚举类
 * <AUTHOR>
 * @date 2024/3/19
 */
@Getter
public enum ArcQueryEsConditionEnum {

    // 档案账号
    ARCHIVE_NAME("archive_name"),

    // 档案名称(别名)
    ARCHIVE_ALIAS("archive_alias"),

    // 最后活跃时间
    LAST_RELATION_TIME("latest_relation_time"),

    // 最后活跃国家
    LATEST_RELATION_COUNTRY("latest_relation_country.keyword"),

    // 最后活跃城市
    LATEST_RELATION_CITY("latest_relation_city.keyword"),

    // 是否radius，默认为全部0=否1=是99=全部，号码档案查询条件
    IS_RADIUS("internet_behavior_num"),

    // 是否有传真，默认为全部, 0=否1=有99=全部，号码档案查询条件
    HAS_FAX("fax_num"),

    // 应用类型，应用档案查询条件
    APP_TYPE("app_type"),

    // 应用名称，IM档案查询条件，预留字段
    APP_NAME("appName"),

    // 是否有文件，默认为全部，0=无1=有99=全部，网站档案查询条件、（Email和协议档案也有，本版本不实现）
    FILE_FLAG("file_flag"),

    // 账号类型，默认为全部, 1 个人 2 政企99=全部
    ACCOUNT_TYPE("account_type");

    private final String fieldName;

    ArcQueryEsConditionEnum(String fieldName) {
        this.fieldName = fieldName;
    }
}

