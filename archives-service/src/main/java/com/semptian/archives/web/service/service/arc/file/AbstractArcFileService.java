package com.semptian.archives.web.service.service.arc.file;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.BusinessTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 档案文件查询服务抽象类
 *
 * <AUTHOR> Hu
 * @date 2024/3/21
 */
@Service
@Slf4j
public abstract class AbstractArcFileService implements ArcFileService {

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private ArcDrillDownFieldTranslateConfigService arcDrillDownFieldTranslateConfigService;

    @Override
    public Map<String, Object> getAttachInfo(ArcContext ctx, BusinessTypeEnum businessTypeEnum) {
        Map<String, Object> resultMap = new HashMap<>(8);

        //构建公共参数,查询日期、关键字模糊、文件类型
        CommonParamUtil.ParamMap params = buildParams(ctx, businessTypeEnum);

        String methodName;
        if (BusinessTypeEnum.OVERVIEW == businessTypeEnum) {
            methodName = BusinessCodeEnum.QUERY_ATTACHMENT_INFO_OVERVIEW.getValue();
        } else {
            methodName = BusinessCodeEnum.QUERY_ATTACHMENT_INFO.getValue();
        }

        Long total = arcCommonService.getCommonServiceCountResult(methodName, params, true);
        if (ObjectUtil.isNull(total) || total <= 0L) {
            total = 0L;
        }

        List<Map<String, Object>> attachmentInfo = Lists.newArrayList();
        if (total > 0L) {
            attachmentInfo = arcCommonService.getCommonServiceListResult(methodName, params, ctx.getPageWarpEntity());

            if (attachmentInfo != null && !attachmentInfo.isEmpty()) {
                attachmentInfo.forEach(item -> {
                    try {
                        String dataType = item.getOrDefault("dataType", "").toString();
                        String dataTypeName = "";

                        if (StrUtil.isNotEmpty(dataType)) {
                            dataTypeName = arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, dataType, ctx.getLang()).toString();
                        }

                        item.put("dataTypeName", dataTypeName);
                    } catch (Exception e) {
                        log.error("获取附件信息失败", e);
                    }

                });
            }
        }

        resultMap.put("total", total);
        resultMap.put("attachInfo", attachmentInfo);
        return resultMap;
    }

    /**
     * 构建参数
     *
     * @param ctx             上下文
     * @param businessTypeEnum 业务类型
     * @return 参数
     */
    public CommonParamUtil.ParamMap buildParams(ArcContext ctx, BusinessTypeEnum businessTypeEnum) {
        //查询日期参数
        CommonParamUtil.ParamMap params = CommonParamUtil.buildCommonTimeParam(ctx.getDateModel());

        //获取档案类型对应的账号参数
        buildAccountParams(params, ctx);
        String arcAccount = getAccount(ctx.getArcAccount());
        params.put("arc_account", arcAccount);

        //tab页查询需要以下参数
        if (BusinessTypeEnum.TAB.equals(businessTypeEnum)) {
            //文件类型过滤参数
            StringBuilder fileTypeParam = new StringBuilder();
            List<String> fileTypes = new ArrayList<>();
            for (String file : ctx.getFileType().split(",")) {
                //0表示全部档案
                if ("0".equals(file)) {
                    continue;
                }

                //unknown表示需要筛除过滤未知类型文件
                if ("unknown".equals(file)) {
                    //是否添加unknown
                    fileTypeParam.append(" and (attach_type <> 'OTHER')");
                } else if (StringUtils.isNotBlank(file)) {
                    fileTypes.add(file);
                }
            }

            if (!fileTypes.isEmpty()) {
                fileTypeParam.append(" and lower(attach_suffix) in (" + "'").append(String.join("','", fileTypes)).append("')");
            }

            params.put("fileType", fileTypeParam.toString());

            //关键字模糊查询
            params.put("keyword", "");
            String keyword = ctx.getKeyWord().toLowerCase();
            if (StringUtils.isNotBlank(keyword)) {
                keyword = EscapeUtil.escapeSingleQuote(keyword);
                keyword = EscapeUtil.escapeCKMatch(keyword);
                buildKeywordCondition(params, keyword);
            }
        }

        //默认排序字段
        if (StringUtils.isBlank(ctx.getPageWarpEntity().getSortField())) {
            switch (businessTypeEnum) {
                case OVERVIEW:
                    ctx.getPageWarpEntity().setSortField("latestRelationTime");
                    break;
                case TAB:
                    ctx.getPageWarpEntity().setSortField("firstAppearTime");
                    break;
                default:
                    break;
            }
        }

        return params;
    }

    protected void buildKeywordCondition(CommonParamUtil.ParamMap params, String keyword) {
        params.put("keyword", " AND (lower(attach_name) LIKE CONCAT('%','" + keyword + "','%') OR  " +
                "lower(virtual_account) LIKE CONCAT('%','" + keyword + "','%') )");
    }


    protected abstract void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx);

    private String getAccount(String radius) {
        StringJoiner stringJoiner = new StringJoiner(",");
        String[] split = radius.split(",");

        for (String string : split) {
            stringJoiner.add("'" + EscapeUtil.escapeSingleQuote(string) + "'");
        }
        return stringJoiner.toString();
    }
}
