package com.semptian.archives.web.service.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2021/01/30
 * @Des 结果页定制
 */
@Data
public class ArchiveResultTableSettingModel {

    @ApiModelProperty(value = "资源库名称")
    private String resourceTableName;

    @ApiModelProperty(value = "定制列参数")
    private List<ArchiveResultTableColumnModel> columns;

}
