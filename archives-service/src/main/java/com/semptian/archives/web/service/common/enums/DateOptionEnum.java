package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 时间快捷选项枚举
 * <AUTHOR>
 * @date 2024/3/22
 */

@Getter
public enum DateOptionEnum {
    // 近7天
    LAST_7_DAYS(1),

    // 近15天
    LAST_15_DAYS(2),

    // 本月
    CURRENT_MONTH(3),

    // 上月
    LAST_MONTH(4),

    // 近二月
    LAST_TWO_MONTHS(5),

    // 过去7天
    PAST_7_DAYS(6),

    // 过去15天
    PAST_15_DAYS(7),

    // 自定义选项
    CUSTOM(0);

    private final Integer value;

    DateOptionEnum(Integer value) {
        this.value = value;
    }

    /**
     * 根据值获取枚举
     * @param value 值
     * @return 枚举
     */
    public static DateOptionEnum getDateOptionByValue(Integer value) {
        for (DateOptionEnum dateOptionEnum : DateOptionEnum.values()) {
            if (dateOptionEnum.getValue().equals(value)) {
                return dateOptionEnum;
            }
        }
        return CUSTOM;
    }
}
