package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.meiya.whalex.util.JsonUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.dao.archive.entity.ArcConnectionTypeEntity;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.entity.UserRelationDataEntity;
import com.semptian.archives.web.dao.archive.mapper.UserRelationDataMapper;
import com.semptian.archives.web.service.common.enums.ConnectTypeEnum;
import com.semptian.archives.web.service.common.enums.LanguageEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcConnectionTypeService;
import com.semptian.archives.web.service.service.ArcRemarkService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.archives.web.service.service.UserRelationDataService;
import com.semptian.base.service.ReturnModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-01-12 19:48:39
 */
@Service("userRelationDataService")
@Slf4j
public class UserRelationDataServiceImpl extends ServiceImpl<UserRelationDataMapper, UserRelationDataEntity> implements UserRelationDataService {

    @Resource
    private ArcConnectionTypeService arcConnectionTypeService;

    @Resource
    private ArcRemarkService arcRemarkService;

    @Resource
    private ArchivesInfoService archivesInfoService;

    @Override
    public Map<String, Object> getRelationInfoVersion(String arcId, String userId, DateModel dateModel, String keyWord, PageWarpEntity pageWarpEntity, List<Integer> permissionList) {
        Long startTime = null;
        if (StrUtil.isNotEmpty(dateModel.getStartDay())) {
            startTime = DateUtils.getOneDayStart(dateModel.getStartDay());
        }
        Long endTime = null;
        if (StrUtil.isNotEmpty(dateModel.getEndDay())) {
            endTime = DateUtils.getOneDayEnd(dateModel.getEndDay());
        }

        QueryWrapper<UserRelationDataEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("is_del", 0);

        if (StrUtil.isNotEmpty(arcId)) {
            queryWrapper.eq("arc_id", arcId);
        }

        if (ObjectUtil.isNotNull(startTime)) {
            queryWrapper.ge("create_time", startTime);
        }

        if (ObjectUtil.isNotNull(endTime)) {
            queryWrapper.le("create_time", endTime);
        }

        if (StrUtil.isNotEmpty(keyWord)) {
            //关键字可模糊查询账号或者版本名称
            queryWrapper.and(wrapper -> wrapper.like("relation_name", keyWord).or().like("arc_account", keyWord));

        }

        //数据权限过滤
        queryWrapper.in("arc_type", permissionList);

        queryWrapper.orderByDesc("create_time", "version");

        Page<UserRelationDataEntity> page = new Page<>();
        page.setCurrent(pageWarpEntity.getOnPage());
        page.setSize(pageWarpEntity.getSize());
        this.page(page, queryWrapper);

        List<UserRelationDataModel> list = Lists.newArrayList();
        if (CollUtil.isNotEmpty(page.getRecords())) {
            Set<String> arcIds = page.getRecords().stream().map(UserRelationDataEntity::getArcId).collect(Collectors.toSet());
            List<ArcInfoModel> arcInfoModelList = archivesInfoService.getArcInfoByIds(new ArrayList<>(arcIds), 0);

            if (CollUtil.isNotEmpty(arcInfoModelList)) {
                Map<String, ArcInfoModel> arcInfoModelMap = new HashMap<>(16);
                arcInfoModelList.forEach(arcInfoModel -> arcInfoModelMap.put(arcInfoModel.getId(), arcInfoModel));

                list = page.getRecords().stream().map(userRelationData -> {
                    UserRelationDataModel userRelationDataModel = new UserRelationDataModel();

                    ArcInfoModel arcInfoModel = arcInfoModelMap.get(userRelationData.getArcId());

                    BeanUtil.copyProperties(userRelationData, userRelationDataModel);
                    if (StrUtil.isNotEmpty(userRelationData.getExpansionArcId())) {
                        userRelationDataModel.setExpansionArcIds(Arrays.asList(userRelationData.getExpansionArcId().split(",")));
                    }
                    userRelationDataModel.setArchivesType(arcInfoModel.getArchivesType());
                    userRelationDataModel.setArcCreateTime(arcInfoModel.getCreateTime());
                    userRelationDataModel.setArcAccountType(arcInfoModel.getArcAccountType());

                    return userRelationDataModel;
                }).collect(Collectors.toList());
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("total", page.getTotal());
        result.put("list", list);
        return result;
    }

    @Override
    public Integer getMaxVersion(String arcId, String userId) {
        return this.baseMapper.getMaxVersion(userId, arcId);
    }

    @Override
    public UserRelationDataEntity queryExistByRelationName(String userId, String arcId, String relationName) {
        QueryWrapper<UserRelationDataEntity> entityWrapper = new QueryWrapper<>();
        entityWrapper.eq("user_id",userId).eq("arc_id",arcId).eq("relation_name",relationName).eq("is_del",0);
        List<UserRelationDataEntity> userRelationDataEntities = this.baseMapper.selectList(entityWrapper);
        if (userRelationDataEntities!=null&&!CollectionUtils.isEmpty(userRelationDataEntities)){
            return userRelationDataEntities.get(0);
        }else {
            return null;
        }
    }

    @Override
    public ReturnModel<?> saveRelationData(UserRelationDataModel userRelationData) {
        String userId = userRelationData.getUserId();

        //获取当前档案下最大版本号
        int maxVersion = 0;
        Integer version = this.getMaxVersion(userRelationData.getArcId(), userId);
        if (version != null) {
            maxVersion = version;
        }

        //判断当前档案下该用户是否已保存相同版本名称的扩线信息
        String relationName = userRelationData.getRelationName();
        String arcId = userRelationData.getArcId();
        UserRelationDataEntity exist = this.queryExistByRelationName(userId, arcId, relationName);
        if (exist != null && StrUtil.isNotEmpty(exist.getArcId())) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("relation.version.exists"));
        }

        //判断当前用户在当前档案下已保存且未删除的关系扩线版本数量，如果大于等于10个则不允许再保存
        QueryWrapper<UserRelationDataEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("is_del", 0);
        queryWrapper.eq("arc_id", arcId);
        if (this.baseMapper.selectCount(queryWrapper) >= 10) {
            return ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("relation.version.exceed.limit"));
        }

        UserRelationDataEntity userRelationDataEntity = new UserRelationDataEntity();
        BeanUtil.copyProperties(userRelationData, userRelationDataEntity);
        if (CollUtil.isNotEmpty(userRelationData.getExpansionArcIds())) {
            userRelationDataEntity.setExpansionArcId(StringUtils.join(userRelationData.getExpansionArcIds(), ","));
        }

        //保存用户关系扩线数据
        userRelationDataEntity.setCreateTime(System.currentTimeMillis());
        userRelationDataEntity.setVersion(maxVersion + 1);
        this.baseMapper.insert(userRelationDataEntity);

        return ReturnModel.getInstance().ok();
    }

    @Override
    public Object connectCreate(String arc1Id, String arc2Id, String connectType,String expansionArcId, String lang) {
        try {
            QueryWrapper<ArcConnectionTypeEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(src -> src.eq("src_arc_id", arc1Id).or().eq("src_arc_id", arc2Id));
            queryWrapper.and(target -> target.eq("target_arc_id", arc2Id).or().eq("target_arc_id", arc1Id));

            ArcConnectionTypeEntity arcConnectionType = arcConnectionTypeService.getOne(queryWrapper);

            UserRelationUpdateParamModel userRelationUpdateParamModel = new UserRelationUpdateParamModel();
            userRelationUpdateParamModel.setExpansionArcId(expansionArcId);
            userRelationUpdateParamModel.setArc1Id(arc1Id);
            userRelationUpdateParamModel.setArc2Id(arc2Id);
            userRelationUpdateParamModel.setConnectType(connectType);
            userRelationUpdateParamModel.setLang(lang);

            if (arcConnectionType != null) {
                if ("-1".equals(connectType)) {
                    //清除关系
                    arcConnectionTypeService.removeById(arcConnectionType.getId());
                    arcConnectionTypeService.updateConnectionTypeCache(arc1Id, arc2Id, null);

                    //更新关系扩线版本信息
                    updateUserRelationData(userRelationUpdateParamModel);
                    return ReturnModel.getInstance().ok();
                }
                arcConnectionType.setConnectionType(connectType);
                arcConnectionType.setModifyTime(System.currentTimeMillis());
                arcConnectionTypeService.updateConnectionTypeCache(arc1Id, arc2Id, connectType);
                arcConnectionTypeService.updateById(arcConnectionType);
            } else {
                if (!"-1".equals(connectType)) {
                    ArcConnectionTypeEntity newArcConnectionType = new ArcConnectionTypeEntity();
                    newArcConnectionType.setSrcArcId(arc1Id);
                    newArcConnectionType.setTargetArcId(arc2Id);
                    newArcConnectionType.setConnectionType(connectType);
                    newArcConnectionType.setCreateTime(System.currentTimeMillis());
                    newArcConnectionType.setModifyTime(System.currentTimeMillis());
                    arcConnectionTypeService.updateConnectionTypeCache(arc1Id, arc2Id, connectType);
                    arcConnectionTypeService.save(newArcConnectionType);
                }
            }

            //更新关系扩线版本信息
            updateUserRelationData(userRelationUpdateParamModel);

            return ReturnModel.getInstance().ok();
        } catch (Exception e) {
            log.error("关系创建失败 ", e);
            return ReturnModel.getInstance().error(I18nUtils.getMessage("error"));
        }
    }

    @Override
    public ReturnModel<?> remarkSave(ArcRemarkEntity arcRemark, String userId) {
        try {
            arcRemark.setUserId(Integer.valueOf(userId));
            arcRemark.setCreateTime(System.currentTimeMillis());
            arcRemarkService.saveOrUpdate(arcRemark);

            UserRelationUpdateParamModel userRelationUpdateParamModel = new UserRelationUpdateParamModel();
            userRelationUpdateParamModel.setExpansionArcId(arcRemark.getExpansionArcId());
            userRelationUpdateParamModel.setArcId(arcRemark.getArcId());
            userRelationUpdateParamModel.setRemark(arcRemark.getRemark());

            //更新关系扩线版本信息
            updateUserRelationData(userRelationUpdateParamModel);
        } catch (Exception e) {
            log.error("remarkSave error", e);
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.REMARK_SAVE_ERROR.getCode()).setMsg(I18nUtils.getMessage("error"));
        }
        return ReturnModel.getInstance().ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(DeleteModel deleteModel) {
        List<String> ids = deleteModel.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            //批量逻辑删除
            this.lambdaUpdate().set(UserRelationDataEntity::getIsDel, 1).in(UserRelationDataEntity::getId, deleteModel.getIds()).update();
        }
    }

    /**
     * 关系扩线节点备注或节点类型备注后，同步更新用户关系扩线版本信息
     * @param userRelationUpdateParamModel 扩线版本更新参数
     */
    @SuppressWarnings({"unchecked"})
    private void updateUserRelationData(UserRelationUpdateParamModel userRelationUpdateParamModel) {
        String expansionArcId = userRelationUpdateParamModel.getExpansionArcId();

        String remark = userRelationUpdateParamModel.getRemark();
        String shortRemark = remark;
        String arcId = userRelationUpdateParamModel.getArcId();
        if (StrUtil.isNotEmpty(remark)) {
            //扩线展示时label长度默认展示前11个字符,多余字符用...表示
            if (remark.length() > 11) {
                shortRemark = remark.substring(0, 11) + "...";
            }
        }

        String arc1Id = userRelationUpdateParamModel.getArc1Id();
        String arc2Id = userRelationUpdateParamModel.getArc2Id();
        String lang = userRelationUpdateParamModel.getLang();
        ConnectTypeEnum connectTypeEnum = ConnectTypeEnum.getConnectTypeEnumByValue(userRelationUpdateParamModel.getConnectType());

        //更新当前档案所有扩线版本中档案节点备注信息和关系类型
        List<UserRelationDataEntity> userRelationDataEntityList = this.lambdaQuery().eq(UserRelationDataEntity::getArcId, expansionArcId)
                .eq(UserRelationDataEntity::getIsDel, 0).list();
        for (UserRelationDataEntity userRelationDataEntity : userRelationDataEntityList) {
            String relationData = userRelationDataEntity.getRelationData();

            Map<String, Object> relationDataMap = JsonUtil.jsonStrToMap(relationData);
            boolean needUpdate = false;

            if (StrUtil.isNotEmpty(arcId) && StrUtil.isNotEmpty(remark)) {
                //更新节点备注
                List<Map<String, Object>> nodes = (List<Map<String, Object>>) relationDataMap.get("nodes");
                for (Map<String, Object> node : nodes) {
                    if (arcId.equals(node.get("id"))) {
                        node.put("name", remark);
                        node.put("content", remark);
                        node.put("fullLabel", remark);
                        node.put("label", shortRemark);
                        node.put("title", shortRemark);

                        needUpdate = true;
                    }
                }
            }

            if (StrUtil.isNotEmpty(arc1Id) && StrUtil.isNotEmpty(arc2Id) && connectTypeEnum != null) {
                //更新关系类型
                List<Map<String, Object>> edges = (List<Map<String, Object>>) relationDataMap.get("edges");
                for (Map<String, Object> edge : edges) {
                    if ((arc1Id.equals(edge.get("startNodeId")) && arc2Id.equals(edge.get("endNodeId"))) ||
                            (arc2Id.equals(edge.get("startNodeId")) && arc1Id.equals(edge.get("endNodeId")))) {

                        Object label = edge.get("label");
                        String peopleRelationType = "";
                        if (null != label && StrUtil.isNotEmpty(label.toString())) {
                            String labelStr = label.toString();

                            String[] labelArr = labelStr.split("---");
                            //原始label值
                            String originLabel;
                            if (labelArr.length > 1) {
                                originLabel = labelArr[labelArr.length - 1];
                            } else {
                                originLabel = labelArr[0];
                            }

                            log.info("originLabel: {}", originLabel);
                            if (connectTypeEnum.equals(ConnectTypeEnum.NON)) {
                                //无,表示需要将关系删除,还原为原始label
                                edge.put("label", originLabel);
                            } else {
                                peopleRelationType = connectTypeEnum.getValue();
                                String connectTypeDesc = LanguageEnum.ZH_CN.getLang().equals(lang) ? connectTypeEnum.getDescZh() : connectTypeEnum.getDescFr();

                                //考虑到当前可能存在已存在通联关系,需要对label进行替换操作例如:夫妻---C:151  S:126  F:93
                                String newLabel = connectTypeDesc + "---" + originLabel;
                                log.info("newLabel: {}", newLabel);
                                edge.put("label", newLabel);
                            }

                            edge.put("peopleRelationType", peopleRelationType);
                            needUpdate = true;
                        }
                    }
                }
            }

            if (needUpdate) {
                UpdateWrapper<UserRelationDataEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", userRelationDataEntity.getId());
                updateWrapper.set(true, "relation_data", JsonUtil.objectToStr(relationDataMap));
                this.update(updateWrapper);
            }
        }
    }
}
