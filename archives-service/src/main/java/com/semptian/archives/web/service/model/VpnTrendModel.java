package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/29 12:08
 * Description: VPN查询相关模型
 */
@ApiModel("VPN模型")
@Data
public class VpnTrendModel {

    /**
     * 档案ID
     */
    @ApiModelProperty(value = "档案ID")
    private String arcId;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "档案类型")
    private Integer arcType;

    /**
     * 档案账号
     */
    @ApiModelProperty(value = "档案账号")
    private String arcAccount;

    /**
     * 档案账号类型
     */
    @ApiModelProperty(value = "档案账号类型")
    private String arcAccountType;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDay;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDay;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private String createDay;

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption;

    /**
     * 分析时段,多时段使用逗号进行拼接，例如1-3,2-5,7-9,12-14,18-20,默认为0-24
     */
    @ApiModelProperty(value = "分析时段")
    private String times = "0-24";

    /**
     * blockFlag 0:全部 1：Alert 2：Reject 3：Log
     */
    @ApiModelProperty(value = "block_flag")
    private Integer block_flag;


}
