package com.semptian.archives.web.service.model;

import lombok.Data;

/**
 * @Description 虚拟账号统计模型
 * <AUTHOR>
 * @Date 2020/11/10
 */

@Data
public class VirtualAccountCountModel implements Comparable{

    /**
     * 所属成员档案信息
     */
    private String arcName;

    /**
     * 账号类型
     */
    private String accountId;

    /**
     * 账号
     */
    private String account;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 最早关联时间
     */
    private long firstRelationTime;

    /**
     * 最后关联时间
     */
    private long lastRelationTime;

    /**
     * 关联次数
     */
    private Integer activeTimes;

    @Override
    public int compareTo(Object o) {
        VirtualAccountCountModel virtualAccountCountModel = (VirtualAccountCountModel) o;
        int times = virtualAccountCountModel.activeTimes - this.activeTimes;
        return times;
    }
}
