package com.semptian.archives.web.service.service.impl;

import com.google.common.collect.Lists;
import com.semptian.archives.web.service.service.EsQueryService;
import com.semptian.base.builders.QueryBuilder;
import com.semptian.base.model.TianhePageModel;
import com.semptian.base.service.elasticsearch.SearchOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * ES查询服务实现类
 * <AUTHOR> Hu
 * @date 2024/1/4
 */
@Service
@Slf4j
public class EsQueryServiceImpl implements EsQueryService {

    @Resource
    private SearchOperation searchOperation;

    @Override
    public List<Map<String, Object>> getRecordsByIds(String field, List<String> ids, String... indexs) {
        return searchOperation.getRecordsByIds(field, ids, indexs);
    }

    @Override
    @SuppressWarnings({"rawtypes", "unchecked"})
    public TianhePageModel query(Integer onPage, Integer size, QueryBuilder queryBuilder, Map<String, String> sortFields, long timeOut, Integer terminateAfter, String... indexs) {
        TianhePageModel tianhePageModel = new TianhePageModel();
        tianhePageModel.setSkip((onPage - 1) * size);
        tianhePageModel.setLimit(size);

        try {
            tianhePageModel = (TianhePageModel) searchOperation.query(tianhePageModel, queryBuilder, sortFields, timeOut, terminateAfter, indexs);
        }catch (Exception e) {
            tianhePageModel.setTotal(0L);
            tianhePageModel.setRecords(Lists.newArrayList());
            log.error("query es error, queryBuilder:{}", queryBuilder.toString(), e);
        }

        return (tianhePageModel) ;
    }
}
