package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.dao.archive.entity.DrillDownResultEntity;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.AttachSourceDataModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.DrillDownModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.ArcAttachService;
import com.semptian.archives.web.service.service.ArcCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: SunQi
 * @create: 2021/03/26
 * desc:
 **/
@Service
@Slf4j
public class ArcAttachServiceImpl implements ArcAttachService {

    @Resource
    private ArcCommonService arcCommonService;

    /**
     * 获取附件来源信息
     *
     * @param archiveType    档案类型
     * @param arcAccount     认证账号
     * @param createDay      固定IP档案建档日期
     * @param dateModel      日期模型
     * @param pageWarpEntity 分页信息
     * @return 附件来源信息
     */
    @Override
    public Object getAttachSourceInfo(Integer archiveType, String arcAccount, String arcAppType, Integer dataType, String attachMd5, String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity, String lang) {
        Map<String, Object> result = new HashMap<>(8);
        result.put("total", 0L);
        result.put("list", new ArrayList<>());

        //根据档案账号和档案类型、附件MD5值查询附件对应的报文地址

        //构建日期参数
        CommonParamUtil.ParamMap params = CommonParamUtil.buildCommonTimeParam(dateModel);
        params.put("arc_account", arcAccount);

        ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(archiveType.toString());

        //构建查询参数
        buildParam(arcTypeEnum, params, arcAppType);
        params.put("attach_md5", attachMd5);
        params.put("norm_data_type", "(" + dataType + ")");

        String serviceCode = null;

        if (ArcTypeEnum.WEB_SITE.getKey().equalsIgnoreCase(archiveType.toString())) {
            serviceCode = BusinessCodeEnum.QUERY_DOMAIN_ATTACHMENT_INFO_FILE_PATH.getValue();
        } else {
            serviceCode = BusinessCodeEnum.QUERY_ATTACHMENT_INFO_FILE_PATH.getValue();
        }

        //查询附件对应的报文地址
        List<Map<String, Object>> listResult = arcCommonService.getCommonServiceListResult(serviceCode, params);

        if (CollUtil.isEmpty(listResult)) {
            return result;
        }

        //根据报文地址和协议编码查询附件明细信息
        String startDay = String.valueOf(params.get("start_day"));
        String endDay = String.valueOf(params.get("end_day"));

        String arcAccountType = null;
        switch (Objects.requireNonNull(arcTypeEnum)) {
            case RADIUS:
                arcAccountType = AuthTypeEnum.RADIUS.getType().toString();
                break;
            case FIXED_IP:
                arcAccountType = AuthTypeEnum.FIXED_IP.getType().toString();
                break;
            default:
                arcAccountType = arcAppType;
                break;
        }

        //获取明细数据
        List<String> filePathList = listResult.stream().map(map -> String.valueOf(map.get("filePath"))).collect(Collectors.toList());

        DrillDownModel drillDownModel = new DrillDownModel();
        drillDownModel.setDataType(dataType);
        drillDownModel.setOnPage(pageWarpEntity.getOnPage());
        drillDownModel.setSize(pageWarpEntity.getSize());
        drillDownModel.setStartDay(startDay);
        drillDownModel.setEndDay(endDay);
        drillDownModel.setCreateDay(createDay);
        drillDownModel.setArcAccount(arcAccount);
        drillDownModel.setArcAccountType(arcAccountType);
        drillDownModel.setLang(lang);
        drillDownModel.setArcType(archiveType + "");
        drillDownModel.setFilePathList(filePathList);
        drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.ATTACH_SOURCE_DETAIL);
        drillDownModel.setSortField(DrillDownNormDataTypeEnum.HTTP.getKey() == dataType ? "latest_relation_time" : "capture_time");
        drillDownModel.setSortType(ObjectUtil.isNull(pageWarpEntity.getSortType()) ? 1 : pageWarpEntity.getSortType());
        return drillFilePathSource(arcAccount, dataType, lang, drillDownModel, listResult, result);
    }

    /**
     *
     * @param arcAccount 认证账号
     * @param dataType 协议类型
     * @param lang 预研
     * @param drillDownModel 下载参数模型
     * @param listResult filePathList
     * @param result 返回结果
     * @return
     */
    public Object drillFilePathSource(String arcAccount, Integer dataType, String lang, DrillDownModel drillDownModel,
                                       List<Map<String, Object>> listResult, Map<String, Object> result) {
        DrillDownResultEntity drillDownResultEntity = arcCommonService.getDrillDownDataDetail(drillDownModel);
        if (CollUtil.isEmpty(drillDownResultEntity.getList())) {
            return result;
        }

        List<AttachSourceDataModel> list = Lists.newArrayList();

        //由于无法直接查询出虚拟账号，所以需要根据file_path查询虚拟账号
        Map<String, String> virtualAccountMap = new HashMap<>();
        for (Map<String, Object> objectMap : listResult) {
            if (ObjectUtil.isNotNull(objectMap.get("filePath"))) {
                String key = objectMap.get("filePath").toString();
                String value = objectMap.getOrDefault("account", "").toString();

                virtualAccountMap.put(key, value);
            }
        }

        //获取明细表头headers需要过滤auth_account、file_path、capture_time字段
        List<Map<String, Object>> headers = arcCommonService.getHeaders(dataType, 0, DrillDownSceneEnum.ATTACH_SOURCE_DETAIL, lang, true);
        //认证账号、虚拟账号、捕获时间(http协议为最后关联时间)为固定字段,需要在明细detail中移除。其他字段为动态字段需要按照field、desc、value的格式返回
        headers.removeIf(header -> "virtual_account".equals(header.get("field")) ||"auth_account".equals(header.get("field")) || "file_path".equals(header.get("field")) || "capture_time".equals(header.get("field")) || "latest_relation_time".equals(header.get("field")));

        if (CollUtil.isNotEmpty(drillDownResultEntity.getList())) {
            list = drillDownResultEntity.getList().stream().map(item -> {
                AttachSourceDataModel attachSourceDataModel = new AttachSourceDataModel();

                attachSourceDataModel.setVirtualAccount("");
                attachSourceDataModel.setAuthAccount("");
                if (ArcTypeEnum.EMAIL.getKey().equalsIgnoreCase(drillDownModel.getArcType()) || ArcTypeEnum.IM.getKey().equalsIgnoreCase(drillDownModel.getArcType())) {
                    attachSourceDataModel.setVirtualAccount(arcAccount);
                } else if (ArcTypeEnum.WEB_SITE.getKey().equalsIgnoreCase(drillDownModel.getArcType())) {
                    Object authAccountObj = item.get("auth_account");
                    if (authAccountObj != null) {
                        attachSourceDataModel.setAuthAccount(authAccountObj.toString());
                    }
                } else {
                    attachSourceDataModel.setAuthAccount(arcAccount);
                }

                Object filePath = item.get("file_path");
                if (ObjectUtil.isNotNull(filePath) && virtualAccountMap.containsKey(item.get("file_path").toString())) {
                    if (ArcTypeEnum.EMAIL.getKey().equalsIgnoreCase(drillDownModel.getArcType())
                            || ArcTypeEnum.IM.getKey().equalsIgnoreCase(drillDownModel.getArcType())) {
                        attachSourceDataModel.setAuthAccount(virtualAccountMap.get(item.get("file_path").toString()));
                    } else if (ArcTypeEnum.WEB_SITE.getKey().equalsIgnoreCase(drillDownModel.getArcType())) {
                        attachSourceDataModel.setVirtualAccount(virtualAccountMap.get(item.get("file_path").toString()));
                    } else {
                        attachSourceDataModel.setVirtualAccount(virtualAccountMap.get(item.get("file_path").toString()));
                    }
                }

                Object captureTime = item.get("capture_time");
                if (DrillDownNormDataTypeEnum.HTTP.getKey() == dataType) {
                    captureTime = item.get("latest_relation_time");
                }
                attachSourceDataModel.setCaptureTime("");
                if (ObjectUtil.isNotNull(captureTime)) {
                    attachSourceDataModel.setCaptureTime(captureTime.toString());
                }

                List<AttachSourceDataModel.DetailEntity> detailEntityList = headers.stream().map(header -> {
                    AttachSourceDataModel.DetailEntity detailEntity = new AttachSourceDataModel.DetailEntity();
                    String field = header.get("field").toString();
                    detailEntity.setField(field);
                    detailEntity.setDesc(header.get("title").toString());
                    detailEntity.setValue(item.get(field));
                    return detailEntity;
                }).collect(Collectors.toList());

                attachSourceDataModel.setDetail(detailEntityList);
                return attachSourceDataModel;
            }).collect(Collectors.toList());
        }

        result.put("total", drillDownResultEntity.getTotal());
        result.put("list", list);
        return result;
    }

    /**
     * 构建参数
     *
     * @param arcTypeEnum    档案类型
     * @param params         参数
     * @param arcAccountType
     */
    private void buildParam(ArcTypeEnum arcTypeEnum , CommonParamUtil.ParamMap params, String arcAccountType) {
        switch (Objects.requireNonNull(arcTypeEnum)) {
            case PHONE:
            case FIXED_IP:
            case RADIUS:
                params.put("account_field", "virtual_account");
                params.put("account_type_condition", " auth_type = " + AuthTypeEnum.getAuthTypeByName(arcTypeEnum.getValue()) + " and auth_account ");
                break;
            case EMAIL:
                params.put("account_field", "auth_account");
                params.put("account_type_condition", " virtual_account ");
                break;
            case IM:
                params.put("account_field", "auth_account");
                if (StringUtils.isEmpty(arcAccountType)) {
                    params.put("account_type_condition", " virtual_account ");
                } else {
                    params.put("account_type_condition", " virtual_app_type = " + arcAccountType + " and virtual_account ");
                }
            case WEB_SITE:
                params.put("account_field", "virtual_account");
                params.put("account_type_condition", " domain ");
                break;
            default:
                params.put("account_field", "virtual_account");
                params.put("account_type_condition", " auth_account ");
                break;
        }
    }

}
