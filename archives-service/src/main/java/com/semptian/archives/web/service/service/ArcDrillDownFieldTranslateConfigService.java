package com.semptian.archives.web.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.archives.web.dao.archive.entity.ArcDrillDownFieldTranslateConfigEntity;

import java.util.List;
import java.util.Map;

/**
 * 明细下钻字段翻译接口
 * <AUTHOR>
 * @date 2024/4/2
 */
public interface ArcDrillDownFieldTranslateConfigService extends IService<ArcDrillDownFieldTranslateConfigEntity> {

    /**
     * 根据下钻字段名称、下钻字段值、以及语种获取对应字典翻译值
     * @param result 下钻结果数据包含数据的字段名称、字段值
     * @param lang 语种
     * @param protocolId 协议ID
     * @return 字典翻译后值
     */
    List<Map<String, Object>> fieldTranslate(List<Map<String, Object>> result, String lang, Integer protocolId);

    /**
     * 字段翻译
     * @param fieldName 字段名称
     * @param code 字典编码
     * @param lang 语种
     * @return 字典翻译值
     */
    Object fieldTranslate(String fieldName, Object code, String lang);

    /**
     * 字段翻译
     * @param fieldName 字段名称
     * @param code 字典编码
     * @return 字典翻译值
     */
    Object fieldTranslate(String fieldName, Object code);

    /**
     * 标签名翻译
     * @param tagNameLatitude 标签名code
     * @param lang 语种
     * @return 字典翻译值
     */
    Object tagNameTranslate(String tagNameLatitude, String lang);

    /**
     * 获取所有标签名翻译
     * @param lang 语种
     * @return 所有标签名翻译
     */
    Object getAllTagNameTranslate(String lang);
}
