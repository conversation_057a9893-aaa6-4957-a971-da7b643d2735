package com.semptian.archives.web.service.common.enums;

/**
 * <AUTHOR>
 * @date 2024/4/1 15:22
 * Description: 阻断类型枚举
 */
public enum BlockFlagEnum {

    /**
     * blockFlag 0:全部 1：Alert 2：Reject 3：Log
     */
    ALL(0, "全部"),
    <PERSON><PERSON>(1, "Alert"),

    <PERSON>ject(2, "Reject"),
    Log(3, "Log");

    private final Integer code;
    private final String value;

    BlockFlagEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getByCode(Integer code) {
        BlockFlagEnum[] values = BlockFlagEnum.values();
        for (BlockFlagEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getValue();
            }
        }
        return "";
    }

    public static BlockFlagEnum getByCodeEnum(Integer code) {
        BlockFlagEnum[] values = BlockFlagEnum.values();
        for (BlockFlagEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static Integer getCodeByValue(String value) {
        BlockFlagEnum[] values = BlockFlagEnum.values();
        for (BlockFlagEnum valueTemp : values) {
            if (valueTemp.getValue().equals(value)) {
                return valueTemp.getCode();
            }
        }
        return 0;
    }
}
