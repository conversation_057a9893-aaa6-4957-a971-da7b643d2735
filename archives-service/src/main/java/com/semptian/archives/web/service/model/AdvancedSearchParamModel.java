package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * Date:2020/11/16
 * Description
 **/
@Data
@ApiModel(description = "高级检索参数", value = "AdvancedSearchParamModel")
public class AdvancedSearchParamModel implements Serializable {

    /**
     * 资源库名称
     */
    @ApiModelProperty(value = "资源库名称 多个用,分隔 ,例如 fuck1000,fuck2000", required = true)
    private Set<String> resourceNames;

    @ApiModelProperty(value = "档案类型")
    private String archiveType;
    /**
     * 搜索字段直接的逻辑关系  必须
     */
    @ApiModelProperty(value = "搜索字段直接的逻辑关系 空格分隔，例如 protocolType\n" + "or\n" + "shujufenlei", required = true)
    private List<String> fieldList;

    /**
     * 搜索字段的值  必须
     */
    @ApiModelProperty(value = "搜索字段的值 ，json格式，例如{\"protocolType\": \"SMTP\",\"shujufenlei\": \"4G\"}", required = true)
    private String fieldVal;

    /**
     * 字段是否为精确
     */
    @ApiModelProperty("字段是否为精确")
    private String isFieldValAccurate;

    /**
     * 字段排序方式
     */
    @ApiModelProperty("字段排序方式")
    private String multiSortField;

    /**
     * 标签和维度信息
     */
    @ApiModelProperty("标签和维度信息")
    private String tags;

    /**
     * 是否包含明细数据
     */
    @ApiModelProperty("是否包含明细数据")
    private Boolean containsDetail = true;

    /**
     * 查询开始时间
     */
    @ApiModelProperty("查询开始时间")
    private Long startTime;

    /**
     * 查询结束时间
     */
    @ApiModelProperty("查询结束时间")
    private Long endTime;


    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer onPage = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "聚合信息")
    private String aggs;

    @ApiModelProperty(value = "过滤特殊号码")
    private List<String> specialNums;

    @ApiModelProperty("查询排序字段，默认排序(0)和时间排序(1)和文件大小排序(2) 热度排序 (3)")
    private Integer sortField;

    @ApiModelProperty("查询关键词")
    private String keyword;

    @ApiModelProperty(value = " 搜索字段直接的逻辑关系,高级表达方式 逐渐替代fieldList ，(!protocolType or shujufenlei and a)")
    private String query;

    @ApiModelProperty("查询字段范围")
    private Set<String> mainFieldsRange;

    @ApiModelProperty(value = "阅读状态 已读1 未读 0  不传或其他值为全部")
    private Integer readStatus;

    @ApiModelProperty(value = "垃圾邮件查询缓存，默认为0，正常缓存")
    private Integer userSpamMark = -1;

    @ApiModelProperty(value = "垃圾邮件查询缓存，默认为0，正常缓存")
    private String collapse;

    @ApiModelProperty("是否游标查询： 默认为false")
    private Boolean scanQuery = false;

    @ApiModelProperty("排除Habse字段")
    private String excludeQualifiers;

    @ApiModelProperty("是否为最后一次查询")
    private Boolean lastScroll = false;

    @ApiModelProperty("结果类型")
    private Integer resultType;

    private Integer terminateAfter;

    private String[] aggregationFields;

    private Integer aggQueryType;
    private String geoVal;
}