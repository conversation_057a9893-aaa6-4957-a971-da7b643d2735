package com.semptian.archives.web.service.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * <AUTHOR> Liu
 * Date: 2020/09/08 15:00
 * Description:
 */
@Slf4j
@Service
public class DateUtils {
    private static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

    private static final ThreadLocal<SimpleDateFormat> THREAD_LOCAL_SDF = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));


    private static final String dayFormat = "yyyyMMdd";

    private static final String monthFormat = "yyyyMM";

    public static final String dateFormat = "yyyy-MM-dd";

    public static final String TIMESTAMP_TO_DATETIME_FORMAT = "MM-dd-yyyy HH:mm:ss";

    public static final String DATE_TO_DATE_FORMAT = "dd-MM-yyyy";

    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static Integer archiveCacheInterval;

    // 将yyyy-MM-dd格式的日期字符串转换为yyyyMM格式的整型数字
    public static Integer getDateInt(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            Date date = THREAD_LOCAL_SDF.get().parse(dateStr);
            return Integer.parseInt(THREAD_LOCAL_SDF.get().format(date));
        } catch (ParseException e) {
            logger.error("日期格式错误", e);
            return null;
        }
    }

    /**
     * 判断传入的字符串是否表示一个月的第一天。
     *
     * @param startDay 待判断的日期字符串，格式为 yyyy-MM-dd。
     * @return 如果传入的日期是当月的第一天，则返回 true；否则返回 false。
     */
    public static boolean isFirstDayOfMonth(String startDay) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        try {
            LocalDate date = LocalDate.parse(startDay, formatter);
            LocalDate firstDayOfMonth = date.withDayOfMonth(1);
            return date.equals(firstDayOfMonth);
        } catch (DateTimeParseException e) {
            log.error("Invalid date format. Expected: yyyy-MM-dd", e);
        }
        return false;
    }

    /**
     * 判断传入的字符串是否表示今天日期。
     *
     * @param startDay 代表日期的字符串，格式为 yyyy-MM-dd。
     * @return 如果传入的日期是今天，则返回 true；否则返回 false。
     */
    public static boolean isToday(String startDay) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        try {
            LocalDate date = LocalDate.parse(startDay, formatter);
            LocalDate today = LocalDate.now();
            return date.equals(today);
        } catch (DateTimeParseException e) {
            log.error("Invalid date format. Expected: yyyy-MM-dd", e);
        }
        return false;
    }

    @Value("${archive.cache.interval:5}")
    private void setArchiveCacheInterval(int archiveCacheInterval) {
        DateUtils.archiveCacheInterval = archiveCacheInterval;
    }

    public static int getBetweenDays(String startDay, String endDay) {
        try {
            Date startDate = THREAD_LOCAL_SDF.get().parse(startDay);
            Date endDate = THREAD_LOCAL_SDF.get().parse(endDay);
            return (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24));
        } catch (ParseException e) {
            logger.error("invalid day, startDayStr:{}, endDayStr:{}", startDay, endDay);
            return 0;
        }
    }

    public static String getAddDay(String startDay, int i) {
        try {
            Date startDate = THREAD_LOCAL_SDF.get().parse(startDay);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.add(Calendar.DATE, i);
            return THREAD_LOCAL_SDF.get().format(calendar.getTime());
        } catch (ParseException e) {
            logger.error("invalid day, startDayStr:{}", startDay);
            return null;
        }
    }

    public static Object getTimestampByDay(String day) {
        try {
            return THREAD_LOCAL_SDF.get().parse(day).getTime();
        } catch (ParseException e) {
            logger.error("invalid day, day:{}", day);
            return null;
        }
    }

    public static String getTimestampByHour(String startDay, String startHour) {
        try {
            Date startDate = THREAD_LOCAL_SDF.get().parse(startDay);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(startHour));
            return String.valueOf(calendar.getTime().getTime());
        } catch (ParseException e) {
            logger.error("invalid day, startDayStr:{}, startHour:{}", startDay, startHour);
            return null;
        }
    }

    public static String getTimestampByHourAndMinute(String startDay, String startHour, String startMinute) {
        try {
            Date startDate = THREAD_LOCAL_SDF.get().parse(startDay);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(startHour));
            calendar.set(Calendar.MINUTE, Integer.parseInt(startMinute));
            return String.valueOf(calendar.getTime().getTime());
        } catch (ParseException e) {
            logger.error("invalid day, startDayStr:{}, startHour:{}", startDay, startHour);
            return null;
        }
    }


    public static Long getOneDayEnd(int index) {
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.set(Calendar.HOUR_OF_DAY, 23);
        calendarEnd.set(Calendar.MINUTE, 59);
        calendarEnd.set(Calendar.SECOND, 59);
        calendarEnd.set(Calendar.MILLISECOND, 000);
        calendarEnd.add(Calendar.DATE, index);
        Date time = calendarEnd.getTime();
        return time.getTime();
    }

    public static Long getOneDayStart(String dateStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        try {
            Date date = simpleDateFormat.parse(dateStr);
            Calendar calendarEnd = Calendar.getInstance();
            calendarEnd.setTimeInMillis(date.getTime());
            calendarEnd.set(Calendar.HOUR_OF_DAY, 0);
            calendarEnd.set(Calendar.MINUTE, 0);
            calendarEnd.set(Calendar.SECOND, 0);
            calendarEnd.set(Calendar.MILLISECOND, 0);
            Date time = calendarEnd.getTime();
            return time.getTime();
        } catch (ParseException e) {
            logger.error("invalid day, dateStr:{}", dateStr);
            return 0L;
        }
    }

    public static Long getOneDayEnd(String dateStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        try {
            Date date = simpleDateFormat.parse(dateStr);
            Calendar calendarEnd = Calendar.getInstance();
            calendarEnd.setTimeInMillis(date.getTime());
            calendarEnd.set(Calendar.HOUR_OF_DAY, 23);
            calendarEnd.set(Calendar.MINUTE, 59);
            calendarEnd.set(Calendar.SECOND, 59);
            calendarEnd.set(Calendar.MILLISECOND, 999);
            Date time = calendarEnd.getTime();
            return time.getTime();
        } catch (ParseException e) {
            logger.error("invalid day, dateStr:{}", dateStr);
            return 0L;
        }
    }

    /**
     * 格式化时间
     *
     * @param date   时间
     * @param format 时间格式
     * @return
     */
    public static String format(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 格式化时间
     *
     * @param timestamp
     * @param format
     * @return
     */
    public static String format(long timestamp, String format) {
        return format(new Date(timestamp), format);
    }

    /**
     * 返回当前小时的开始时间戳
     *
     * @param date
     * @return
     */
    public static Long getCurrentHourStart(Date date) {
        Calendar calendar = DateUtil.calendar(date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return DateUtil.date(calendar).getTime();
    }

    /**
     * 判断两个yyyy-MM-dd日期的先后顺序。
     * 如果before在after之前，或者为同一天则返回true。否则返回false
     */
    public static boolean dateBefore(String before, String after) {
        try {
            Date date1 = THREAD_LOCAL_SDF.get().parse(before);
            Date date2 = THREAD_LOCAL_SDF.get().parse(after);

            return !date2.before(date1);
        } catch (Exception e) {
            log.error("Date parse error: " + e.getMessage());
        }

        return true;
    }

    /**
     * 返回当前天的开始时间戳
     *
     * @param date
     * @return
     */
    public static Long getCurrentDayStart(Date date) {
        Calendar calendar = DateUtil.calendar(date);
        calendar.set(Calendar.HOUR_OF_DAY, archiveCacheInterval);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return DateUtil.date(calendar).getTime();
    }

    /**
     * 返回下一小时的开始时间
     *
     * @param date
     * @return
     */
    public static Long getNextHourStart(Date date) {
        Calendar calendar = DateUtil.calendar(DateUtil.offsetHour(date, 1));
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return DateUtil.date(calendar).getTime();
    }

    /**
     * 返回当天的结束时间,缓存时间为当天23：59：59：999
     *
     * @param date
     * @return
     */
    public static Long getOneDayEnd(Date date) {
        Calendar calendar = DateUtil.calendar(DateUtil.offsetHour(date, 0));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return DateUtil.date(calendar).getTime();
    }

    /**
     * 获取最近7天的日期
     *
     * @return
     */
    public static Map<String, String> getLatest7Day() {
        Map<String, String> result = new HashMap<>(2);

        Date today = new Date();
        result.put("start_day", DateUtil.offsetDay(today, -6).toString("yyyy-MM-dd"));
        result.put("end_day", DateUtil.format(today, "yyyy-MM-dd"));

        return result;
    }

    /**
     * 获取过去7天的日期
     *
     * @return
     */
    public static Map<String, String> getPast7Day() {
        Map<String, String> result = new HashMap<>(2);

        Date today = new Date();
        result.put("start_day", DateUtil.offsetDay(today, -7).toString("yyyy-MM-dd"));
        result.put("end_day", DateUtil.offsetDay(today, -1).toString("yyyy-MM-dd"));

        return result;
    }


    public static Long getRedisExpireTime() {
        return (DateUtils.getNextHourStart(new Date()) - (new Date()).getTime()) / 1000;
    }


    /**
     * 判断count值是否是错误，错误返回缓存时长0L
     * 将date转换成字符串形式
     * 正常判断date是否等于当天时间，当天缓存时长设置1小时
     * 不等于当天缓存时长设置成到达1天的最后1s
     *
     * @param date   时间 1645762588000
     * @param result 查询ck的count值
     * @return
     */
    public static Long getRedisTime(String date, Long result) {
        String now = DateUtil.today();
        date = date.trim();
        if (ObjectUtil.isNull(result) || result < 0) {
            return 0L;
        }
        //如果结束时间小于当天，缓存一天，如果结束时间为当天，缓存一小时
        if (date.equalsIgnoreCase(now)) {
            return (DateUtils.getNextHourStart(new Date()) - (new Date()).getTime()) / 1000;
        } else {
            return (DateUtils.getOneDayEnd(new Date()) - (new Date()).getTime()) / 1000;
        }
    }

    /**
     * 比较固定IP档案的业务查询开始时间是否小于等于档案建档时间，如果小于等于则将开始时间修改为档案建档时间
     *
     * <AUTHOR> Hu
     * @since 2025/1/17
     */
    public static void validateFixedIpStartDay(CommonParamUtil.ParamMap params, String createDay) {
        if (StringUtils.isNotBlank(createDay)) {
            if (params.containsKey("start_day") && StrUtil.isNotEmpty(createDay)) {
                String startDay = String.valueOf(params.get("start_day"));
                if (DateUtils.dateBefore(startDay, createDay)) {
                    params.put("start_day", createDay);
                }
            }
        }
    }

    public static void validateFixedIpStartDay(Map<String, Object> params, String createDay) {
        if (StringUtils.isNotBlank(createDay)) {
            if (params.containsKey("start_day") && StrUtil.isNotEmpty(createDay)) {
                String startDay = String.valueOf(params.get("start_day"));
                if (DateUtils.dateBefore(startDay, createDay)) {
                    params.put("start_day", createDay);
                }
            }
        }
    }

    /**
     * 用于固定Ip开始时间和建档时间校对paramMap
     *
     * @param params
     * @param createDate
     */
    public static void validateParamMap(Map<String, Object> params, String createDate) {
        if (StringUtils.isNotBlank(createDate)) {
            if (params.containsKey("start_day")) {
                String startDay = String.valueOf(params.get("start_day"));
                if (DateUtils.compareDay(createDate, startDay)) {
                    params.put("start_day", createDate);
                }
            }
            if (params.containsKey("startDay")) {
                String startDay1 = String.valueOf(params.get("startDay"));
                if (DateUtils.compareDay(createDate, startDay1)) {
                    params.put("startDay", createDate);
                }
            }
        }
    }

    /**
     * 获取两个日期间所有天
     *
     * @param startDay 开始日期
     * @param endDay   结束日期
     * @return 两个日期间所有天
     */
    public static List<Integer> getDayList(String startDay, String endDay) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        SimpleDateFormat simpleDayFormat = new SimpleDateFormat(dayFormat);
        List<Integer> dayList = Lists.newArrayList();
        try {
            Date startD = simpleDateFormat.parse(startDay);
            Date endD = simpleDateFormat.parse(endDay);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(startD);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endD);
            while (tempStart.before(tempEnd)) {
                dayList.add(Integer.parseInt(simpleDayFormat.format(tempStart.getTime())));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
            if (tempStart.equals(tempEnd)) {
                dayList.add(Integer.parseInt(simpleDayFormat.format(tempStart.getTime())));
            }
        } catch (ParseException e) {
            logger.error("invalid day, startDayStr:{}, endDayStr:{}", startDay, endDay);
            return Collections.emptyList();
        }
        return dayList;
    }

    public static boolean compareDay(String oneDay, String twoDay) {
        if (StringUtils.isBlank(oneDay) || StringUtils.isBlank(twoDay)) {
            return false;
        }
        if (twoDay.equals(oneDay)) {
            return false;
        } else {
            //如果startDay 比 createDay 小，表示需要替换
            return ((DateUtil.parse(oneDay).getTime() - DateUtil.parse(twoDay).getTime()) / 86400000) > 0;
        }
    }

    public static String transferSeconds(int totalSeconds){

        int days = totalSeconds / (24 * 60 * 60);
        int hours = (totalSeconds % (24 * 60 * 60)) / 3600;
        int minutes = (totalSeconds % (24 * 60 * 60)) % 3600 / 60;
        int seconds = totalSeconds % 60;
        StringBuilder sb = new StringBuilder();
        if(days > 0){
            sb.append(days).append("d");
        }
        if(hours > 0){
            sb.append(hours).append("H");
        }
        if(minutes > 0){
            sb.append(minutes).append("m");
        }
        if(seconds > 0){
            sb.append(seconds).append("s");
        } else if(seconds == 0) {
            sb.append(seconds).append("s");
        }

        return sb.toString();
    }

    public static String toddmmyyyy(String yyyymmdd) {
       if (yyyymmdd.isEmpty()) {
           return "";
       }
       try {
           return DateUtil.format(DateUtil.parse(yyyymmdd), "dd-MM-yyyy");
       } catch (Exception e) {
           return yyyymmdd;
       }
    }

    public static void main(String[] args) {
//        System.out.println(transferSeconds(1000));

        String s = DateUtils.transferSeconds((int) Float.parseFloat("0.651"));
        System.out.println(s);

    }
}
