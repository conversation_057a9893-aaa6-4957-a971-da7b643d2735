package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.entity.ArchivePhoneOwnerInfoEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcRemarkMapper;
import com.semptian.archives.web.dao.archive.mapper.ArchivePhoneOwnerInfoMapper;
import com.semptian.archives.web.service.service.ArcPhoneOwnerInfoService;
import com.semptian.archives.web.service.service.ArcRemarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-02-07 12:13:23
 */
@Slf4j
@Service("ArcPhoneOwnerInfoService")
public class ArcPhoneOwnerInfoServiceImpl extends ServiceImpl<ArchivePhoneOwnerInfoMapper, ArchivePhoneOwnerInfoEntity> implements
        ArcPhoneOwnerInfoService {

}
