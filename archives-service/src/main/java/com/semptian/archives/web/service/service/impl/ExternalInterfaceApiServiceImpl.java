package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.semptian.archives.web.service.model.TargetModel;
import com.semptian.archives.web.service.service.ExternalInterfaceApiService;
import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.base.util.Md5Util;
import com.semptian.redis.template.RedisOps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.annotation.AccessType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExternalInterfaceApiServiceImpl implements ExternalInterfaceApiService {

    @Resource
    private TianHeDorisTemplate tianHeDorisTemplate;

    @Autowired
    private RedisOps redisOps;
    @Override
    public List<Map<String, Object>> phoneAndAuthAccountQuery(TargetModel targetModel) {
        List<Map<String, Object>> resultData = new ArrayList<>();
        if(targetModel != null){
            String cacheKey = "phoneAndAuthAccount-"+Md5Util.getMD5(JSON.toJSONString(targetModel));
            if(redisOps.hasKey(cacheKey)){
                if(redisOps.get(cacheKey) != null){
                    return (List<Map<String, Object>>) redisOps.get(cacheKey);
                }
                return resultData;
            }

            String targetType =  targetModel.getTargetType();
            List<String> targetAccounts =  targetModel.getTargetAccounts();
            String sql = "";

            if(CollectionUtil.isNotEmpty(targetAccounts)) {
                //1、生成账号查询子句
                String accountSqlStr = generateInClause(targetAccounts);

                if (targetType.equals("phone")) {
                    //2、手机号查询，生成查询语句
                    sql = "select phone_number as targetAccount, behavior_num, internet_behavior_num, call_num, message_num, fax_num, block_behavior_num, latest_relation_country, latest_relation_city, imei, imsi, latest_relation_time from ads_archive_phone_statistics where phone_number in "+accountSqlStr;
                } else if (targetType.equals("radius")) {
                    //3、固网radius查询
                    sql = "select auth_account as targetAccount, auth_type, behavior_num, latest_relation_time, latest_relation_ip, latest_relation_country, latest_relation_city   from ads_archive_auth_account_statistics where auth_account in "+accountSqlStr;
                }
                resultData = tianHeDorisTemplate.queryForList(sql);
                redisOps.set(cacheKey,resultData, 60*5);
            }
        }
        return  resultData;
    }

    /**
     * 将账号列表转换为SQL IN子句格式的字符串。
     * @param accounts 账号列表
     * @return SQL IN子句格式的字符串
     */
    public static String generateInClause(List<String> accounts) {
        return "(" + accounts.stream()
                .map(account -> "'" + account + "'")
                .collect(Collectors.joining(", ")) + ")";
    }
}
