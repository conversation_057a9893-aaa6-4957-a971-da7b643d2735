package com.semptian.archives.web.service.service.arc.homepage;

import lombok.Getter;

/**
 * 该枚举值用于支撑各档案的“档案概览”页面的统计信息
 * <AUTHOR>
 */
@Getter
public enum ArcStatisticDimension {

    /**
     * 各统计维度
     */
    BEHAVIOUR_NUM(1, "行为次数"),
    APP_NUM(2, "应用数"),
    COMMUNICATION_AREA_NUM(3, "通联区域数/访问区域数"),
    VIRTUAL_ACCOUNT_NUM(4, "虚拟账号数"),
    BLOCK_BEHAVIOUR_NUM(5, "阻断行为数"),
    FILE_NUM(6, "文件个数"),
    BLOCK_APP_NUM(7, "阻断应用数"),
    CALL_NUM(8, "通话次数"),
    FAX_NUM(9, "传真次数"),
    SMS_NUM(10, "短信条数"),
    KEY_TARGET_ACCESS_NUM(11, "重点目标访问数"),
    ACCESS_ARCHIVE_NUM(12, "访问档案个数"),
    OCCUPIED_IP_NUM(13, "占用IP个数");

    /**
     * 统计维度对应的编码值
     */
    private final int code;

    /**
     * 统计维度对应的说明
     */
    private final String name;

    ArcStatisticDimension(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
