package com.semptian.archives.web.service.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/27 17:39
 **/
public class StringTemplateUtil {

    public static void main(String[] args) {
        String t6 = "SELECT any(data_id) data_id,any(auth_account) auth_account, any(auth_type) auth_type, any(virtual_account) virtual_account,\n" +
                "any (virtual_account_type) virtual_account_type, any(target_account) target_account, any(strsrc_ip) strsrc_ip,any(area_address) area_address,\n" +
                "any(tool_name) tool_name, any (uparea_id) uparea_id, any(strdst_ip) strdst_ip, any(src_port) src_port, any(dst_port) dst_port,any(user_name) user_name,any(visit_type) visit_type,\n" +
                "`domain`, count(1) linkCount, max(capture_time) capture_time \n" +
                "FROM ads_vaccount_action_log_all avala \n" +
                "WHERE `domain` is not null and `domain` <> '' \n" +
                "and avala.visit_type == 1\n" +
                "and capture_date = '{capture_date}'\n" +
                "and avala.insert_timestamp >= {startTime} \n" +
                "and avala.insert_timestamp < {startTime} \n" +
                "group by `domain`";
        String r6 = fillTemplate(t6, "2021-02-27", "111", "2222");
//        System.out.println(r6);

        System.out.println("--------------------");

        HashMap<String, Object> param = new HashMap<>();
        param.put("capture_date", "2021-02-27");
        param.put("startTime", "111");
        String s = fillTemplateByMap(t6, param);
        System.out.println(s);
    }

    public static String fillTemplateByMap(String stringTemplate, Map<String, Object> params) {
        String sql = stringTemplate;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (key != null && !"".equals(key) && value != null) {
                sql = sql.replaceAll("\\{" + key + "\\}", value.toString());
            }
        }

        return sql;
    }

    public static String fillTemplate(String stringTemplate, Object... params) {
        String descString = stringTemplate;

        for (Object param : params) {
            descString = descString.replaceFirst("\\{.*?\\}", param.toString());
        }

        return descString;
    }

    public static String fillTemplateALL(String stringTemplate, String radiusCondition,Integer accountLimit) {
        String descString = stringTemplate;

        descString = descString.replaceAll("\\{condition\\}", radiusCondition.toString());
        descString = descString.replaceFirst("\\{size\\}", accountLimit.toString());

        return descString;
    }

    /**
     * 逗号拼接
     * @param conditionList 列表
     * @param needQuotation 是否需要引号
     * @return '1','2' or 1,2
     */
    public static String appendComma(ArrayList<String> conditionList, boolean needQuotation) {
        if (conditionList != null && conditionList.size() > 0) {
            StringBuilder result = new StringBuilder();
            for (String condition : conditionList) {
                if (needQuotation) {
                    result.append("'").append(condition).append("'").append(",");
                } else {
                    result.append(condition).append(",");
                }
            }
            return result.toString().substring(0, result.length() - 1);
        } else {
            return "";
        }
    }

}
