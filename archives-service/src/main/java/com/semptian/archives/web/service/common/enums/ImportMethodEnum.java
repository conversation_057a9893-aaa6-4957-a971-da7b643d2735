package com.semptian.archives.web.service.common.enums;

public enum ImportMethodEnum {

    ONPAGE("ONPAGE",1,"导出当前页"),
    CONTENT("CONTENT",2,"导出所选内容"),
    ORDERBY("ORDERBY",3,"导出前5000条");


    private String key;

    private Integer value;

    private String description;

    ImportMethodEnum(String key, Integer value, String description) {
        this.key = key;
        this.value = value;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static Integer getByKey(String key) {
        ImportMethodEnum[] values = ImportMethodEnum.values();
        for (ImportMethodEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return 0;
    }
}
