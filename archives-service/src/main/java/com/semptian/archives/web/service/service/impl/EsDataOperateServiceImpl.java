package com.semptian.archives.web.service.service.impl;

import com.semptian.archives.web.service.service.EsDataOperateService;
import com.semptian.base.service.elasticsearch.DataOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ES数据操作服务实现类
 * <AUTHOR>
 * @date 2024/1/11
 */
@Service
@Slf4j
public class EsDataOperateServiceImpl implements EsDataOperateService {

    @Resource
    private DataOperation dataOperation;

    @Override
    public boolean add(String index, Object object) {
        return dataOperation.add(index, object);
    }

    @Override
    public boolean updateDataById(String index, String field, String id, Object object) {
        return dataOperation.updateDataById(index, field, id, object);
    }
}
