package com.semptian.archives.web.service.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date: 2020/11/19 18:04
 * Description:
 */
@Data
public class DetailQueryResultModel extends AbstractQueryResultModel implements Serializable {

    /**
     * 查询记录
     */
    private List<Map<String, Object>> records;

    /**
     * 分词后的结果
     */
    private List<String> terms;

    /**
     * 同义词
     */
    private Collection<String> synonyms;

    /**
     * 语种
     */
    private String lang;


    /**
     * 结果类型
     */
    private Integer resultType;


    /**
     * 搜索档案结果
     */
    List<HashMap<String, Object>> arcResult;

    /**
     * 是否是从缓存中取结果
     */
    private Boolean useCache = false;

    /**
     * 不过滤相似数据时的总数
     */
    private long noFilterSimilarDataTotal;

    /**
     * 过滤相似数据时的总数
     */
    private long filterSimilarDataTotal;

    /**
     * 游标查询返回的id
     */
    private String scrollId;
}
