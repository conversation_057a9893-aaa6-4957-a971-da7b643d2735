package com.semptian.archives.web.service.fegin;

import com.semptian.base.service.ReturnModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * resouce feign
 *
 * <AUTHOR>
 * @date 2024/10/14
 */
@FeignClient(name = "deye-common-proxy", path = "/")
public interface ResourceFeign {

    @RequestMapping(value = "/tag/tag/tag_value_list.json", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_VALUE)
    ReturnModel tagValueList(@RequestParam(name = "tagValueType") Integer tagValueType, @RequestParam(name = "lang") String lang);
}
