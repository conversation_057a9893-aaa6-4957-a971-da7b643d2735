package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 字段排序类型枚举
 *
 * <AUTHOR>
 * @date 2024/1/23
 */
@Getter
public enum SortTypeEnum {

    /**
     * 升序
     */
    ASC(0, "ASC"),

    /**
     * 降序
     */
    DESC(1, "DESC");

    private final Integer code;

    private final String name;

    SortTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        for (SortTypeEnum sortTypeEnum : SortTypeEnum.values()) {
            if (sortTypeEnum.getCode().equals(code)) {
                return sortTypeEnum.getName();
            }
        }
        return ASC.getName();
    }
}
