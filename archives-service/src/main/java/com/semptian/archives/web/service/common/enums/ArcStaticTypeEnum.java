package com.semptian.archives.web.service.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 档案统计(数量)类型
 * <AUTHOR> Hu
 * @date 2024/3/19
 */
@Getter
public enum ArcStaticTypeEnum {

    TOTAL("total", "全部"),

    ACTIVE("active", "近30天活跃"),

    LOSE_ACTIVE("loseActive", "近30天失活"),

    NEW("new", "近30天新增");

    private final String type;

    private final String description;

    ArcStaticTypeEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }

    /**
     * //根据type获取对应枚举值
     * @param type 统计类型
     * @return 统计类型枚举
     */
    public static ArcStaticTypeEnum getStaticTypeByType(String type) {
        if ("lostActive".equals(type)) {
            return ArcStaticTypeEnum.LOSE_ACTIVE;
        }

        if (StrUtil.isNotEmpty(type)) {
            for (ArcStaticTypeEnum arcStaticTypeEnum : ArcStaticTypeEnum.values()) {
                if (arcStaticTypeEnum.getType().equals(type)) {
                    return arcStaticTypeEnum;
                }
            }
        }
        return ArcStaticTypeEnum.TOTAL;
    }

}

