package com.semptian.archives.web.service.service.common;

import cn.hutool.core.collection.CollUtil;
import com.semptian.archives.web.service.fegin.AdsFeignClient;
import com.semptian.archives.web.service.model.AdsModel;
import com.semptian.base.service.ReturnModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上报地、协议等字典翻译器
 * */
@Component
public class DicTranslator {

    private static final Map<String, String> PROTOCOLS = new HashMap<>();

    @Autowired
    private AdsFeignClient adsFeignClient;

    public String translateProtocol(String protocolCode) {

        if (protocolCode == null) {
            return null;
        }
        if (PROTOCOLS.containsKey(protocolCode)) {
            return PROTOCOLS.get(protocolCode);
        } else {
            queryProtocolList();
        }

        return PROTOCOLS.getOrDefault(protocolCode, protocolCode + "");
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void queryProtocolList() {
        AdsModel adsModel = new AdsModel();
        adsModel.setParentId(-1);
        adsModel.setPageFlag(false);
        ReturnModel protocolList = adsFeignClient.getProtocolList(adsModel);

        if (protocolList.getCode() == 1 && protocolList.getData() != null) {
            Map<String, Object> map = (Map<String, Object>) protocolList.getData();
            Object records = map.get("records");
            if (records != null) {
                List<Map<String, Object>> mapList = (List<Map<String, Object>>) records;

                mapList.forEach(item -> {
                    PROTOCOLS.put(item.get("protocolId").toString(), (String) item.get("protocolName"));
                });
            }
        }
    }

    /**
     * 获取所有协议
     * @return 协议信息
     */
    public Map<String, String> getAllProtocol() {
        if (CollUtil.isEmpty(PROTOCOLS)) {
            queryProtocolList();
        }

        return PROTOCOLS;
    }
}
