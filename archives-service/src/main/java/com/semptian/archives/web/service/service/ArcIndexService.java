package com.semptian.archives.web.service.service;

import com.semptian.archives.web.service.model.ArcInfoModel;

import java.util.List;
import java.util.Map;

/**
 * @author: SunQi
 * @create: 2021/05/19
 * desc:
 **/
public interface ArcIndexService {

    /**
     * 按照活跃、失活、新增、总数来统计文档数量
     * */
    Map<String, Long> statisticArcAmount(List<Integer> permissionList, String userId);

    /**
     * 按照文档的类型统计文档的总数
     * */
    Map<String, Object> statisticArcAmountByType(String staticType,List<Integer> permissionList,String userId);

    /**
     * 获取用户档案权限
     *
     * @return 用户档案权限
     */
    List<Integer> getUserArchivePermission(String appId,String userId);

    /**
     * 全部档案-活跃档案Top10
     * @param arcType 档案类型
     * @param permissionList 用户权限
     * @return 档案列表
     */
    List<ArcInfoModel> allActiveArcTop10(Integer arcType, List<Integer> permissionList,String lang);

    /**
     * 全部档案-失活档案Top10
     * @param arcType 档案类型
     * @param permissionList 用户权限
     * @return 档案列表
     */
    List<ArcInfoModel> allLostActiveArcTop10(Integer arcType, List<Integer> permissionList, String lang);
}
