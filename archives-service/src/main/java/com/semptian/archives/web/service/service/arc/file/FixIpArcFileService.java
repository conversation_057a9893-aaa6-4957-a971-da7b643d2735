package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 固定IP档案文件服务实现类
 *
 * <AUTHOR> Hu
 * @date 2024/3/21
 */
@Service
public class FixIpArcFileService extends AbstractArcFileService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.FIXED_IP;
    }

    @Override
    protected void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx) {
        params.put("account_type_condition", " auth_type = " + AuthTypeEnum.FIXED_IP.getType() + " and auth_account ");
        params.put("dataType", "");

        //固定IP档案，需要判断档案建档日期和数据查询范围关系
        if (DateUtils.dateBefore(params.get("start_day").toString(), ctx.getCreateDay())) {
            if (StringUtils.isNotEmpty(ctx.getCreateDay())) {
                params.put("start_day", ctx.getCreateDay());
            }
        }
    }
}
