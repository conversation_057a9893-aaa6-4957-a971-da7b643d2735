package com.semptian.archives.web.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.archives.web.dao.archive.entity.ArcCollectionEntity;

import java.util.List;

/**
 * 档案收藏接口
 * <AUTHOR>
 * @since 2024/9/4
 */
public interface ArcCollectionService extends IService<ArcCollectionEntity> {

    /**
     * 查询指定用户的特定类型的关注档案信息
     * <p>
     * 当arcType = 0 时，查询所有类型的关注档案
     */
    List<ArcCollectionEntity> queryCareArcWithArcType(Long userId, String arcType);

}
