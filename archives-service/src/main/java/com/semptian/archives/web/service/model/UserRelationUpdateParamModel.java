package com.semptian.archives.web.service.model;

import lombok.Data;

/**
 * 节点备注和节点关系备注后关系扩线版本数据更新参数model
 *
 * <AUTHOR>
 * @since 2024/5/30
 */
@Data
public class UserRelationUpdateParamModel {

    /**
     * 当前档案ID
     */
    private String expansionArcId;

    /**
     * 语种
     */
    private String lang;

    /**
     * 备注节点ID
     */
    private String arcId;

    /**
     * 备注信息
     */
    private String remark;



    /**
     * 开始节点ID
     */
    String arc1Id;

    /**
     * 结束节点ID
     */
    String arc2Id;

    /**
     * 关系类型
     */
    String connectType;
}