package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Description 关系扩线请求模型
 * <AUTHOR>
 * @Date 2021/6/15
 */

@Data
@ApiModel(value = "RelationExpansionRequestModel", description = "关系扩线实体")
public class RelationExpansionRequestModel {

    /**
     * 档案id
     */
    @ApiModelProperty(notes = "档案id")
    private String arcId;

    /**
     * 档案类型
     */
    @ApiModelProperty(notes = "档案类型")
    private String arcType;

    /**
     * 认证账户
     */
    @ApiModelProperty(notes = "认证账户")
    private String authAccount;

    @ApiModelProperty(notes = "认证账户类型")
    private String authType;

    /**
     * 虚拟账号
     */
    @ApiModelProperty(notes = "虚拟账号/邮箱地址")
    private String account;


    @ApiModelProperty(notes = "虚拟账号应用类型")
    private String virtualAccountAppType;

    /**
     * 关系类型：0 全部； 1 同事; 2 朋友; 3 亲人； 4 夫妻 5其他
     */
    @ApiModelProperty(notes = "关系类型：0 全部； 1 同事; 2 朋友; 3 亲人； 4 夫妻 5 其他", required = false)
    private String connectType = "0";

    /**
     * 扩线开始时间
     */
    @ApiModelProperty(notes = "扩线开始时间 2021-06-22")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(notes = "结束时间 2021-06-22")
    private String endTime;

    /**
     * 最小通联次数
     */
    @ApiModelProperty(notes = "最小通联次数")
    private int minLinkCount=0;

    /**
     * 扩线规则： 1:传真 2:通话 3:短信 4:EMAIL 5:IM 多个用逗号分隔。默认为1,2,3,4,5
     */
    @ApiModelProperty(notes = "扩线规则： 1:传真 2:通话 3:短信 4:EMAIL 5:IM  多个用逗号分隔。默认为1,2,3,4,5", required = false)
    private String expansionRule="0";


    private Integer onPage = 1;

    private Integer size = 50;

    /**
     * 建档时间
     */
    private String createDay;
}
