package com.semptian.archives.web.service.fegin;

import com.semptian.base.service.ReturnModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: SunQi
 * @create: 2022/03/31
 * desc: 权限中心Api接口
 **/
@FeignClient(name = "deye-permission-service", path = "/permission")
public interface PermissionApiClient {


    /**
     * 根据用户id查询用户的数据权限
     *
     * @param userId
     * @param appId
     * @param serviceType
     * @return
     */
    @GetMapping("/data_permission_proxy/user_service_permission.json")
    ReturnModel queryUserDataPermission(@RequestParam("userId") String userId, @RequestParam("appId") String appId, @RequestParam("serviceType") String serviceType);


    /**
     * 获取用户档案权限
     * @param appId
     * @param serviceType
     * @param userId
     * @return
     */
    @GetMapping(value = "/data_permission_proxy/user_service_permission.json")
    ReturnModel getUserServicePermission(
            @RequestParam("appId") String appId,
            @RequestParam("serviceType") String serviceType,
            @RequestParam("userId") String userId
    );
}
