package com.semptian.archives.web.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 手机号统计模型
 * <AUTHOR>
 * @Date 2021/6/15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PhoneAllStatisticInfoModel {

    /**
     * 活跃总数
     */
    private long totalActive;

    /**
     * 近一周活跃数
     */
    private long oneWeekActive;

    /**
     * 总归属区域
     */
    private long totalConnectArea;

    /**
     * 近一周归属区域
     */
    private long oneWeekConnectArea;

    /**
     * 通话总数
     */
    private long totalCallNumber;

    /**
     * 近一周通话总数
     */
    private long oneWeekCallNumber;

    /**
     * 总信息数
     */
    private long totalMessageNumber;

    /**
     * 近一周信息数
     */
    private long oneWeekMessageNumber;

    /**
     * 附件总数
     */
    private long totalAttachmentNumber;

    /**
     * 近一周附件数
     */
    private long oneWeekAttachmentNumber;

    /**
     * 总声纹数
     */
//    private long totalVoiceNumber;

    /**
     * 近一周声纹数
     */
//    private long oneWeekVoiceNumber;
}
