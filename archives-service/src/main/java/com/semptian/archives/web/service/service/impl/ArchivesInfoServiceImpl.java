package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.core.common.enums.SortFieldEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.dao.archive.entity.ArcCollectionEntity;
import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.archives.web.service.common.config.PhoneOperatorConfig;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.*;
import com.semptian.archives.web.service.model.ArcCollectionModel;
import com.semptian.archives.web.service.model.ArcInfoModel;
import com.semptian.archives.web.service.model.ArcQueryModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.service.ArcCollectionService;
import com.semptian.archives.web.service.model.ExternalArcQueryModel;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.base.builders.BoolQueryBuilder;
import com.semptian.base.builders.QueryBuilder;
import com.semptian.base.builders.QueryBuilders;
import com.semptian.base.builders.TermQueryBuilder;
import com.semptian.base.service.ReturnModel;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: create by liuhao
 * @Date: 2020/10/22
 * @Desc: com.semptian.archives.web.service.service.impl
 **/
@Service
@Slf4j
public class ArchivesInfoServiceImpl implements ArchivesInfoService {

    @Resource
    private RedisOps redisOps;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private ArcDrillDownFieldTranslateConfigService translateConfigService;

    @Resource
    private ArcCollectionService arcCollectionService;

    @Value("${archive.query.upper.limit:100000000}")
    private Long queryUpperLimit;

    @Autowired
    PhoneOperatorConfig phoneOperatorConfig;

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Object queryArcInfo(ArcQueryModel arcQueryModel, DateModel dateModel, List<Integer> permissionList) {
        Map<String, Object> resultMap = new HashMap<>();
        long total = 0L;
        String archivesType = arcQueryModel.getArcType().toString();

        //排序字段参数转换
        arcQueryModel.setSortType(arcQueryModel.getSortType() == 1 ? arcQueryModel.getSortType() : 0);
        Map<String, String> sortFields = SortFieldEnum.getSortFieldMapByValueAndSortType(arcQueryModel.getSortField(), arcQueryModel.getSortType());

        Map<String, Object> esResultMap;
        List<ArcInfoModel> arcInfoModelList = new ArrayList<>();


        List<String> allCollectionArcIdList = Lists.newArrayList();
        Map<String, Long> arcCollectionInfoModelMap = new HashMap<>();
        //查询所有收藏档案
        List<ArcCollectionEntity> arcCollectionEntityList = getUserCollectionArcList(arcQueryModel.getUserId(), arcQueryModel.getArcType(), arcQueryModel.getSortType());
        if(CollectionUtils.isNotEmpty(arcCollectionEntityList)){
            allCollectionArcIdList = arcCollectionEntityList.stream().map(ArcCollectionEntity::getArchiveId).collect(Collectors.toList());
            arcCollectionInfoModelMap = arcCollectionEntityList.stream().collect(Collectors.toMap(ArcCollectionEntity::getArchiveId, ArcCollectionEntity::getCreateTime));
        }
        String redisKey = arcCommonService.getRedisKeyByIndexCount(true, "queryArcInfo", archivesType, arcQueryModel, dateModel, permissionList, allCollectionArcIdList);

        if (StringUtils.isBlank(arcQueryModel.getKeyWord()) && redisOps.hasKey(redisKey) && ArcCommonServiceImpl.isOpen) {
            resultMap = (HashMap<String, Object>) redisOps.get(redisKey);
            List<ArcInfoModel> arcInfoModels = JSONArray.parseArray(JSONObject.toJSONString(resultMap.get("list")), ArcInfoModel.class);
            arcInfoModelList.addAll(arcInfoModels);
            total = (long) resultMap.get("total");
        } else {
            //收藏档案查询，如果没有收藏档案直接返回空
            if(arcQueryModel.getIsCare() != null && arcQueryModel.getIsCare() == 1 && CollectionUtils.isEmpty(arcCollectionEntityList)){
                resultMap.put("list", Lists.newArrayList());
                resultMap.put("total", 0);
                return resultMap;
            }
            //构建查询条件
            QueryBuilder queryBuilder = buildQueryCondition(arcQueryModel, dateModel, Lists.newArrayList(arcCollectionInfoModelMap.keySet()), permissionList);
            log.info("search ES queryBuilder: {}, sortFields: {}, archivesType: {}", queryBuilder, sortFields, archivesType);

            //收藏档案查询的排序分页需要单独处理
            if(arcQueryModel.getIsCare() != null && arcQueryModel.getIsCare() == 1){
                esResultMap = arcCommonService.query(1, allCollectionArcIdList.size() * 2, queryBuilder, sortFields, arcQueryModel.getArcType(), permissionList);
            }
            else {
                esResultMap = arcCommonService.query(arcQueryModel.getOnPage(), arcQueryModel.getSize(), queryBuilder, sortFields, arcQueryModel.getArcType(), permissionList);
            }
            if (MapUtil.isEmpty(esResultMap)) {
                log.error("query es failed");
                return ReturnModel.getInstance().setCode(ArcServerReturnCode.INTERFACE_INNER_INVOKE_ERROR.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.INTERFACE_INNER_INVOKE_ERROR.getMsg()));
            }
            total = Long.parseLong(esResultMap.getOrDefault("total", "0").toString());
            List<ArcEsEntity> arcInfoList = (List) esResultMap.get("data");

            //将ES查询结果转换为前端需要的格式
           arcInfoModelList = arcInfoList.stream()
            .map(arcEsEntity -> this.arcEsEntity2ArcInfoModel(arcEsEntity, false, arcQueryModel.getLang()))
            .collect(Collectors.toList());

            //收藏档案查询的排序分页需要单独处理
            if(arcQueryModel.getIsCare() != null && arcQueryModel.getIsCare() == 1){
                arcInfoModelList = ArcCommonUtils.sortResult(arcInfoModelList, allCollectionArcIdList);
                if(CollectionUtils.isNotEmpty(arcInfoModelList) && arcInfoModelList.size() > arcQueryModel.getSize() * (arcQueryModel.getOnPage() - 1)){
                    arcInfoModelList = new ArrayList<>(arcInfoModelList.subList(arcQueryModel.getSize() * (arcQueryModel.getOnPage() - 1),
                            arcInfoModelList.size() < arcQueryModel.getSize() * arcQueryModel.getOnPage() ? arcInfoModelList.size() : arcQueryModel.getSize() * arcQueryModel.getOnPage() ));
                }
            }
        }


        //从基站历史信息表中，获取当前批次号码档案对应的最新imei
        List<String> phoneArcAccountList = arcInfoModelList.stream().filter(arcInfoModel -> Integer.parseInt(ArcTypeEnum.PHONE.getKey()) == arcInfoModel.getArcType()).map(ArcInfoModel::getArcAccount).collect(Collectors.toList());
        Map<String,String> phoneImeiMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(phoneArcAccountList)){

            List<String> phoneList = phoneArcAccountList.stream().map(account -> "'" + account + "'").collect(Collectors.toList());
            CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam();

            // 根据号码前缀进行分组 2135：表示O运营商；，2137：表示D运营商；， 2136：表示M运营商；
            List<String> oPhoneList = new ArrayList<>();
            List<String> dPhoneList = new ArrayList<>();
            List<String> mPhoneList = new ArrayList<>();

            buildPhoneList(phoneList, oPhoneList, dPhoneList, mPhoneList);

            List<Map<String, Object>> data = new ArrayList<>();
            dataAdd(oPhoneList, paramMap, data, phoneOperatorConfig.getOOperatorHis());
            dataAdd(dPhoneList, paramMap, data, phoneOperatorConfig.getDOperatorHis());
            dataAdd(mPhoneList, paramMap, data, phoneOperatorConfig.getMOperatorHis());

            if(CollectionUtils.isNotEmpty(data)){
                data.forEach(stringObjectMap -> {
                    String imei = (String) stringObjectMap.getOrDefault("imei", "");
                    String account = (String) stringObjectMap.getOrDefault("account", "");
                    if(StringUtils.isNotEmpty(imei) && StringUtils.isNotEmpty(account)){
                        phoneImeiMap.put(account, imei);
                    }
                });
            }
        }

        //给返回值添加是否收藏标志和收藏时间，同时检查档案别名在缓存周期内是否有变更
        Map<String, Long> finalArcCollectionInfoModelMap = arcCollectionInfoModelMap;
        arcInfoModelList.forEach(arcInfoModel -> {
            //添加是否收藏标志和收藏时间
            if(finalArcCollectionInfoModelMap.containsKey(arcInfoModel.getArcId())){
                arcInfoModel.setIsCare(1);
                arcInfoModel.setCollectionTime(finalArcCollectionInfoModelMap.get(arcInfoModel.getArcId()));
            }
            //检查档案别名是否有变更
            if(redisOps.hasKey(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()) && redisOps.get(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()) != null){
                arcInfoModel.setName((String) redisOps.get(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()));
            }
            //返回从基站历史表中查询到的最新imei
            if(Integer.parseInt(ArcTypeEnum.PHONE.getKey()) == arcInfoModel.getArcType() && phoneImeiMap.containsKey(arcInfoModel.getArcAccount())){
                arcInfoModel.setImei(phoneImeiMap.get(arcInfoModel.getArcAccount()));
            }
        });

        resultMap.put("list", arcInfoModelList);
        resultMap.put("total", total);

        if (!arcInfoModelList.isEmpty()) {
            Long nextHourStart = DateUtils.getNextHourStart(new Date());
            redisOps.set(redisKey, resultMap, (nextHourStart - ((new Date()).getTime())) / 1000);
        }
        return ReturnModel.getInstance().ok(resultMap);
    }

    private static void buildPhoneList(List<String> phoneList, List<String> oPhoneList, List<String> dPhoneList, List<String> mPhoneList) {
        for (String phone : phoneList) {
            if(phone.startsWith("'2135") || phone.startsWith("2135")){
                oPhoneList.add(phone);
            } else if(phone.startsWith("'2137") || phone.startsWith("2137")){
                dPhoneList.add(phone);
            } else if(phone.startsWith("'2136") || phone.startsWith("2136")){
                mPhoneList.add(phone);
            } else {
                mPhoneList.add(phone);
            }
        }
    }

    private void dataAdd(List<String> phoneList, CommonParamUtil.ParamMap paramMap, List<Map<String, Object>> data, String tableName) {
        if (CollectionUtil.isNotEmpty(phoneList)) {
            paramMap.put("arcAccount", String.join(",", phoneList));
            paramMap.put("ads_archive_phone_location_his_table", tableName);
            List<Map<String, Object>> tempData = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.PHONE_IMEI_LIST_BATCH.getValue(), paramMap);

            if (CollectionUtil.isNotEmpty(tempData)) {
                data.addAll(tempData);
            }
        }
    }

    public ArcInfoModel arcEsEntity2ArcInfoModel(ArcEsEntity arcEsEntity) {
        return arcEsEntity2ArcInfoModel(arcEsEntity, false, LanguageEnum.EN_US.getLang());
    }

    private List<ArcCollectionEntity> getUserCollectionArcList(String userId, Integer arcType, Integer sortType){

        //查询所有收藏档案
        QueryWrapper<ArcCollectionEntity> entityWrapper = new QueryWrapper<>();
        entityWrapper.eq("user_id", userId);
        if(arcType != null && arcType != 0){
            entityWrapper.eq("archive_type", arcType);
        }
        if(sortType == null || sortType == 1){
            entityWrapper.orderByDesc("create_time");
        }
        else {
            entityWrapper.orderByAsc("create_time");
        }
        return arcCollectionService.list(entityWrapper);
    }

    /**
     * ES档案实体转换为档案信息模型
     * @param arcEsEntity ES档案实体
     * @return 档案信息模型
     */
    public ArcInfoModel arcEsEntity2ArcInfoModel(ArcEsEntity arcEsEntity, boolean isDetail, String lang) {
        ArcInfoModel arcInfoModel = new ArcInfoModel();
        try {
            arcInfoModel.setId(arcEsEntity.getId());
            arcInfoModel.setArcId(arcEsEntity.getId());
            arcInfoModel.setArchivesType(arcEsEntity.getArchiveType());
            arcInfoModel.setArcType(arcEsEntity.getArchiveType());
            //档案arcAccount对应ES中archiveName,不可修改
            arcInfoModel.setArcAccount(arcEsEntity.getArchiveName());
            //档案name对应ES中archiveAlias, 用户可修改，默认为空
            arcInfoModel.setName(arcEsEntity.getArchiveAlias());

            arcInfoModel.setCreateTime(arcEsEntity.getCreateTime());
            arcInfoModel.setUpdateTime(arcEsEntity.getUpdateTime());
            arcInfoModel.setLastRelationTime(arcEsEntity.getLatestRelationTime());
            arcInfoModel.setActiveRate(Double.valueOf(Optional.ofNullable(arcEsEntity.getSortScore()).orElse(0L)));
            arcInfoModel.setActiveNum(arcEsEntity.getBehaviorNum());

            //应用档案特有字段
            arcInfoModel.setAppType(arcEsEntity.getAppType());
            arcInfoModel.setAppName(arcEsEntity.getArchiveName());

            //网站档案特有字段
            arcInfoModel.setDomainName(arcEsEntity.getArchiveName());

            //号码档案特有字段
            arcInfoModel.setImsi(arcEsEntity.getImsi());
            arcInfoModel.setImei(arcEsEntity.getImei());
            arcInfoModel.setLastRelationCountry(arcEsEntity.getLatestRelationCountry());
            arcInfoModel.setLastRelationCity(arcEsEntity.getLatestRelationCity());
            arcInfoModel.setTelNumber(arcEsEntity.getArchiveName());

            //radius和固定IP档案特有字段
            arcInfoModel.setArcAccountType(arcEsEntity.getAuthType());
            arcInfoModel.setRadius(arcEsEntity.getArchiveName());

            if (ObjectUtil.isNull(arcInfoModel.getArcType()) || StrUtil.isEmpty(arcInfoModel.getArcAccount())) {
                return arcInfoModel;
            }

            //字典值转换
            if (ArcTypeEnum.FIXED_IP.getKey().equals(arcEsEntity.getArchiveType().toString())
                    && ObjectUtil.isNotNull(arcEsEntity.getAccountType())) {

                arcInfoModel.setAccountType(I18nUtils.getMessage("fixed.ip.account.type." + FixedIpAccountTypeEnum.getNameByCode(arcEsEntity.getAccountType())));
            } else if (ArcTypeEnum.IM.getKey().equals(arcEsEntity.getArchiveType().toString())){
                // appType 字典转换
                Object childTypeObj = translateConfigService.fieldTranslate("child_type", arcInfoModel.getAppType(), lang);

                if (childTypeObj != null) {
                    arcInfoModel.setAppName(childTypeObj.toString());
                }
            }

            //补充档案认证账号类型信息
            ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(arcEsEntity.getArchiveType().toString());
            switch (Objects.requireNonNull(arcTypeEnum)) {
                case RADIUS:
                    arcInfoModel.setArcAccountType(AuthTypeEnum.RADIUS.getType().toString());
                    break;
                case FIXED_IP:
                    arcInfoModel.setArcAccountType(AuthTypeEnum.FIXED_IP.getType().toString());
                    break;
                case PHONE:
                    arcInfoModel.setArcAccountType(AuthTypeEnum.PHONE.getType().toString());
                    break;
                case APP:
                    arcInfoModel.setArcAccountType(arcInfoModel.getAppType());
                    break;
                case IM:
                    arcInfoModel.setArcAccountType(arcInfoModel.getAppType());
                    break;
                case NET_PROTOCOL:
                    arcInfoModel.setArcAccountType(arcInfoModel.getAppType());
                    break;
            }

            //查询档案详情信息时补全详细信息
            if (isDetail) {
                CommonParamUtil.ParamMap param = CommonParamUtil.buildCommonTimeParam();
                param.put("arc_account", arcInfoModel.getArcAccount());
                Map<String, Object> resultMap;
                switch (Objects.requireNonNull(arcTypeEnum)) {
                    case RADIUS:
                    case FIXED_IP:
                        //档案类型为radius或固定IP时，查询radius档案统计表获取档案最近关联IP地址、档案最近关联国家
                        param.put("auth_type", arcInfoModel.getArcAccountType());

                        resultMap = getArcStatistics(BusinessCodeEnum.RADIUS_QUERY_AUTH_ACCOUNT_STATISTICS.getValue(), param);

                        if (MapUtil.isNotEmpty(resultMap)) {
                            arcInfoModel.setIp(ObjectUtil.isNotNull(resultMap.get("latestRelationIp")) ? resultMap.get("latestRelationIp").toString() : "");
                            arcInfoModel.setLastRelationCountry(ObjectUtil.isNotNull(resultMap.get("latestRelationCountry")) ? resultMap.get("latestRelationCountry").toString() : "");
                            arcInfoModel.setLastRelationCity(ObjectUtil.isNotNull(resultMap.get("latestRelationCity")) ? resultMap.get("latestRelationCity").toString() : "");
                        }
                        break;
                    case NET_PROTOCOL:
                    case APP:
                        param.put("app_type", arcInfoModel.getAppType());

                        resultMap = getArcStatistics(BusinessCodeEnum.APP_QUERY_STATISTICS.getValue(), param);

                        if (MapUtil.isNotEmpty(resultMap)) {
                            arcInfoModel.setIp(ObjectUtil.isNotNull(resultMap.get("latestRelationIp")) ? resultMap.get("latestRelationIp").toString() : "");
                        }
                        break;
                    case WEB_SITE:
                        resultMap = getArcStatistics(BusinessCodeEnum.DOMAIN_QUERY_STATISTICS.getValue(), param);

                        //网站档案最近关联IP和最近IP归属地统一从Doris中获取
                        arcInfoModel.setIp("");
                        arcInfoModel.setLastRelationCountry("");
                        arcInfoModel.setLastRelationCity("");

                        if (MapUtil.isNotEmpty(resultMap)) {
                            arcInfoModel.setIp(ObjectUtil.isNotNull(resultMap.get("latestRelationIp")) ? resultMap.get("latestRelationIp").toString() : "");
                            arcInfoModel.setLastRelationCountry(ObjectUtil.isNotNull(resultMap.get("latestRelationCountry")) ? resultMap.get("latestRelationCountry").toString() : "");
                            arcInfoModel.setLastRelationCity(ObjectUtil.isNotNull(resultMap.get("latestRelationCity")) ? resultMap.get("latestRelationCity").toString() : "");
                        }
                        break;
                    case PHONE:

                        String countryCode = DeyeGetCountryCodeByPhoneNumber.getCountryNameByPhoneNumber(arcInfoModel.getArcAccount());
                        Object fieldTranslate = translateConfigService.fieldTranslate(CommonConstent.COUNTRY_CODE_FIELD, countryCode, lang);
                        if (ObjectUtil.isNotNull(fieldTranslate) && StrUtil.isNotEmpty(fieldTranslate.toString())) {
                            arcInfoModel.setAttribution(fieldTranslate.toString());
                        }

                        resultMap = getArcStatistics(BusinessCodeEnum.PHONE_LAST_RELATION_AREA_STATISTICS.getValue(), param);

                        if (MapUtil.isNotEmpty(resultMap)) {
                            arcInfoModel.setLastRelationCountry(ObjectUtil.isNotNull(resultMap.get("latestRelationCountry")) ? resultMap.get("latestRelationCountry").toString() : "");
                            arcInfoModel.setLastRelationCity(ObjectUtil.isNotNull(resultMap.get("latestRelationCity")) ? resultMap.get("latestRelationCity").toString() : "");
                        }

                        break;
                    case EMAIL:
                        resultMap = getArcStatistics(BusinessCodeEnum.EMAIL_LAST_RELATION_AREA_STATISTICS.getValue(), param);

                        if (MapUtil.isNotEmpty(resultMap)) {
                            arcInfoModel.setLastRelationCountry(ObjectUtil.isNotNull(resultMap.get("latestRelationCountry")) ? resultMap.get("latestRelationCountry").toString() : "");
                            arcInfoModel.setLastRelationCity(ObjectUtil.isNotNull(resultMap.get("latestRelationCity")) ? resultMap.get("latestRelationCity").toString() : "");
                        }
                        break;
                    case IM:

                        if (StringUtils.isNotEmpty(arcInfoModel.getArcAccountType())) {
                            param.put("app_type", " AND virtual_app_type = " + arcInfoModel.getArcAccountType());
                        } else {
                            param.put("app_type", "");
                        }
                        resultMap = getArcStatistics(BusinessCodeEnum.IM_LAST_RELATION_AREA_STATISTICS.getValue(), param);

                        if (MapUtil.isNotEmpty(resultMap)) {
                            arcInfoModel.setLastRelationCountry(ObjectUtil.isNotNull(resultMap.get("latestRelationCountry")) ? resultMap.get("latestRelationCountry").toString() : "");
                            arcInfoModel.setLastRelationCity(ObjectUtil.isNotNull(resultMap.get("latestRelationCity")) ? resultMap.get("latestRelationCity").toString() : "");
                        }
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e) {
            log.error("arcEsEntity to arcInfoModel error", e);
        }
        return arcInfoModel;
    }

    @Override
    public List<ArcInfoModel> getArcInfoByIds(List<String> arcIds, Integer arcType) {
        List<ArcInfoModel> list = Lists.newArrayList();

        List<ArcEsEntity> arcEsEntityList;
        if (CommonConstent.ARC_TYPE_ALL.equals(arcType)) {
            arcEsEntityList = arcCommonService.getArcInfoByIds(arcIds);
        } else {
            arcEsEntityList = arcCommonService.getArcInfoByIds(arcIds, ArcTypeEnum.getArcTypeByKey(arcType.toString()));
        }

        if (CollUtil.isNotEmpty(arcEsEntityList)) {
            list = arcEsEntityList.stream().map(this::arcEsEntity2ArcInfoModel).collect(Collectors.toList());
        }
        list.forEach(arcInfoModel -> {
            //检查档案别名是否有变更
            if(redisOps.hasKey(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()) && redisOps.get(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()) != null){
                arcInfoModel.setName((String) redisOps.get(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()));
            }
        });
        return list;
    }

    @Override
    public Boolean updateArcCollection(ArcCollectionModel arcCollectionModel, String userId) {

        //收藏档案
        if(arcCollectionModel != null && arcCollectionModel.getIsCare() != null && arcCollectionModel.getIsCare() == 1){

            //检查是否已收藏，已收藏直接返回
            QueryWrapper<ArcCollectionEntity> entityWrapper = new QueryWrapper<>();
            entityWrapper.eq("user_id", userId).eq("archive_id", arcCollectionModel.getArcId());
            int count = arcCollectionService.count(entityWrapper);
            if(count > 0){
                return true;
            }

            ArcCollectionEntity arcCollectionEntity = new ArcCollectionEntity();
            arcCollectionEntity.setArchiveId(arcCollectionModel.getArcId());
            arcCollectionEntity.setArchiveType(arcCollectionModel.getArcType());
            arcCollectionEntity.setUserId(userId);
            arcCollectionEntity.setCreateTime(System.currentTimeMillis());
            return arcCollectionService.save(arcCollectionEntity);
        }
        //取消收藏
        else if(arcCollectionModel != null && arcCollectionModel.getIsCare() != null && arcCollectionModel.getIsCare() == 0){
            QueryWrapper<ArcCollectionEntity> entityWrapper = new QueryWrapper<>();
            entityWrapper.eq("user_id", userId).eq("archive_id", arcCollectionModel.getArcId());
            return arcCollectionService.remove(entityWrapper);
        }

        return false;
    }

    @Override
    public Boolean clearArcCollection(String userId) {
        QueryWrapper<ArcCollectionEntity> entityWrapper = new QueryWrapper<>();
        entityWrapper.eq("user_id", userId);
        return arcCollectionService.remove(entityWrapper);
    }


    @Override
    public Object arcQuery(ExternalArcQueryModel externalArcQueryModel, List<Integer> permissionList) {
        Map<String, Object> resultMap = new HashMap<>();
        long total = 0L;

        //构建查询条件
        QueryBuilder queryBuilder = buildExternalQueryCondition(externalArcQueryModel);

        Map<String, Object> esResultMap;
        List<ArcInfoModel> arcInfoModelList = new ArrayList<>();

        //查询全部档案全部类型档案缓存key
        String redisKey = arcCommonService.getRedisKeyByIndexCount(true, "arcQuery", externalArcQueryModel, permissionList);
        if (redisOps.hasKey(redisKey) && ArcCommonServiceImpl.isOpen) {
            resultMap = (HashMap<String, Object>) redisOps.get(redisKey);
            List<ArcInfoModel> arcInfoModels = JSONArray.parseArray(JSONObject.toJSONString(resultMap.get("list")), ArcInfoModel.class);
            arcInfoModelList.addAll(arcInfoModels);

            resultMap.put("list", arcInfoModelList);
            return resultMap;
        } else {
            log.info("search ES queryBuilder: {}, sortFields: {}, externalArcQueryModel: {}", queryBuilder, externalArcQueryModel);
            esResultMap = arcCommonService.query(1, 10, queryBuilder, new HashMap<>(),externalArcQueryModel.getArcTypes(), permissionList);

            if (MapUtil.isEmpty(esResultMap)) {
                log.error("query es failed");
                return ReturnModel.getInstance().setCode(ArcServerReturnCode.INTERFACE_INNER_INVOKE_ERROR.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.INTERFACE_INNER_INVOKE_ERROR.getMsg()));
            }

            total = Long.parseLong(esResultMap.getOrDefault("total", "0").toString());
            List<ArcEsEntity> arcInfoList = (List) esResultMap.get("data");

            //将ES查询结果转换为前端需要的格式
            arcInfoModelList = arcInfoList.stream().map(this::arcEsEntity2ArcInfoModel).collect(Collectors.toList());
        }

        resultMap.put("list", arcInfoModelList);
        resultMap.put("total", total);

        if (!arcInfoModelList.isEmpty()) {
            Long nextHourStart = DateUtils.getNextHourStart(new Date());
            redisOps.set(redisKey, resultMap, (nextHourStart - ((new Date()).getTime())) / 1000);
        }

        return ReturnModel.getInstance().ok(resultMap);
    }

    private QueryBuilder buildExternalQueryCondition(ExternalArcQueryModel externalArcQueryModel) {
        // 查询规则如下
        // 1 若 authAccount 认为需要查询 radius/号码/及固定ip档案
        // 2 若 virtualAccount 不为空，且 dataType 为 101 认为需要查询 EMAIL 档案
        // 3 若 virtualAccount 不为空，且 dataType 为 103 认为需要查询 IM 档案
        // 4 若 appName和 appType 不为空，则需要查询应用档案对应索引,包含应用档案和协议档案数据
        // 5 查询不限制时间范围
        // 6 档案名称精确查询
        // 7 返回结构参考 arc_info 返回
        // 默认情况下查询所有档案

        BoolQueryBuilder totalBoolBuilder = QueryBuilders.boolQuery();

        if (StringUtils.isNotBlank(externalArcQueryModel.getAuthAccount())) {
            totalBoolBuilder.must(QueryBuilders.termQuery("archive_name.keyword", externalArcQueryModel.getAuthAccount()));
        } else if (StringUtils.isNotBlank(externalArcQueryModel.getVirtualAccount())) {
            totalBoolBuilder.must(QueryBuilders.termQuery("archive_name.keyword", externalArcQueryModel.getVirtualAccount()));
            if (DataTypeEnum.IM.getKey().equals(externalArcQueryModel.getDataType())) {
                if (StringUtils.isNotBlank(externalArcQueryModel.getChildType())) {
                    QueryBuilder appType = QueryBuilders.termQuery(ArcQueryEsConditionEnum.APP_TYPE.getFieldName(), externalArcQueryModel.getChildType());
                    totalBoolBuilder.must(appType);
                }
            }
        } else if (StringUtils.isNotBlank(externalArcQueryModel.getAppName())
                && StringUtils.isNotBlank(externalArcQueryModel.getAppType())) {
            totalBoolBuilder.must(QueryBuilders.termQuery("archive_name.keyword", externalArcQueryModel.getAppName()));
            totalBoolBuilder.must(QueryBuilders.termQuery(ArcQueryEsConditionEnum.APP_TYPE.getFieldName(), externalArcQueryModel.getAppType().toLowerCase()));
        }

        return totalBoolBuilder;
    }
    /**
     * 构建档案ES查询条件
     * @param arcQueryModel 档案查询模型
     * @return ES查询条件
     */
    private BoolQueryBuilder buildQueryCondition(ArcQueryModel arcQueryModel, DateModel dateModel, List<String> allCollectionArcIdList, List<Integer> permissionList) {
        BoolQueryBuilder totalBoolBuilder = QueryBuilders.boolQuery();
        String queryCondition = "";
        if (StringUtils.isNotBlank(arcQueryModel.getKeyWord())) {
            String keyWord = EscapeUtil.escapeAll(arcQueryModel.getKeyWord());
            //判断keyWord是否为纯数字，如果是则在末尾拼接*，否则不拼接
            if (keyWord.matches("[0-9]+")) {
                keyWord = keyWord + "*";
            }
            queryCondition += keyWord;
        }else {
            queryCondition = "*";
        }

        // 创建字段名称和权重的映射关系
        Map<String, Float> map = new HashMap<>(4);
        map.put(ArcQueryEsConditionEnum.ARCHIVE_NAME.getFieldName(), (float) 1);
        map.put(ArcQueryEsConditionEnum.ARCHIVE_ALIAS.getFieldName(), (float) 1);

        BoolQueryBuilder queryStringBuilder = QueryBuilders.boolQuery().must(QueryBuilders.queryStringQuery(queryCondition).defaultOperator("and").fields(map));
        totalBoolBuilder.must(queryStringBuilder);

        //公共查询条件活跃开始日期和活跃结束日期
        if (ObjectUtil.isNotNull(dateModel.getDateOption())
                && StringUtils.isNotBlank(dateModel.getStartDay()) && StringUtils.isNotBlank(dateModel.getEndDay())) {
            //构建公共查询条件活跃日期
            CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
            Long lastRelationTimeStart = DateUtils.getOneDayStart(String.valueOf(paramMap.get("start_day")));
            Long lastRelationTimeEnd = DateUtils.getOneDayEnd(String.valueOf(paramMap.get("end_day")));

            QueryBuilder latestRelationTimeRange = QueryBuilders.rangeQuery(ArcQueryEsConditionEnum.LAST_RELATION_TIME.getFieldName()).gte(lastRelationTimeStart).lte(lastRelationTimeEnd);
            totalBoolBuilder.filter(latestRelationTimeRange);
        }

        //收藏档案查询，需要添加该用户所有收藏档案条件
        if(arcQueryModel.getIsCare() == null || arcQueryModel.getIsCare() == 1){
            QueryBuilder idQuery = QueryBuilders.idsQuery().addIds(allCollectionArcIdList.toArray(new String[]{}));
            totalBoolBuilder.must(idQuery);
        }

        //档案类型为全部，则只有公共查询条件活跃日期
        ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(arcQueryModel.getArcType().toString());
        if (arcTypeEnum == null) {
            //如果档案类型选择全部,但应用档案和协议档案的权限只勾选了一个没有勾选两个,由于这两种档案共用同一个索引,因此需要对其单独处理过滤档案类型
            if ((permissionList.contains(Integer.valueOf(ArcTypeEnum.APP.getKey())) && !permissionList.contains(Integer.valueOf(ArcTypeEnum.NET_PROTOCOL.getKey())))
                    || (permissionList.contains(Integer.valueOf(ArcTypeEnum.NET_PROTOCOL.getKey())) && !permissionList.contains(Integer.valueOf(ArcTypeEnum.APP.getKey())))) {
                BoolQueryBuilder appTypeBoolBuilder = QueryBuilders.boolQuery();
                for (Integer permission : permissionList) {
                    QueryBuilder arcTypeQueryBuilder = QueryBuilders.termQuery("archive_type", permission);
                    appTypeBoolBuilder.should(arcTypeQueryBuilder);
                }
                totalBoolBuilder.filter(appTypeBoolBuilder);
            }
            return totalBoolBuilder;
        }

        //除了号码档案其他档案查询条件
        //是否有文件
        if (arcQueryModel.getFileFlag() != null && arcQueryModel.getFileFlag() != 99) {
            QueryBuilder fileFlag = QueryBuilders.termQuery(ArcQueryEsConditionEnum.FILE_FLAG.getFieldName(), arcQueryModel.getFileFlag());
            totalBoolBuilder.filter(fileFlag);
        }

        //根据档案类型拼接查询条件
        switch (arcTypeEnum) {
            case PHONE:
                //号码档案查询条件
                //是否radius
                if (arcQueryModel.getIsRadius() != null && arcQueryModel.getIsRadius() != 99) {
                    QueryBuilder isRadius;
                    if (arcQueryModel.getIsRadius() == 0) {
                        isRadius = QueryBuilders.termQuery(ArcQueryEsConditionEnum.IS_RADIUS.getFieldName(), 0L);
                    }else {
                        isRadius = QueryBuilders.rangeQuery(ArcQueryEsConditionEnum.IS_RADIUS.getFieldName()).gt(0L).lte(queryUpperLimit);
                    }
                    totalBoolBuilder.filter(isRadius);
                }

                //是否有传真
                if (arcQueryModel.getHasFax() != null && arcQueryModel.getHasFax() != 99) {
                    QueryBuilder hasFax;
                    if (arcQueryModel.getHasFax() == 0) {
                        hasFax = QueryBuilders.termQuery(ArcQueryEsConditionEnum.HAS_FAX.getFieldName(), 0L);
                    } else {
                        hasFax = QueryBuilders.rangeQuery(ArcQueryEsConditionEnum.HAS_FAX.getFieldName()).gt(0L).lte(queryUpperLimit);
                    }
                    totalBoolBuilder.filter(hasFax);
                }

                //活跃地-国家级
                if (StringUtils.isNotBlank(arcQueryModel.getLatestRelationCountry()) && !"all".equals(arcQueryModel.getLatestRelationCountry())) {
                    QueryBuilder latestRelationCountry = QueryBuilders.termQuery(ArcQueryEsConditionEnum.LATEST_RELATION_COUNTRY.getFieldName(), arcQueryModel.getLatestRelationCountry().toLowerCase());
                    totalBoolBuilder.filter(latestRelationCountry);
                }

                //活跃地-城市级
                if (StringUtils.isNotBlank(arcQueryModel.getLatestRelationCity()) && !"all".equals(arcQueryModel.getLatestRelationCity())) {
                    QueryBuilder latestRelationCity = QueryBuilders.termQuery(ArcQueryEsConditionEnum.LATEST_RELATION_CITY.getFieldName(), arcQueryModel.getLatestRelationCity().toLowerCase());
                    totalBoolBuilder.filter(latestRelationCity);
                }
                break;
            case APP:
                //应用档案查询条件
                //应用类型
                if (StringUtils.isNotBlank(arcQueryModel.getAppType())) {
                    String[] appTypeArr = arcQueryModel.getAppType().split(",");

                    if (appTypeArr.length == 1) {
                        QueryBuilder appType = QueryBuilders.termQuery(ArcQueryEsConditionEnum.APP_TYPE.getFieldName(), arcQueryModel.getAppType());
                        totalBoolBuilder.filter(appType);
                    } else {
                        //appType多个时，拼接or查询
                        BoolQueryBuilder appTypeBoolBuilder = QueryBuilders.boolQuery();
                        for (String appType : appTypeArr) {
                            QueryBuilder appTypeQueryBuilder = QueryBuilders.termQuery(ArcQueryEsConditionEnum.APP_TYPE.getFieldName(), appType);
                            appTypeBoolBuilder.should(appTypeQueryBuilder);
                        }
                        totalBoolBuilder.filter(appTypeBoolBuilder);
                    }
                } else {
                    TermQueryBuilder arcType = QueryBuilders.termQuery("archive_type", arcTypeEnum.getKey());

                    totalBoolBuilder.must(arcType);
                }
                break;
            case FIXED_IP:
                //固定IP档案查询条件
                //账号类型
                if (arcQueryModel.getAccountType() != null && arcQueryModel.getAccountType() != 99) {
                    QueryBuilder accountType = QueryBuilders.termQuery(ArcQueryEsConditionEnum.ACCOUNT_TYPE.getFieldName(), arcQueryModel.getAccountType());
                    totalBoolBuilder.filter(accountType);
                }
                break;
            case EMAIL:
                // EMAIL 查询条件
                break;
            case IM:
                if (StringUtils.isNotBlank(arcQueryModel.getAppType())) {
                    String[] appTypeArr = arcQueryModel.getAppType().split(",");

                    if (appTypeArr.length == 1) {
                        QueryBuilder appType = QueryBuilders.termQuery(ArcQueryEsConditionEnum.APP_TYPE.getFieldName(), arcQueryModel.getAppType());
                        totalBoolBuilder.filter(appType);
                    } else {
                        //appType多个时，拼接or查询
                        BoolQueryBuilder appTypeBoolBuilder = QueryBuilders.boolQuery();
                        for (String appType : appTypeArr) {
                            QueryBuilder appTypeQueryBuilder = QueryBuilders.termQuery(ArcQueryEsConditionEnum.APP_TYPE.getFieldName(), appType);
                            appTypeBoolBuilder.should(appTypeQueryBuilder);
                        }
                        totalBoolBuilder.filter(appTypeBoolBuilder);
                    }
                }
                break;
            case NET_PROTOCOL:
                TermQueryBuilder arcType = QueryBuilders.termQuery("archive_type", arcTypeEnum.getKey());

                totalBoolBuilder.must(arcType);
                break;
            default:
                break;
        }

        return totalBoolBuilder;
    }

    @Override
    public ArcInfoModel getArcInfoById(String arcId, String arcAccount, Integer arcType, String arcAccountType, String userId, String lang) {

        ArcEsEntity arcEsEntity;
        if (StrUtil.isNotEmpty(arcId)) {
            arcEsEntity = arcCommonService.getArcInfoById(arcId, arcType);
        } else {
            arcEsEntity = arcCommonService.getArcInfoByArcAccount(arcAccount, arcType, arcAccountType);
        }

        if (arcEsEntity != null) {
            //类型转换
            ArcInfoModel arcInfoModel = arcEsEntity2ArcInfoModel(arcEsEntity, true, lang);

            //查询所有收藏档案
            QueryWrapper<ArcCollectionEntity> entityWrapper = new QueryWrapper<>();
            entityWrapper.eq("user_id", userId).eq("archive_id", arcInfoModel.getArcId());
            arcInfoModel.setIsCare(arcCollectionService.count(entityWrapper) > 0 ? 1 : 0);

            //检查档案别名是否有变更
            if(redisOps.hasKey(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()) && redisOps.get(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()) != null){
                arcInfoModel.setName((String) redisOps.get(ArcCommonServiceImpl.UPDATE_ARC_NAME_KEY + arcInfoModel.getArcId()));
            }

            if(ArcTypeEnum.IM.getKey().equalsIgnoreCase(arcInfoModel.getArcType() + "")){

                //IM档案的账号要拼上最新的昵称信息
                Map<String, Object> params = new HashMap<>();
                params.put("arc_name", arcInfoModel.getArcAccount());
                params.put("data_type", DataTypeEnum.IM.getKey());

                //获取IM档案关联昵称的最新日期
                List<Map<String, Object>> list = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.IM_LAST_RELATIOJN_NICKNAME_CAPTUREDAY.getValue(), params, true);
                if(CollectionUtils.isNotEmpty(list) && list.get(0).containsKey("capture_day")
                        && StringUtils.isNotEmpty(list.get(0).getOrDefault("capture_day", "").toString())){
                    String captureDay = list.get(0).getOrDefault("capture_day", "").toString();
                    params.put("capture_day",captureDay);

                    List<Map<String, Object>> nickNameList = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.IM_LAST_RELATIOJN_NICKNAME.getValue(), params, true);
                    if(CollectionUtils.isNotEmpty(nickNameList) && nickNameList.get(0).containsKey("alias")
                            && StringUtils.isNotEmpty(nickNameList.get(0).getOrDefault("alias", "").toString())){
                        arcInfoModel.setNickName(nickNameList.get(0).getOrDefault("alias", "").toString());
                    }
                }
            }

            if(ArcTypeEnum.PHONE.getKey().equalsIgnoreCase(arcInfoModel.getArcType() + "")){

                //从基站历史信息表中，获取当前批次号码档案对应的最新imei

                List<String> phoneArcAccountList = Lists.newArrayList(arcInfoModel.getArcAccount());
                List<String> phoneList = phoneArcAccountList.stream().map(account -> "'" + account + "'").collect(Collectors.toList());
                CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam();

                // 根据号码前缀进行分组 2135：表示O运营商；，2137：表示D运营商；， 2136：表示M运营商；
                List<String> oPhoneList = new ArrayList<>();
                List<String> dPhoneList = new ArrayList<>();
                List<String> mPhoneList = new ArrayList<>();

                buildPhoneList(phoneList, oPhoneList, dPhoneList, mPhoneList);

                List<Map<String, Object>> data = new ArrayList<>();
                dataAdd(oPhoneList, paramMap, data, phoneOperatorConfig.getOOperatorHis());
                dataAdd(dPhoneList, paramMap, data, phoneOperatorConfig.getDOperatorHis());
                dataAdd(mPhoneList, paramMap, data, phoneOperatorConfig.getMOperatorHis());

                if(CollectionUtils.isNotEmpty(data)){
                    data.forEach(stringObjectMap -> {
                        String imei = (String) stringObjectMap.getOrDefault("imei", "");
                        if(StringUtils.isNotEmpty(imei)){
                            arcInfoModel.setImei(imei);
                        }
                    });
                }
            }

            // 因为最后关联城市中存在英文，做置空处理
            arcInfoModel.setLastRelationCity("");
            return arcInfoModel;
        }
        log.info("*******查询档案信息失败");
        return new ArcInfoModel();
    }

    /**
     * 查询档案统计信息
     * @param businessCode 业务码
     * @param param 查询参数
     * @return 档案统计信息
     */
    private Map<String, Object> getArcStatistics(String businessCode, Map<String, Object> param) {
        List<Map<String, Object>> listResult = arcCommonService.getCommonServiceListResult(businessCode, param);
        if (CollUtil.isNotEmpty(listResult)) {
            return listResult.get(0);
        }
        return new HashMap<>();
    }

}

