package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/29 12:08
 * Description: 高频词查询相关模型
 */
@ApiModel("LIS和NF记录趋势及分布模型")
@Data
public class HighFrequencyWordsModel {

    /**
     * 档案ID
     */
    @ApiModelProperty(value = "档案ID")
    private String arcId;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "档案类型")
    private Integer arcType;

    /**
     * 档案账号
     */
    @ApiModelProperty(value = "档案账号")
    private String arcAccount;

    /**
     * 档案账号类型
     */
    @ApiModelProperty(value = "档案账号类型")
    private String arcAccountType;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDay;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDay;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private String createDay;

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption;

    /**
     * 数据来源 1=邮件热词;2=短信热词，3=全部热词,默认为1
     */
    @ApiModelProperty(value = "数据来源")
    private Integer dataSource = 1;

    /**
     * onPage
     */
    @ApiModelProperty(value = "页数")
    private Integer onPage;

    /**
     * size
     */
    @ApiModelProperty(value = "每页大小")
    private Integer size;

    /**
     * lang
     */
    @ApiModelProperty(value = "语言")
    private String lang;


    @ApiModelProperty(value = "排序字段，默认按行为次数排序")
    private String sortField;

    @ApiModelProperty(value = "排序方式，默认为降序，1=降序；2=升序")
    private Integer sortType;


}
