package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.service.common.enums.BusinessTypeEnum;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.ArcFeatures;
import com.semptian.archives.web.service.service.arc.ArcFunctional;

import java.util.Map;

/**
 * 档案文件查询服务接口
 * <AUTHOR>
 * @date 2024/3/22
 */
public interface ArcFileService extends ArcFunctional {

    @Override
    default ArcFeatures feature() {
        return ArcFeatures.FILE_INFO;
    }

    /**
     * 获取档案文件信息列表
     *
     * @param ctx 查询上下文
     * @param businessTypeEnum 业务类型
     * @return 文件信息列表
     */
    Map<String, Object> getAttachInfo(ArcContext ctx, BusinessTypeEnum businessTypeEnum);

}
