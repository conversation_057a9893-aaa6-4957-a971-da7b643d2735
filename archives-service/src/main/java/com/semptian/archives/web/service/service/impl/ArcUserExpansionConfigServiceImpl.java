package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.archives.web.dao.archive.entity.ArcUserExpansionConfigEntity;
import com.semptian.archives.web.dao.archive.mapper.ArcUserExpansionConfigMapper;
import com.semptian.archives.web.service.service.ArcUserExpansionConfigService;
import org.springframework.stereotype.Service;

/**
 * 用户扩线配置表接口实现
 * <AUTHOR>
 * @since 2024/6/3
 */
@Service
public class ArcUserExpansionConfigServiceImpl extends ServiceImpl<ArcUserExpansionConfigMapper, ArcUserExpansionConfigEntity> implements ArcUserExpansionConfigService {

}