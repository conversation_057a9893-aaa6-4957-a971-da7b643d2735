package com.semptian.archives.web.service.model;

import lombok.Data;

import java.util.List;

/**
 * 重要目标model
 * <AUTHOR>
 * @since  2024/4/28
 */
@Data
public class ImportantTargetModel {

    /**
     * 重要目标账号
     */
    private String importantTarget;

    /**
     * 重要目标类型: 1=固网RADIUS账号,3=固定IP账号,2=移动网RADIUS账号,4=自定义用户（IP或者IP段）
     */
    private String importantTargetType;

    /**
     * 重要目标分类(当前节点)
     */
    private String importantTargetCategory;

    /**
     * 重要目标分类列表(包含从一级节点到当前节点)
     */
    private List<String> importantTargetCategoryList;
}
