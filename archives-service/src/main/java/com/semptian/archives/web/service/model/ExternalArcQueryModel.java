package com.semptian.archives.web.service.model;

import lombok.Data;

/**
 * 用于外部的档案查询接口
 *
 * <AUTHOR>
 * @date 2024/8/13 14:46
 **/
@Data
public class ExternalArcQueryModel {

    /**
     * 认证账号 (包含固网，移动网，固定ip)
     */
    private String authAccount;

    /**
     * 虚拟账号
     */
    private String virtualAccount;

    /**
     * 协议类型 101, 103
     */
    private String dataType;

    /**
     * 子协议类型 1030001 等
     */
    private String childType;

    /**
     * 档案类型, 多个以逗号进行分隔
     */
    private String arcTypes;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用类型
     */
    private String appType;
}
