package com.semptian.archives.web.service.service;

/**
 * ES数据操作服务接口
 * <AUTHOR>
 * @date 2024/1/09
 */
public interface EsDataOperateService {

    /**
     * 新增es数据
     * @param index 索引名
     * @param object 新增数据实体类
     */
    boolean add(String index, Object object);


    /**
     * 根据id更新索引数据
     * @param index 索引名
     * @param field id字段
     * @param id id字段值
     * @param object 更新数据实体类
     * @return
     */
    boolean updateDataById(String index, String field, String id, Object object);
}
