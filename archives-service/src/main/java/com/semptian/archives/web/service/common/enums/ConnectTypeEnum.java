package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 *
 * 通联关系类型枚举类
 * <AUTHOR>
 * @since 2024/5/31
 */
@Getter
public enum ConnectTypeEnum {

    /**
     * 无实际意义枚举, 通联关系清空时使用，需要将已有通联关系清空
     */
    NON("-1", "无", "Non"),

    COLLEAGUE("1", "同事", "Collègue"),

    FRIEND("2", "朋友", "Ami"),

    PARENT("3", "亲人", "Parent"),

    COUPLE("4", "夫妻", "Époux"),

    OTHER("5", "其他", "Autres");

    private final String value;

    private final String descZh;

    private final String descFr;

    ConnectTypeEnum(String value, String descZh, String descFr) {
        this.value = value;
        this.descZh = descZh;
        this.descFr = descFr;
    }

    public static ConnectTypeEnum getConnectTypeEnumByValue(String value) {
        for (ConnectTypeEnum connectTypeEnum : ConnectTypeEnum.values()) {
            if (connectTypeEnum.getValue().equals(value)) {
                return connectTypeEnum;
            }
        }
        return null;
    }
}