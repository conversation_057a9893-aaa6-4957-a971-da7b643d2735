package com.semptian.archives.web.service.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 节点信息
 * <AUTHOR>
 * @Date 2021/6/15
 */

@Data
public class RelationNodeModel implements Serializable {
    /**
     * 档案id
     */
    private String id;

    /**
     * 档案类型
     */
    private String type;

    /**
     * 档案名称
     */
    private String name;

    /**
     * 认证账户
     */
    private String authAccount;

    /**
     * 虚拟账号
     */
    private String account;

    /**
     * 虚拟账号应用类型
     */
    private String virtualAccountAppType;
}
