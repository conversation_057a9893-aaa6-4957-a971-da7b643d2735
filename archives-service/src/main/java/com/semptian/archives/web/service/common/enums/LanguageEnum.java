package com.semptian.archives.web.service.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 语种枚举类
 *
 * <AUTHOR>
 * @date 2024/4/3
 */

@Getter
public enum LanguageEnum {

    ZH_CN("zh_CN"),

    FR_DZ("fr_DZ"),

    EN_US("en_US"),

    AR_DZ("ar_DZ");

    private final String lang;

    LanguageEnum(String lang) {
        this.lang = lang;
    }

    /**
     * 获取标准化的语种值
     * @param lang 语种
     * @return 标准化语种值
     */
    public static String getNormalizedLang(String lang) {
        if (StringUtils.isNotBlank(lang)) {
            if (lang.toLowerCase().contains("zh")) {
                lang = ZH_CN.getLang();
            } else if (lang.toLowerCase().contains("fr")) {
                lang = FR_DZ.getLang();
            } else if (lang.toLowerCase().contains("en")) {
                lang = EN_US.getLang();
            } else if (lang.toLowerCase().contains("ar")) {
                lang = AR_DZ.getLang();
            } else {
                lang = ZH_CN.getLang();
            }
        } else {
            lang = ZH_CN.getLang();
        }

        return lang;
    }
}
