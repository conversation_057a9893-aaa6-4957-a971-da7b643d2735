package com.semptian.archives.web.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: SunQi
 * @create: 2021/07/28
 * desc: 查询数据明细模型
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryDataDetailModel {
    /**
     * 协议类型
     */
    private String dataType;
    /**
     * 认证账号
     */
    private String authAccount;
    /**
     * 源虚拟账号
     */
    private String virtualAccount;
    /**
     * 目标虚拟账号
     */
    private String targetAccount;
    /**
     * 源电话号码
     */
    private String srcNumber;
    /**
     * 目标电话号码
     */
    private String dstNumber;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用类型
     */
    private String appType;

    /**
     * 网站名称
     */
    private String domain;

    /**
     * 源ip
     */
    private String srcIp;

    /**
     * 目标ip
     */
    private String dstIp;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 捕获开始时间
     */
    private Long startTime;
    /**
     * 捕获结束时间
     */
    private Long endTime;
    /**
     * 页码
     */
    private Integer onPage;
    /**
     * 大小
     */
    private Integer size;

    private String lang;

    /**
     * 是否只展示有值的列  0=是 1=否  默认为1
     */
    private Integer onlyShowValue;

    /**
     * 是否是用户在定制了结果列后发起的请求，如果是则不能走redis缓存
     */
    private Boolean isUpdateColumn;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 通联区域
     */
    private String connectArea;

    /**
     * 访问者所在国家
     */
    private String srcCountry;

    /**
     * 通联类型   1=电话、短信、传真
     */
    private String callTag;

    /**
     * 下钻编码
     */
    private String drillDownCode;

    /**
     * 固定Ip账号
     */
    private String fixedIpAccount;

    /**
     * 建档时间
     */
    private String createDay;

    public QueryDataDetailModel(QueryDataDetailModel queryDataDetailModelORIG) {
        this.dataType = queryDataDetailModelORIG.dataType;
        this.authAccount = queryDataDetailModelORIG.authAccount;
        this.virtualAccount = queryDataDetailModelORIG.virtualAccount;
        this.targetAccount = queryDataDetailModelORIG.targetAccount;
        this.srcNumber = queryDataDetailModelORIG.srcNumber;
        this.dstNumber = queryDataDetailModelORIG.dstNumber;
        this.appName = queryDataDetailModelORIG.appName;
        this.appType = queryDataDetailModelORIG.appType;
        this.domain = queryDataDetailModelORIG.domain;
        this.srcIp = queryDataDetailModelORIG.srcIp;
        this.dstIp = queryDataDetailModelORIG.dstIp;
        this.ip = queryDataDetailModelORIG.ip;
        this.startTime = queryDataDetailModelORIG.startTime;
        this.endTime = queryDataDetailModelORIG.endTime;
        this.onPage = queryDataDetailModelORIG.onPage;
        this.size = queryDataDetailModelORIG.size;
        this.lang = queryDataDetailModelORIG.lang;
        this.onlyShowValue = queryDataDetailModelORIG.onlyShowValue;
        this.isUpdateColumn = queryDataDetailModelORIG.isUpdateColumn;
        this.fileId = queryDataDetailModelORIG.fileId;
        this.connectArea = queryDataDetailModelORIG.connectArea;
        this.srcCountry = queryDataDetailModelORIG.srcCountry;
        this.callTag = queryDataDetailModelORIG.callTag;
        this.drillDownCode = queryDataDetailModelORIG.drillDownCode;
        this.fixedIpAccount = queryDataDetailModelORIG.fixedIpAccount;
        this.createDay = queryDataDetailModelORIG.createDay;
    }
}
