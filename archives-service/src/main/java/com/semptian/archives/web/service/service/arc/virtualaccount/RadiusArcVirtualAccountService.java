package com.semptian.archives.web.service.service.arc.virtualaccount;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.AuthTypeEnum;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class RadiusArcVirtualAccountService extends AbstractArcVirtualAccountService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.RADIUS;
    }

    @Override
    protected String countDataTypeTemplate(ArcContext ctx) {
        return BusinessCodeEnum.RADIUS_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE.getValue();
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        params.put("auth_account", ctx.getArcAccount());

        params.put("auth_account_type", AuthTypeEnum.RADIUS.getType());

        if (StringUtils.isNotEmpty(ctx.getKeyWord())) {
            params.put("key_word", " AND  lower(virtual_account) LIKE '%" + ctx.getKeyWord().toLowerCase() + "%' ");
        } else {
            params.put("key_word", "");
        }
    }
}
