package com.semptian.archives.web.service.common.enums;

/**
 * 关联信息枚举
 */
public enum RelationTypeEnum {

    RELATION(1, "relate"),

    LINK(2, "link"),

    GROUP(3,"group");

    private int key;

    private String value;

    RelationTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(int key) {
        RelationTypeEnum[] values = RelationTypeEnum.values();
        for (RelationTypeEnum value : values) {
            if (value.getKey() == key) {
                return value.getValue();
            }
        }
        return "";
    }

    public static void main(String[] args) {
        String byKey = RelationTypeEnum.getByKey(1);
        System.out.println(byKey);
    }
}
