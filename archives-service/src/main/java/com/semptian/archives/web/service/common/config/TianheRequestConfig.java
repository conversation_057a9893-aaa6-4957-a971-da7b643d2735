package com.semptian.archives.web.service.common.config;

import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.service.model.AdvancedSearchParamModel;
import com.semptian.base.model.TianheRequestModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Configuration
public class TianheRequestConfig {

    @Value("${tianhe.data.request.searchIdsUri:/searchDetail}")
    private String searchIdsUri;


    @Value("${tianhe.data.request.searchIdsUri:/advanced_search}")
    private String advanceSearchUri;

    private String aggregateUri = "/aggregate";
    private String collectionDataSearchUri = "/dataCollection_search";
    private String scrollSearchUri = "/scroll_search";

    private String searchByIdsWithoutQueryEsSearchUri = "/search_by_ids_without_query_es";

    private String getAnalyse = "/get_analyse";

    public TianheRequestModel getSearchIdsRequestModel(String resourceNames, String ids, String excludeQualifiers) {
        TianheRequestModel tianheRequestModel = new TianheRequestModel();
        tianheRequestModel.setUri(this.searchIdsUri);
        tianheRequestModel.setMethods("POST");
        JSONObject params = new JSONObject();
        params.put(resourceNames, ids);
        if (excludeQualifiers != null) {
            params.put("excludeQualifiers", excludeQualifiers);
        }
        tianheRequestModel.setParams(params);
        return tianheRequestModel;
    }

    public TianheRequestModel getSearchIdsRequestModel(JSONObject params) {
        TianheRequestModel tianheRequestModel = new TianheRequestModel();
        tianheRequestModel.setUri(this.searchIdsUri);
        tianheRequestModel.setMethods("POST");
        tianheRequestModel.setParams(params);
        return tianheRequestModel;
    }

    public TianheRequestModel getAdvancedSearchRequestModel(AdvancedSearchParamModel advancedSearchParamModel) {
        TianheRequestModel tianheRequestModel = new TianheRequestModel();
        tianheRequestModel.setUri(this.advanceSearchUri);
        tianheRequestModel.setMethods("POST");
        tianheRequestModel.setParams(advancedSearchParamModel);
        return tianheRequestModel;
    }



    public TianheRequestModel getSearchIdsWithoutQueryEsRequestModel(Set<String> resourceNames, String[] ids, String excludeQualifiers) {
        TianheRequestModel tianheRequestModel = new TianheRequestModel();
        tianheRequestModel.setUri(this.searchByIdsWithoutQueryEsSearchUri);
        tianheRequestModel.setMethods("POST");
        JSONObject params = new JSONObject();
        params.put("ids", ids);
        params.put("resourceNames", resourceNames);
        if (excludeQualifiers != null) {
            params.put("excludeQualifiers", excludeQualifiers);
        }
        tianheRequestModel.setParams(params);
        return tianheRequestModel;
    }


    public TianheRequestModel getAnalyseRequestModel(JSONObject params) {
        TianheRequestModel tianheRequestModel = new TianheRequestModel();
        tianheRequestModel.setUri(this.getAnalyse);
        tianheRequestModel.setMethods("POST");
        tianheRequestModel.setParams(params);
        return tianheRequestModel;
    }
}
