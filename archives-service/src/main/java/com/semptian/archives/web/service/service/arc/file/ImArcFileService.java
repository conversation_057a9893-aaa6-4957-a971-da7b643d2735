package com.semptian.archives.web.service.service.arc.file;

import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class ImArcFileService extends AbstractArcFileService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.IM;
    }

    @Override
    protected void buildAccountParams(CommonParamUtil.ParamMap params, ArcContext ctx) {
        String account;
        if (StringUtils.isNotBlank(ctx.getArcAccountType())) {
            account = " virtual_app_type ='" + ctx.getArcAccountType() + "' and virtual_account";
        } else {
            account = "virtual_app_type = '' and  virtual_account";
        }
        params.put("account_type_condition", account);

        params.put("dataType", " AND data_type = " + DataTypeEnum.IM.getKey() + " ");
    }

    @Override
    protected void buildKeywordCondition(CommonParamUtil.ParamMap params, String keyword) {
        params.put("keyword", " AND (lower(attach_name) LIKE CONCAT('%','" + keyword + "','%') OR  " +
                "lower(auth_account) LIKE CONCAT('%','" + keyword + "','%') )");
    }
}
