package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RadiusAppTimeListModel implements Comparable<RadiusAppTimeListModel> {
    private int time;
    private List<RadiusAppTimeModel> detail;

    @Override
    public int compareTo(RadiusAppTimeListModel o) {
        return o.time - this.time;
    }
}
