package com.semptian.archives.web.service.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.service.fegin.AdsFeignClient;
import com.semptian.archives.web.service.service.DimensionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class DimensionServiceImpl implements DimensionService {

    @Resource
    private AdsFeignClient dimensionFeign;

    @Override
    public Map<String, String> queryIpAddress(List<String> ips, String lang) {
        Map<String, String> res = new HashMap<>(ips.size());
        JSONObject req = new JSONObject();

        if (lang.contains("zh")) {
            lang = "zh-CN";
        }
        req.put("ipList", ips);
        req.put("languageType", lang);

        JSONObject ipResponse = dimensionFeign.queryIpDetail(req.toJSONString());
        if (ipResponse.getInteger("code") == 1) {
            JSONArray ipData = ipResponse.getJSONArray("data");
            ipData.forEach(d -> {
                JSONObject data = (JSONObject) d;
                String ip = data.getString("ip");
                String address = "";
                String country = data.getString("countryNames");
                String subdivisionNames = data.getString("subdivisionNames");
                String city = data.getString("cityNames");
                if (country != null) {
                    address += country;
                }

                if (subdivisionNames != null) {
                    address += subdivisionNames;
                }

                if (city != null) {
                    address += city;
                }

                res.put(ip, address);
            });
        }

        return res;
    }
}
