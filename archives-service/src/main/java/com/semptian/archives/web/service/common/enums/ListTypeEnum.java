package com.semptian.archives.web.service.common.enums;

public enum ListTypeEnum {

    RADIUS("RADIUS",1,"applicationAnalysisList"),
    VIRTUAL("VIRTUAL",2,"detailDataList"),
    EMAIL("EMAIL",6,"detailDataList"),
    IM("IM",7,"detailDataList"),
    WEB_SITE("WEB_SITE",3,"siteVisitorList"),
    APP("APP",4,"applicationVisitorList"),
    PHONE("PHONE",5,"associatedAccountList"),
    FIXED_IP("FIXED_IP",8,"applicationAnalysisList");

    private String key;

    private Integer value;

    private String description;

    ListTypeEnum(String key, Integer value, String description) {
        this.key = key;
        this.value = value;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

}
