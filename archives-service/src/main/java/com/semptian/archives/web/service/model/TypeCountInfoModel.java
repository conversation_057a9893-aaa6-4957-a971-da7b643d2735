package com.semptian.archives.web.service.model;

import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/21 16:42
 **/
@Data
public class TypeCountInfoModel {

    /**
     * 总数
     */
    private Integer total = 0;

    /**
     * email 通联次数
     */
    private Integer emailCount = 0;

    /**
     * 通话次数
     */
    private Integer callCount = 0;

    /**
     * 短信次数
     */
    private Integer msgCount = 0;

    /**
     * 传真次数
     */
    private Integer faxCount = 0;

    /**
     * 构建
     * @return
     */
    public Map<String, Object> buildTypeCountInfo () {
        Map<String, Object> typeCountInfo = new HashMap<>();

        if (emailCount != 0) {
            typeCountInfo.put("EMAIL", emailCount);
        }

        if (callCount != 0) {
            typeCountInfo.put("CALL", callCount);
        }

        if (msgCount != 0) {
            typeCountInfo.put("MESSAGE", msgCount);
        }

        if (faxCount != 0) {
            typeCountInfo.put("FAX", faxCount);
        }

        total = emailCount + callCount + msgCount + faxCount;
        return typeCountInfo;
    }


    /**
     * 构建
     * @return
     */
    public Map<String, Object> buildTypeCountInfo (String dataType) {
        Map<String, Object> typeCountInfo = new HashMap<>();

        if (emailCount != 0 && DataTypeEnum.EMAIL.getKey().equals(dataType)) {
            typeCountInfo.put("EMAIL", emailCount);
        }

        if (emailCount != 0 && DataTypeEnum.IM.getKey().equals(dataType)) {
            typeCountInfo.put("IM", emailCount);
        }

        if (callCount != 0) {
            typeCountInfo.put("CALL", callCount);
        }

        if (msgCount != 0) {
            typeCountInfo.put("MESSAGE", msgCount);
        }

        if (faxCount != 0) {
            typeCountInfo.put("FAX", faxCount);
        }

        total = emailCount + callCount + msgCount + faxCount;
        return typeCountInfo;
    }

    /**
     * 追加 typeCountInfo
     * @param newTypeCountInfoModel
     * @return
     */
    public TypeCountInfoModel addTypeCountInfoModel(TypeCountInfoModel newTypeCountInfoModel) {
        emailCount = this.getEmailCount() + newTypeCountInfoModel.getEmailCount();
        callCount = this.getCallCount() + newTypeCountInfoModel.getCallCount();
        msgCount = this.getMsgCount() + newTypeCountInfoModel.getMsgCount();
        faxCount = this.getFaxCount() + newTypeCountInfoModel.getFaxCount();

        return this;
    }
}
