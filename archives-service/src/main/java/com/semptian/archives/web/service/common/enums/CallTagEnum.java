package com.semptian.archives.web.service.common.enums;

/**
 * @Description 图库call_tag枚举
 * <AUTHOR>
 * @Date 2021/6/16
 */
public enum CallTagEnum {

    FAX("1", "FAX"),

    PHONE("2", "PHONE"),

    MESSAGE("3", "MESSAGE"),

    VOIP("0", "VOIP");

    private String key;

    private String value;

    CallTagEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getByKey(String key) {
        CallTagEnum[] values = CallTagEnum.values();
        for (CallTagEnum value : values) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return "";
    }
}
