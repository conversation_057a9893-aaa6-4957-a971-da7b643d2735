package com.semptian.archives.web.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 活跃趋势模型
 * <AUTHOR>
 * @Date 2021/06/13
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActiveTrendModel implements Comparable{

    /**
     * 日期/月份
     */
    private long time;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        ActiveTrendModel virtualAccountCountModel = (ActiveTrendModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
