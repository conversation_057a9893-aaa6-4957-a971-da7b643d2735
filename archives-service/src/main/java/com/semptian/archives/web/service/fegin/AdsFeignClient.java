package com.semptian.archives.web.service.fegin;

import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.service.model.AdsModel;
import com.semptian.base.service.ReturnModel;
import feign.Headers;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "deye-dimension-service",path = "/ads_dimension")
public interface AdsFeignClient {
    /**
     * Feign调用ADS服务: 查询所有通联地址
     *
     * @return ReturnModel
     */
    @RequestMapping(value = "/dic/page_country_code.json", method = RequestMethod.POST, consumes = "application/json")
    ReturnModel getCountryList(@RequestBody AdsModel params);

    /**
     * 根据IP地址查询对应的地区详细信息
     */
    @Headers("Content-Type: application-json")
    @PostMapping("/ip_warehouse/detail_by_ipList.json")
    JSONObject queryIpDetail(String info);

    /**
     * 根据报文地址地址，协议报文解析
     */
    @GetMapping("/packageAnalysis/package_analysis.json")
    ReturnModel getAttachPath(@RequestParam("filePath") String filePath,@RequestParam("messageId") String messageId,@RequestParam("nestedParse") String nestedParse,@RequestParam("returnNestedParseResult") String returnNestedParseResult);

    /**
     * 根据报附件名称和id，下载附件
     */
    @GetMapping("/commonFile/attach/down.json")
    Response downloadAttach(@RequestParam("fileId") String fileId, @RequestParam("attachName") String attachName);

    /**
     * 根据hbase表名和fileid，查询附件file_path
     */
    @GetMapping("/commonFile/attach/query.json")
    ReturnModel getPath( @RequestParam("fileId") String dataId);

    /**
     * Feign调用ADS服务: 查询所有上报地信息
     *
     * @param  params AdsModel
     * @return ReturnModel
     */
    @RequestMapping(value = "/dic/page_uparea.json", method = RequestMethod.POST, consumes = "application/json")
    ReturnModel getUpAreaList(AdsModel params);

    /**
     * Feign调用ADS服务: 查询所有协议
     *
     * @param  params AdsModel
     * @return ReturnModel
     */
    @RequestMapping(value = "/dic/page_protocol_type.json", method = RequestMethod.POST, consumes = "application/json")
    ReturnModel getProtocolList(AdsModel params);

    /**
     * 协议报文解析
     *
     * @param filePath
     * @param fileName
     * @param charsetCode
     * @return
     */
    @GetMapping("/packageAnalysis/package_analysis.json")
    ReturnModel packageAnalysis(@RequestParam("filePath") String filePath,
                                @RequestParam("fileName") String fileName,
                                @RequestParam("charsetCode") Integer charsetCode,
                                @RequestParam("messageId") String messageId,
                                @RequestParam("nestedParse") Boolean nestedParse,
                                @RequestParam("returnNestedParseResult") Boolean returnNestedParseResult)
    ;
}
