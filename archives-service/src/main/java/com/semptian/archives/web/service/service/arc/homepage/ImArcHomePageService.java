package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.ArchiveModel;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ImArcHomePageService extends AbstractArcHomePageService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.IM;
    }

    @Override
    protected Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("imAccount", authAccounts);

        if (StringUtils.isEmpty(appTypes)) {
            params.put("app_type", "");
        } else {
            params.put("app_type", "and virtual_app_type in ("  + appTypes + " )");

        }

        params.put("dataType", " AND data_type = " + DataTypeEnum.IM.getKey() + " ");

        return params;
    }

    @Override
    protected Map<String, String> buildArcInfoMap(List<ArchiveModel> archiveModels) {
        return archiveModels.stream().collect(Collectors.toMap(arc -> arc.getArcAccount() + arc.getArcAccountType(), ArchiveModel::getArcId));
    }

    @Override
    protected Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        Map<String, String> past7Day = DateUtils.getPast7Day();

        if (ctx.getDateModel().getStartDay() != null) {
            past7Day.put("start_day", ctx.getDateModel().getStartDay());
        }

        if (ctx.getDateModel().getEndDay() != null) {
            past7Day.put("end_day", ctx.getDateModel().getEndDay());
        }
        return new HashMap<>(past7Day);
    }

    @Override
    protected String behaviorKey(Integer behaviorType) {
        return "pr";
    }

    @Override
    public void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.put("imAccount", EscapeUtil.escapeSingleQuote(ctx.getArcAccount()));
        if (StringUtils.isEmpty(ctx.getArcAccountType())) {
            params.put("app_type", "");
        } else {
            params.put("app_type", "and virtual_app_type = " + "'" + EscapeUtil.escapeSingleQuote(ctx.getArcAccountType()) + "'");
        }

        params.put("dataType", " AND data_type = " + DataTypeEnum.IM.getKey() + " ");
    }

    @Override
    public String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {

        return BusinessCodeEnum.IM_TOP_COMMUNICATE_ACCOUNT.getValue();
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        String arcAccountType = ctx.getArcAccountType();
        if (StringUtils.isNotBlank(arcAccountType)) {
            params.put("app_type", " AND virtual_app_type = " + arcAccountType + " ");
        } else {
            params.put("app_type", "");
        }

        if (StringUtils.isNotEmpty(ctx.getTargetAccount())) {
            params.put("target_account", " AND target_account = '" + ctx.getTargetAccount() + "'");
            params.put("lis_target_condition", " AND (( virtual_account = '" + ctx.getArcAccount() + "' AND target_account = '" + ctx.getTargetAccount() + "') OR ( target_account = '" + ctx.getArcAccount() + "' AND virtual_account = '" + ctx.getTargetAccount() + "'))");
        } else {
            params.put("target_account", "");
            params.put("lis_target_condition", "");
        }
    }

    @Override
    public String activeTrendTemplate(ArcContext ctx) {
        if (StringUtils.isNotEmpty(ctx.getTargetAccount())) {
          return BusinessCodeEnum.COMMON_IM_LIS_DAY_TREND_ANALYZE.getValue();
        }

        return BusinessCodeEnum.COMMON_IM_DAY_TREND_ANALYZE.getValue();
    }

    @Override
    public String periodStatisticsTemplate() {
        return BusinessCodeEnum.COMMON_IM_DAY_HOUR_ANALYZE.getValue();
    }

}
