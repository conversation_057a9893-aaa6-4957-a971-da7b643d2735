package com.semptian.archives.web.service.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/10/31
 */

@Data
@EqualsAndHashCode
public class ArcInfoModel {

    /**
     * 档案id
     */
    private String id;

    /**
     * 档案id
     */
    private String arcId;

    /**
     * 档案类型:1=radius、2=email、3=website、4=app、5=phone、6=im、7=protocol、8=fixed_ip
     */

    private Integer archivesType;

    /**
     * 档案类型:1=radius、2=email、3=website、4=app、5=phone、6=im、7=protocol、8=fixed_ip
     */

    private Integer arcType;

    /**
     * 档案账号
     */
    private String arcAccount;

    /**
     * 档案账号类型，对于radius=1020001、固定IP档案=1029997
     */
    private String arcAccountType;

    /**
     * 档案名称
     */
    private String name;

    /**
     * Radius账号
     */
    private String radius;

    /**
     * 电话号码
     */
    private String telNumber;

    /**
     * imsi
     */
    private String imsi;

    /**
     * imei
     */
    private String imei;

    /**
     * ip
     */
    private String ip;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用类型
     */
    private String appType;

    /**
     * 域名
     */
    private String domainName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 档案图像
     */
    private String photoUrl;

    /**
     * 活跃总次数
     */
    private Long activeNum;

    /**
     * 活跃度
     */
    private Double activeRate;

    /**
     * 最后关联时间
     */
    private Long lastRelationTime;

    /**
     * 最后关联国家
     */
    private String lastRelationCountry;

    /**
     * 最后关联城市
     */
    private String lastRelationCity;

    /**
     * 是否关注,当前版本统一为0
     */
    private Integer isCare = 0;

    /**
     * 固定IP档案账号实体类型：1=个人 2=政企
     */
    private String accountType;

    /**
     * 号码归属地(号码档案特有)
     */
    private String attribution;

    /**
     * 收藏时间
     */
    private Long collectionTime;

    /**
     * 昵称
     */
    private String nickName;
}
