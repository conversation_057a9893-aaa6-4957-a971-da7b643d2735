package com.semptian.archives.web.service.service.arc.ip;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AppArcServerIpService extends AbstractArcServerIpService {

    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.APP;
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        params.put("app_name", EscapeUtil.escapeSingleQuote(ctx.getAppName()));

        if (StringUtils.isNotEmpty(ctx.getAppType())) {
            params.put("app_type", "and app_type = " + "'" + EscapeUtil.escapeSingleQuote(ctx.getAppType()) + "'");
        } else {
            params.put("app_type", "");
        }
    }


    /**
     * @param ctx 查询上下文
     * @return 返回不同visitorType对应的查询模板
     */
    @Override
    public String serverIpListTemplate(ArcContext ctx) {

        return BusinessCodeEnum.APP_SERVER_IP_LIST.getValue();
    }
}
