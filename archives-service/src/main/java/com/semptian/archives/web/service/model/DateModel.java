package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用于接受前端传参，并且生成后端时间查询参数的模型
 *
 * <AUTHOR>
 * @date 2024/3/21 15:47
 **/
@ApiModel(value = "时间传参实体类")
@Data
public class DateModel {

    /**
     * 开始日期 (支持多种格式 详见 cn.hutool.core.date.DateUtil.parse)
     */
    @ApiModelProperty(value = "开始日期")
    String startDay;

    /**
     * 结束日期 (支持多种格式 详见 cn.hutool.core.date.DateUtil.parse)
     */
    @ApiModelProperty(value = "结束日期")
    String endDay;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始月份")
    String startMonth;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束月份")
    String endMonth;

    @ApiModelProperty(value = "开始时间戳")
    String startTime;

    @ApiModelProperty(value = "结束时间戳")
    String endTime;

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption;

    public DateModel(String startDay, String endDay, Integer dateOption) {
        this.startDay = startDay;
        this.endDay = endDay;
        this.dateOption = dateOption;
    }

    public DateModel() {
    }
}
