package com.semptian.archives.web.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 查询关联关系返回模型
 * <AUTHOR>
 * @Date 2021/6/19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelationDetailModel {

    /**
     * 源虚拟账号
     */
    private String srcVirtualAccount;

    /**
     * 目标虚拟账号
     */
    private String targetVirtualAccount;

    /**
     * 关系类型
     */
    private String relationType;

    /**
     * 统计
     */
    private Integer relationNumber;

    /**
     * 最早关联时间
     */
    private long earliestTime;

    /**
     * 最近关系时间
     */
    private long latestTime;

    /**
     * 协议类型 : EMAIL/CALL/VOIP
     */
    private String dataTypeString;
}
