package com.semptian.archives.web.service.service.arc.homepage;

import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class EmailArcHomePageService extends AbstractArcHomePageService {
    @Override
    public ArcTypeEnum arcType() {
        return ArcTypeEnum.EMAIL;
    }

    @Override
    protected Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("email", authAccounts);
        params.put("dataType", " AND data_type = " + DataTypeEnum.EMAIL.getKey() + " ");

        return params;
    }

    @Override
    protected Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {

        Map<String, String> past7Day = DateUtils.getLatest7Day();

        if (ctx.getDateModel().getStartDay() != null) {
            past7Day.put("start_day", ctx.getDateModel().getStartDay());
        }

        if (ctx.getDateModel().getEndDay() != null) {
            past7Day.put("end_day", ctx.getDateModel().getEndDay());
        }
        return new HashMap<>(past7Day);
    }

    @Override
    public void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {

    }

    @Override
    protected String behaviorKey(Integer behaviorType) {
        return "pr";
    }

    @Override
    public String activeTrendTemplate(ArcContext ctx) {
        return BusinessCodeEnum.COMMON_EMAIL_DAY_TREND_ANALYZE.getValue();
    }

    @Override
    public String periodStatisticsTemplate() {
        return BusinessCodeEnum.COMMON_EMAIL_DAY_HOUR_ANALYZE.getValue();
    }

    @Override
    public void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.put("email", EscapeUtil.escapeSingleQuote(ctx.getArcAccount()));
        params.put("dataType", " AND data_type = " + DataTypeEnum.EMAIL.getKey() + " ");
    }

    @Override
    public String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {

        return BusinessCodeEnum.EMAIL_TOP_EMAIL_ACCOUNT.getValue();

    }
}
