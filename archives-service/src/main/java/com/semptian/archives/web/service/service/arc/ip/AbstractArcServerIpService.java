package com.semptian.archives.web.service.service.arc.ip;

import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.service.common.enums.SortTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractArcServerIpService implements ArcServerIpService {

    @Resource
    private ArcCommonService arcCommonService;


    @Override
    public Map<String, Object> getServerIpList(ArcContext ctx) {
        //时间参数
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam(ctx.getDateModel());

        //构造档案特定的参数
        buildArcSpecifiedParams(params, ctx);

        //排序字段
        String orderType = SortTypeEnum.getNameByCode(ctx.getSortType());
        params.put(CommonConstent.ORDER_FIELD, ctx.getSortField() + " " + orderType );

        //获取doris sql模板
        String accessTargetListTemplate = serverIpListTemplate(ctx);

        List<Map<String, Object>> list = arcCommonService.getCommonServiceListResult(accessTargetListTemplate, params, true, ctx.getOnPage(), ctx.getSize());
        Long total = arcCommonService.getCommonServiceCountResult(accessTargetListTemplate, params, true);

        HashMap<String, Object> result = new HashMap<>();
        result.put("ipList", list);
        result.put("total", total);

        return result;
    }

    protected abstract void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx);

    protected abstract String serverIpListTemplate(ArcContext ctx);
}
