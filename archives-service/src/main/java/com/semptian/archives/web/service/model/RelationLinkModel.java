package com.semptian.archives.web.service.model;

import com.semptian.archives.web.service.common.enums.LinkTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Description 关联关系
 * <AUTHOR>
 * @Date 2021/6/15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelationLinkModel {

    /**
     * 开始节点id
     */
    private String startNodeId;

    /**
     * 开始节点账号
     */
    private String startAccount;

    /**
     * 结束节点id
     */
    private String endNodeId;

    /**
     * 结束节点账号
     */
    private String endAccount;

    /**
     * 人员关系类型
     */
    private String peopleRelationType;

    /**
     * 关系属性详情  例如  {"EMAIL": 391}
     */
    private Map typeCountInfo;

    /**
     * 总数
     */
    private Integer total = 0;

    /**
     * 关系类型：LinkTypeEnum, PHONE_LINK=号码通联关系,RADIUS_LINK=需要考虑RADIUS 的通联关系,EMAIL_LINK=邮箱通联关系,RELATE=认证账号和虚拟账号关联关系
     */
    private String type;

}
