package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2021/01/30
 * @Des 列定制模型
 */
@Data
@ApiModel("列定制模型")
public class CustomColumnsModel {
    
    @ApiModelProperty(value = "数据类型")
    private String dataType;
    
    @ApiModelProperty(value = "列配置列表")
    private LinkedList<ColumnConfig> columns;
    
    @Data
    @ApiModel("列配置")
    public static class ColumnConfig {
        
        @ApiModelProperty(value = "字段名")
        private String field;
        
        @ApiModelProperty(value = "是否可排序")
        private String sortable;
        
        @ApiModelProperty(value = "排序位置")
        private Integer sort;
        
        @ApiModelProperty(value = "标题")
        private String title;
        
        @ApiModelProperty(value = "是否可搜索")
        private Boolean searchable;
        
        @ApiModelProperty(value = "是否核心字段")
        private Integer isCore;

        @ApiModelProperty(value = "是否隐藏列")
        private Integer isShow;
    }
}