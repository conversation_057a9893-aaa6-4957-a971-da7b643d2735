package com.semptian.archives.web.service.service.arc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.semptian.archives.web.model.RadiusAppTimeListModel;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.BusinessTypeEnum;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.arc.accessor.ArcAccessorService;
import com.semptian.archives.web.service.service.arc.app.ArcAppAnalysisService;
import com.semptian.archives.web.service.service.arc.file.ArcFileService;
import com.semptian.archives.web.service.service.arc.homepage.ArcHomePageService;
import com.semptian.archives.web.service.service.arc.ip.ArcServerIpService;
import com.semptian.archives.web.service.service.arc.virtualaccount.ArcVirtualAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CommonArcServiceFacade {


    private final Table<ArcTypeEnum, ArcFeatures, ArcFunctional> ARC_FUNCTION_SERVICES = HashBasedTable.create();

    @Autowired
    public CommonArcServiceFacade(List<ArcFunctional> arcFunctionalList) {
        arcFunctionalList.forEach(arcFunctional -> {
            if (ARC_FUNCTION_SERVICES.contains(arcFunctional.arcType(), arcFunctional.feature())) {
                log.error("========重复注册了arcType:{}, feature:{}========", arcFunctional.arcType(), arcFunctional.feature());
            }

            ARC_FUNCTION_SERVICES.put(arcFunctional.arcType(), arcFunctional.feature(), arcFunctional);
        });
    }

    /*******************************档案概览开始************************************************/

    /**
     * 查询档案概览中的维度统计信息
     * @param arcType 档案类型
     * @param archiveModels 档案信息
     * @param isAll 是否仅查询维度的总数统计信息，0 = 同时查询近7天的数据，1 = 仅查询总数统计信息
     * @param index 统计维度。 1 = 行为次数，2 = 应用数， 3 = 通联区域数/访问区域数， 4 = 虚拟账号数， 5 = 阻断行为数， 6 = 文件个数， 7 = 阻断应用数
     *              8 = 通话次数， 9 = 传真次数， 10 = 短信条数， 11 = 重点目标访问数， 12 = 访问档案个数， 13 = 占用IP个数
     * @param userId 用户ID
     * @return 各维度对应的统计信息。默认包含近7天的数据
     */
    public Object getStatisticalDimensions(ArcTypeEnum arcType, List<ArchiveModel> archiveModels, int isAll, Integer index, String userId) {
        ArcHomePageService homePageService = (ArcHomePageService) ARC_FUNCTION_SERVICES.get(arcType, ArcFeatures.HOME_PAGE);

        return homePageService.getStatisticalDimensions(arcType, archiveModels, isAll, index, userId);
    }

    /*******************************档案概览结束************************************************/

    Object getAppStatisticsInfo(String arcId, String arcAccount, String arcAccountType, int isAll) {
        return null;
    }


    Object getStatisticalDimensions(List<ArchiveModel> archiveModels, int isAll, Integer index) {
        return null;
    }

    JSONArray getAppAccessTop(String appName, String appType, Integer topSize) {
        return null;
    }


    JSONArray getAppHeatMap(String appName, String appType, Long startTime, Long endTime) {
        return null;
    }

    /**
     * 获取首页访问者top
     *
     * @param arcType 档案类型
     * @param ctx 查询上下文
     * @param accessTargetTopEnum 访问者TOP枚举
     * @return 访问者top
     */
    public Map<String, Object> getAccessTargetTop(ArcTypeEnum arcType, ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        ArcHomePageService homePageService = (ArcHomePageService) ARC_FUNCTION_SERVICES.get(arcType, ArcFeatures.HOME_PAGE);
        ctx.setArcType(arcType.getKey());
        return homePageService.getAccessTargetTop(ctx, accessTargetTopEnum);
    }

    /**
     * 获取活跃趋势分析
     * @param ctx 查询上下文
     * @return 活跃趋势
     */
    public Map<String,List<ActiveTrendModel>> getActiveTrend(ArcContext ctx) {
        ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(ctx.getArcType());
        ArcHomePageService homePageService = (ArcHomePageService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.HOME_PAGE);
        return homePageService.getActiveTrend(ctx);
    }

    /**
     * 获取活跃时段分析
     * @param ctx 查询上下文
     * @return 时段分析
     */
    public Map<String, List<PeriodStatisticsModel>> getPeriodStatistics(ArcContext ctx) {
        ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(ctx.getArcType());
        ArcHomePageService homePageService = (ArcHomePageService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.HOME_PAGE);
        return homePageService.getPeriodStatistics(ctx);
    }

    /**
     * 获取通联区域
     * @param ctx 查询上下文
     * @return 通联区域
     */
    public CommunicationAreaStatisticsModel getCommunicationArea(ArcContext ctx) {
        ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(ctx.getArcType());
        ArcHomePageService homePageService = (ArcHomePageService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.HOME_PAGE);
        return homePageService.getCommunicationArea(ctx);
    }

    /**
     * 获取访问者列表
     *
     * @param arcType 档案类型
     * @param ctx 查询上下文
     * @return 访问者列表
     */
    public Map<String, Object> getAccessTargetList(ArcTypeEnum arcType, ArcContext ctx) {
        ArcAccessorService arcAccessorService = (ArcAccessorService) ARC_FUNCTION_SERVICES.get(arcType, ArcFeatures.ACCESSOR);
        ctx.setArcType(arcType.getKey());
        return arcAccessorService.getAccessTargetList(ctx);
    }

    /**
     * 获取档案服务器IP列表
     * @param arcType 档案类型
     * @param ctx 查询上下文
     * @return 档案服务器IP列表
     */
    public Map<String, Object> getServerIpList(ArcTypeEnum arcType, ArcContext ctx) {
        ArcServerIpService arcServerIpService = (ArcServerIpService) ARC_FUNCTION_SERVICES.get(arcType, ArcFeatures.SERVER_IP);
        ctx.setArcType(arcType.getKey());
        return arcServerIpService.getServerIpList(ctx);
    }

    /**
     * 获取文件信息
     * @param ctx 查询上下文
     * @return 文件信息列表
     */
    public Map<String, Object> getAttachInfo(ArcContext ctx, BusinessTypeEnum businessTypeEnum) {
        ArcTypeEnum arcTypeEnum = ArcTypeEnum.getArcTypeByKey(ctx.getArcType());
        ArcFileService arcFileService = (ArcFileService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.FILE_INFO);
        return arcFileService.getAttachInfo(ctx, businessTypeEnum);
    }

    /**
     * 查询应用分析 - 分布排名
     * */
    public Map<String, Object> getAppImgTable(ArcTypeEnum arcType, ArcContext ctx) {
        ArcAppAnalysisService arcServerIpService = (ArcAppAnalysisService) ARC_FUNCTION_SERVICES.get(arcType, ArcFeatures.APP_ANALYSIS);
        ctx.setArcType(arcType.getKey());
        return arcServerIpService.getAppImgTable(ctx);
    }

    public List<RadiusAppTimeListModel> getAppTimeInfo(ArcTypeEnum arcTypeEnum, ArcContext ctx) {
        ArcAppAnalysisService arcServerIpService = (ArcAppAnalysisService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.APP_ANALYSIS);
        ctx.setArcType(arcTypeEnum.getKey());
        return arcServerIpService.getAppTimeInfo(ctx);
    }

    public JSONObject getAppList(ArcTypeEnum arcTypeEnum, ArcContext ctx) {
        ArcAppAnalysisService arcServerIpService = (ArcAppAnalysisService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.APP_ANALYSIS);
        ctx.setArcType(arcTypeEnum.getKey());
        return arcServerIpService.getAppList(ctx);
    }

    public List<CountDataTypeModel> countDataType(ArcTypeEnum arcTypeEnum, ArcContext ctx) {
        ArcVirtualAccountService arcServerIpService = (ArcVirtualAccountService) ARC_FUNCTION_SERVICES.get(arcTypeEnum, ArcFeatures.VIRTUAL_ACCOUNT);
        ctx.setArcType(arcTypeEnum.getKey());
        return arcServerIpService.countDataType(ctx);
    }
}
