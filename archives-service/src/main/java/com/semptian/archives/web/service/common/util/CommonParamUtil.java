package com.semptian.archives.web.service.common.util;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.semptian.archives.web.service.common.enums.DateOptionEnum;
import com.semptian.archives.web.service.common.enums.TagDateOptionEnum;
import com.semptian.archives.web.service.model.DateModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * 公共参数构建类
 *
 * <AUTHOR>
 * @date 2021/8/5 18:07
 **/
@Component
public class CommonParamUtil {

    public static Integer defaultDaysInterval;

    private static String ADS_DB_NAME;

    private static String DWD_DB_NAME;

    private static String DWS_DB_NAME;

    @Value("${archive.common.commonDayInterval}")
    public void setDefaultDaysInterval(Integer defaultDaysInterval) {
        CommonParamUtil.defaultDaysInterval = defaultDaysInterval;
    }

    @Value("${archive.dbName.ads:ads}")
    public void setAdsDbName(String dbName) {
        CommonParamUtil.ADS_DB_NAME = dbName;
    }

    @Value("${archive.dbName.dwd:dwd}")
    public void setDwdDbName(String dbName) {
        CommonParamUtil.DWD_DB_NAME = dbName;
    }

    @Value("${archive.dbName.dws:dws}")
    public void setDwsDbName(String dbName) {
        CommonParamUtil.DWS_DB_NAME = dbName;
    }

    public static ParamMap buildCommonTimeParam() {
        ParamMap result = new ParamMap();

        result.put("ads", ADS_DB_NAME);
        result.put("dwd", DWD_DB_NAME);
        result.put("dws", DWS_DB_NAME);

        return result;
    }

    public static ParamMap buildCommonTimeParam(DateModel dateModel) {
        ParamMap result = buildCommonTimeParam();
        DateTime startDayDateTime = DateUtil.parse(dateModel.getStartDay());
        DateTime endDayDateTime = DateUtil.parse(dateModel.getEndDay());

        if (startDayDateTime != null && endDayDateTime != null) {
            result.put("start_day", startDayDateTime.toString("yyyy-MM-dd"));
            result.put("end_day", endDayDateTime.toString("yyyy-MM-dd"));

            if (StringUtils.isBlank(dateModel.getStartTime()) || StringUtils.isBlank(dateModel.getEndTime())) {
                result.put("start_time", startDayDateTime.getTime());
                result.put("end_time", endDayDateTime.getTime());
            } else {
                result.put("start_time", dateModel.getStartTime());
                result.put("end_time", dateModel.getEndTime());
            }
        }

        DateTime today = new DateTime(new Date());

        DateTime startDay = null;
        DateTime endDay = null;

        DateOptionEnum dateOptionEnum = DateOptionEnum.getDateOptionByValue(dateModel.getDateOption());

        switch (dateOptionEnum) {
            case LAST_7_DAYS:
                //近7天
                startDay = DateUtil.offsetDay(today, -6);
                endDay = today;
                break;
            case LAST_15_DAYS:
                //近15天
                startDay = DateUtil.offsetDay(today, -14);
                endDay = today;
                break;
            case CURRENT_MONTH:
                //本月
                startDay = DateUtil.beginOfMonth(today);
                endDay = today;
                break;
            case LAST_MONTH:
                //上月
                startDay = DateUtil.beginOfMonth(DateUtil.offsetMonth(today, -1));
                endDay = DateUtil.endOfMonth(DateUtil.offsetMonth(today, -1));
                break;
            case LAST_TWO_MONTHS:
                //近二月
                startDay = DateUtil.beginOfMonth(DateUtil.offsetMonth(today, -1));
                endDay = today;
                break;
            case PAST_7_DAYS:
                //过去7天
                startDay = DateUtil.offsetDay(today, -7);
                endDay = DateUtil.offsetDay(today, -1);
                break;
            case PAST_15_DAYS:
                //过去15天
                startDay = DateUtil.offsetDay(today, -15);
                endDay = DateUtil.offsetDay(today, -1);
                break;
            case CUSTOM:
                //自定义选项
                break;
            default:
                break;
        }

        if (startDay != null && endDay != null) {
            result.put("start_day", startDay.toString("yyyy-MM-dd"));
            result.put("end_day", endDay.toString("yyyy-MM-dd"));
        }

        return result;
    }


    public static ParamMap buildTagListParam(DateModel dateModel) {
        ParamMap result = buildCommonTimeParam();

        DateTime today = new DateTime(new Date());

        DateTime startDay = null;
        DateTime endDay = null;
        DateTime startMonth = null;
        DateTime endMonth = null;

        TagDateOptionEnum dateOptionEnum = TagDateOptionEnum.getDateOptionByValue(dateModel.getDateOption());

        switch (dateOptionEnum) {
            case LAST_7_DAYS:
                //过去7天
                startDay = DateUtil.offsetDay(today, -7);
                endDay = DateUtil.offsetDay(today, -1);
                break;
            case CURRENT_MONTH:
                //本月
                startMonth = today;
                endMonth = today;
                break;
            case LAST_MONTH:
                //上月
                startMonth = DateUtil.beginOfMonth(DateUtil.offsetMonth(today, -1));
                endMonth = DateUtil.endOfMonth(DateUtil.offsetMonth(today, -1));
                break;
            case LAST_THREE_MONTHS:
                //近三月
                startMonth = DateUtil.beginOfMonth(DateUtil.offsetMonth(today, -2));
                endMonth = today;
                break;
            case CUSTOM:
                if (StringUtils.isNotEmpty(dateModel.getStartMonth()) && StringUtils.isNotEmpty(dateModel.getEndMonth())) {
                    result.put("start_month", dateModel.getStartMonth().replaceAll("-",""));
                    result.put("end_month", dateModel.getEndMonth().replaceAll("-",""));
                }
                break;
            default:
                break;
        }

        if (startDay != null && endDay != null) {
            result.put("start_day", startDay.toString("yyyy-MM-dd"));
            result.put("end_day", endDay.toString("yyyy-MM-dd"));
        }
        if (startMonth != null) {
            result.put("start_month", Integer.parseInt(startMonth.toString("yyyyMM")));
            result.put("end_month", Integer.parseInt(endMonth.toString("yyyyMM")));
        }

        return result;
    }

    /**
     * 公共参数构建
     *
     * @param startDay 开始日期
     * @param endDay  结束日期
     * @return 公共参数
     */
    public static ParamMap buildCommonTimeParam(String startDay, String endDay) {
        ParamMap result = new ParamMap();
        result.put("start_day", startDay);
        result.put("end_day", endDay);

        result.put("ads", ADS_DB_NAME);
        result.put("dwd", DWD_DB_NAME);
        result.put("dws", DWS_DB_NAME);

        return result;
    }

    public static ParamMap buildCommonTimeParam(String startDay, String endDay, String startMonth, String endMonth) {
        ParamMap result = buildCommonTimeParam(startDay, endDay);
        result.put("start_month", startMonth);
        result.put("end_month", endMonth);

        return result;
    }

    public static class ParamMap extends HashMap<String, Object> {
        public String getStartDay() {
            return String.valueOf(this.get("start_day"));
        }

        public String getEndDay() {
            return String.valueOf(this.get("end_day"));
        }

        public String getStartMonth() {
            return String.valueOf(this.get("start_month"));
        }

        public String getEndMonth() {
            return String.valueOf(this.get("end_month"));
        }

    }
}
