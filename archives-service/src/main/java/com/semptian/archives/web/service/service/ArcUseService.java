package com.semptian.archives.web.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.archives.web.dao.archive.entity.ArcUseEntity;

import java.util.List;

/**
 * 档案使用表服务
 * */
public interface ArcUseService extends IService<ArcUseEntity> {

    /**
     * 更新用户的档案使用情况。
     *
     * 若之前未使用过，则新建使用记录。若使用过，则更新使用次数和最后访问时间
     * */
    void updateArcUser(Long userId, String arcId,Integer arcType,String arcAccount,String arcAccountType,String dataType);

    /**
     * 查询最近使用的档案TOP10
     * */
    List<ArcUseEntity> latestUseArcTop10(Long userId, Integer arcType,List<Integer> permissionList);

    /**
     * 查询指定用户最频繁使用的档案Top10
     * */
    List<ArcUseEntity> frequencyUseArcTop10(Long userId, Integer arcType,List<Integer> permissionList);
}
