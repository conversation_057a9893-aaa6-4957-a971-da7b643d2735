package com.semptian.archives.web.service.service.arc.homepage;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.util.EscapeUtil;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.common.DicTranslator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 档案概览抽象服务，用于给各个档案类型的概览服务提供公共方法以及定义各方法的模板
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public abstract class AbstractArcHomePageService implements ArcHomePageService {

    @Autowired
    private ArchivesInfoService archivesInfoService;

    @Resource
    private ArcCommonService arcCommonService;

    @Autowired
    private DicTranslator dicTranslator;

    @Resource
    private ArcDrillDownFieldTranslateConfigService arcDrillDownFieldTranslateConfigService;

    @Override
    public Object getStatisticalDimensions(ArcTypeEnum arcType, List<ArchiveModel> archiveModels, Integer isAll, Integer index, String userId) {

        List<String> authAccount = archiveModels.stream().map(arc -> "'" + arc.getArcAccount() + "'").collect(Collectors.toList());

        Set<String> appTypes = archiveModels.stream().filter(arc -> StringUtils.isNotEmpty(arc.getArcAccountType())).map(arc -> "'" + arc.getArcAccountType() + "'").collect(Collectors.toSet());

        // 档案账号与档案IP的映射关系
        Map<String, String> accountIdMaps = buildArcInfoMap(archiveModels);

        // 获取基础查询条件
        Map<String, Object> params = getStatisticDimensionParams();
        // 增加档案特定的查询条件
        params.putAll(arcStatisticDimensionParams(String.join(",", authAccount), appTypes.isEmpty() ? "" : String.join(",", appTypes)));

        // 1. 总数统计值
        List<BusinessCodeEnum> candidateBusinessCodesTotal = Arrays.stream(BusinessCodeEnum.values())
                .filter(code -> code.usedForArcType(arcType().getValue()))
                .filter(code -> code.usedForFunctional("dimension_statistics"))
                .filter(BusinessCodeEnum::usedForTotal)
                .filter(code -> code.usedForDimension(index))
                .collect(Collectors.toList());

        if (candidateBusinessCodesTotal.size() != 1) {
            log.info("Total BusinessCode Enum has redundant codes or has no codes. param is [{}]-[{}]-[{}]-[{}]", archiveModels, isAll, index, userId);
            throw new IllegalArgumentException("Total BusinessCode Enum has redundant codes or has no codes.");
        }

        List<Map<String, Object>> totalStatisticalValue = arcCommonService.getCommonServiceListResult(candidateBusinessCodesTotal.get(0).getValue(), params);

        List<ArchiveReturnModel> returnModels = new ArrayList<>();
        if (CollectionUtils.isEmpty(totalStatisticalValue)) {
            for (ArchiveModel arc : archiveModels) {
                ArchiveReturnModel arcReturnModel = new ArchiveReturnModel();
                arcReturnModel.setArcId(arc.getArcId());
                arcReturnModel.setArcAccount(arc.getArcAccount());
                arcReturnModel.setArcAccountType(arc.getArcAccountType());
                arcReturnModel.setTotal(0L);
                arcReturnModel.setOneWeek(0L);

                returnModels.add(arcReturnModel);
            }
        } else {
            returnModels = totalStatisticalValue.stream()
                    .map(map -> {
                        ArchiveReturnModel arcReturnModel = new ArchiveReturnModel();
                        String account = map.get("arcName") + "";
                        String appType = "";
                        if (map.containsKey("appType")) {
                            appType = map.get("appType") + "";
                        }
                        arcReturnModel.setArcId(accountIdMaps.getOrDefault(account + appType, ""));
                        arcReturnModel.setArcAccount(account);
                        arcReturnModel.setTotal(Long.valueOf(String.valueOf(map.getOrDefault("nums", "0"))));
                        arcReturnModel.setOneWeek(0L);

                        return arcReturnModel;
                    })
                    .collect(Collectors.toList());
        }

        // 2. 近7天统计值
        if (isAll == 0) {
            List<BusinessCodeEnum> candidateBusinessCodesOneWeek = Arrays.stream(BusinessCodeEnum.values())
                    .filter(code -> code.usedForArcType(arcType().getValue()))
                    .filter(code -> code.usedForFunctional("dimension_statistics"))
                    .filter(BusinessCodeEnum::usedForOneWeek)
                    .filter(code -> code.usedForDimension(index))
                    .collect(Collectors.toList());

            if (candidateBusinessCodesOneWeek.size() != 1) {
                throw new IllegalArgumentException("OneWeek BusinessCode Enum has redundant codes or has no codes.");
            }

            // archiveModels size 为 1 , 且档案类型为固定IP的情况下
            if (archiveModels.size() == 1 && ArcTypeEnum.FIXED_IP.getKey().equals(arcType.getKey())) {
                String createDay = archiveModels.get(0).getCreateDay();
                DateUtils.validateFixedIpStartDay(params, createDay);
            }

            List<Map<String, Object>> oneWeekStatisticalValues = arcCommonService.getCommonServiceListResult(candidateBusinessCodesOneWeek.get(0).getValue(), params);

            Map<String, Long> arcOneWeekValueMap = oneWeekStatisticalValues.stream()
                    .collect(Collectors.toMap(arcValue ->  {
                                String appType = "";
                                if (arcValue.containsKey("appType")) {
                                    appType = arcValue.get("appType") + "";
                                }
                                return arcValue.get("arcName") + appType;
                            },
                            (arcValue -> Long.parseLong(String.valueOf(arcValue.getOrDefault("nums", "0"))))));

            returnModels.forEach(model -> {

                AtomicReference<String> key = new AtomicReference<>(model.getArcAccount());

                accountIdMaps.forEach((k, v) -> {
                    if (v.equals(model.getArcId())) {
                        key.set(k);
                    }
                });

                if (arcOneWeekValueMap.containsKey(key.get())) {
                    if (model.getTotal() < arcOneWeekValueMap.get(key.get())) {
                        model.setOneWeek(model.getTotal());
                    } else {
                        model.setOneWeek(arcOneWeekValueMap.get(key.get()));
                    }
                }
            });
        }

        return returnModels;
    }

    /**
     * 用于构建档案的唯一标识与ID之间的对应关系，对于应用档案需要使用appName+appType构建唯一标识
     * */
    protected Map<String, String> buildArcInfoMap(List<ArchiveModel> archiveModels) {
        return archiveModels.stream().collect(Collectors.toMap(ArchiveModel::getArcAccount, ArchiveModel::getArcId));
    }

    /**
     * 在查询条件中增加档案特定的查询条件
     *
     * @param authAccounts 查询的目标账号，多账号时使用逗号进行分割
     * @return 返回档案特定的查询条件集合
     */
    protected abstract Map<String, Object> arcStatisticDimensionParams(String authAccounts, String appTypes);

    /**
     * 获取档案概览的维度统计查询参数
     */
    private Map<String, Object> getStatisticDimensionParams() {
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam();
        // 添加近7天的时间条件
        params.putAll(DateUtils.getLatest7Day());
        return params;
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Map<String, Object> getAccessTargetTop(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        //获取时间参数
        Map<String, Object> params = buildArcTimeRangeParams(ctx, accessTargetTopEnum);

        params.putAll(CommonParamUtil.buildCommonTimeParam());
        //构造档案特定的参数
        buildAccessTargetTopParams(params, ctx);

        //查询档案特定的模板语句
        String templateName = accessTargetTopTemplate(ctx, accessTargetTopEnum);
        List<Map<String, Object>> arcTargetMap = arcCommonService.getCommonServiceListResult(templateName, params, true, 1, ctx.getTopSize());

        if(CollectionUtils.isNotEmpty(arcTargetMap)){
            arcTargetMap.forEach(arcTarget -> {
                if (arcTarget.containsKey("dataType")) {
                    arcTarget.put("dataType", arcDrillDownFieldTranslateConfigService.fieldTranslate(CommonConstent.NORM_DATA_TYPE, String.valueOf(arcTarget.get("dataType")), ctx.getLang()).toString());
                }
            });
        }

        Map result = new HashMap();
        result.put(accessTargetTopEnum.getValue(), arcTargetMap);

        return result;
    }

    protected abstract Map<String, Object> buildArcTimeRangeParams(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum);

    @Override
    public Map<String,List<ActiveTrendModel>> getActiveTrend(ArcContext ctx) {
        Map<String,List<ActiveTrendModel>> result = new HashMap<>(4);
        Map<String, Object> params = getCommonParams(ctx);

        int originalBehaviorType = ctx.getBehaviorType();
        if (ArcTypeEnum.PHONE.getKey().equals(ctx.getArcType())) {
            params.put("behavior_type", 1);
            originalBehaviorType = 1;
        }
        //查询档案特定的模板语句
        String activeTrendTemplate = activeTrendTemplate(ctx);
        List<Map<String, Object>> activeTrendMap = arcCommonService.getCommonServiceListResult(activeTrendTemplate, params);

        List<ActiveTrendModel> activeTrendModelList = activeTrendMap.stream().map(map -> BeanUtil.toBeanIgnoreError(map, ActiveTrendModel.class)).collect(Collectors.toList());

        activeTrendModelList = formatActiveTrend(activeTrendModelList, ctx.getStartDay(), ctx.getEndDay());

        String behaviorKey = behaviorKey(originalBehaviorType);

        result.put(behaviorKey, activeTrendModelList);

        if (ArcTypeEnum.PHONE.getKey().equals(ctx.getArcType())) {
            params.put("behavior_type", ctx.getBehaviorType());
            activeTrendTemplate = activeTrendTemplate(ctx);
            activeTrendMap = arcCommonService.getCommonServiceListResult(activeTrendTemplate, params);
            activeTrendModelList = activeTrendMap.stream().map(map -> BeanUtil.toBeanIgnoreError(map, ActiveTrendModel.class)).collect(Collectors.toList());
            activeTrendModelList = formatActiveTrend(activeTrendModelList, ctx.getStartDay(), ctx.getEndDay());
            behaviorKey = behaviorKey(ctx.getBehaviorType());
            result.put(behaviorKey, activeTrendModelList);
        }

        return result;
    }

    /**
     * 默认返回nf
     * @param behaviorType 行为类型：1=通话行为，2=PR行为，3=NF行为，默认为3
     * @return 响应结果集对应的Key
     */
    protected String behaviorKey(Integer behaviorType) {
        return "nf";
    }

    private Map<String, Object> getCommonParams(ArcContext ctx) {
        //获取时间参数
        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam(ctx.getStartDay(), ctx.getEndDay());

        params.put("arc_account", EscapeUtil.escapeSingleQuote(ctx.getArcAccount()));
        params.put("behavior_type", ctx.getBehaviorType());

        params.putAll(CommonParamUtil.buildCommonTimeParam());

        //构造档案特定的参数
        buildArcSpecifiedParams(params, ctx);
        return params;
    }

    @Override
    public Map<String, List<PeriodStatisticsModel>> getPeriodStatistics(ArcContext ctx) {


        Map<String, List<PeriodStatisticsModel>> result = new HashMap<>(4);
        Map<String, Object> params = getCommonParams(ctx);

        int originalBehaviorType = ctx.getBehaviorType();
        if (ArcTypeEnum.PHONE.getKey().equals(ctx.getArcType())) {
            params.put("behavior_type", 1);
            originalBehaviorType = 1;
        }
        //查询档案特定的模板语句
        String periodStatisticsTemplate = periodStatisticsTemplate();
        List<Map<String, Object>> periodStatisticsMap = arcCommonService.getCommonServiceListResult(periodStatisticsTemplate, params);

        if (periodStatisticsMap == null) {
            return result;
        }

        List<PeriodStatisticsModel> periodStatisticsModelList = periodStatisticsMap.stream().map(map -> BeanUtil.toBeanIgnoreError(map, PeriodStatisticsModel.class)).collect(Collectors.toList());

        periodStatisticsModelList = formatPeriodStatisticsList(periodStatisticsModelList);

        String behaviorKey = behaviorKey(originalBehaviorType);

        result.put(behaviorKey, periodStatisticsModelList);

        if (ArcTypeEnum.PHONE.getKey().equals(ctx.getArcType())) {
            params.put("behavior_type", ctx.getBehaviorType());
            periodStatisticsTemplate = periodStatisticsTemplate();
            periodStatisticsMap = arcCommonService.getCommonServiceListResult(periodStatisticsTemplate, params);
            periodStatisticsModelList = periodStatisticsMap.stream().map(map -> BeanUtil.toBeanIgnoreError(map, PeriodStatisticsModel.class)).collect(Collectors.toList());
            periodStatisticsModelList = formatPeriodStatisticsList(periodStatisticsModelList);
            behaviorKey = behaviorKey(ctx.getBehaviorType());
            result.put(behaviorKey, periodStatisticsModelList);
        }

        return result;
    }

    @Override
    public CommunicationAreaStatisticsModel getCommunicationArea(ArcContext ctx) {
        Map<String, Object> params = getCommonParams(ctx);

        //查询档案特定的模板语句
        String communicationAreaTemplate = communicationAreaTemplate();
        List<Map<String, Object>> list = arcCommonService.getCommonServiceListResult(communicationAreaTemplate, params);

        return getCommunicationAreaMap(list, ctx.getArcType());
    }

    protected void buildAccessTargetTopParams(Map<String, Object> params, ArcContext ctx) {
        params.putAll(CommonParamUtil.buildCommonTimeParam());
    }

    protected void buildArcSpecifiedParams(Map<String, Object> params, ArcContext ctx) {
        throw new UnsupportedOperationException(arcType() + " not support build params method");
    }

    protected String accessTargetTopTemplate(ArcContext ctx, AccessTargetTopEnum accessTargetTopEnum) {
        throw new UnsupportedOperationException(arcType() + " not support doris sql template name query method");
    }

    protected String activeTrendTemplate(ArcContext ctx) {
        throw new UnsupportedOperationException(arcType() + " not support doris sql template name query method");
    }

    protected String periodStatisticsTemplate() {
        throw new UnsupportedOperationException(arcType() + " not support doris sql template name query method");
    }

    protected String communicationAreaTemplate() {
        throw new UnsupportedOperationException(arcType() + " not support doris sql template name query method");
    }

    /**
     * 给不存在的日期填充0
     *
     * @param activeTrendModelList activeTrendModelList
     * @param startDay             开始日期
     * @param endDay               结束日期
     * @return 填充0后的结果
     */
    private List<ActiveTrendModel> formatActiveTrend(List<ActiveTrendModel> activeTrendModelList, String startDay, String endDay) {

        List<ActiveTrendModel> newActiveTrendList = Lists.newArrayList();
        List<Integer> timeList = DateUtils.getDayList(startDay, endDay);

        Map<Integer, ActiveTrendModel> modelMaps = activeTrendModelList.stream().collect(Collectors.toMap(model -> Integer.valueOf(String.valueOf(model.getTime())), model -> model));

        for (Integer timePoint : timeList) {
            if (modelMaps.containsKey(timePoint)) {
                newActiveTrendList.add(modelMaps.get(timePoint));
            } else {
                newActiveTrendList.add(new ActiveTrendModel(timePoint, 0));
            }
        }
        return newActiveTrendList;
    }

    /**
     * 给不存在的时段填充0
     *
     * @param periodStatisticsModelList periodStatisticsModelList
     * @return 填充0后结果
     */
    private List<PeriodStatisticsModel> formatPeriodStatisticsList(List<PeriodStatisticsModel> periodStatisticsModelList) {


        HashSet<Integer> hourSet = new HashSet<>();
        for (PeriodStatisticsModel periodStatisticsModel : periodStatisticsModelList) {
            hourSet.add(periodStatisticsModel.getPeriod());
        }
        List<PeriodStatisticsModel> newPeriodStatisticsModelList = Lists.newArrayList();
        int index = 0;
        for (int i = 0; i < 24; i++) {
            if (hourSet.contains(i)) {
                newPeriodStatisticsModelList.add(periodStatisticsModelList.get(index++));
            } else {
                newPeriodStatisticsModelList.add(new PeriodStatisticsModel(i, 0));
            }
        }
        return newPeriodStatisticsModelList;
    }

    private CommunicationAreaStatisticsModel getCommunicationAreaMap(List<Map<String, Object>> list, String arcType) {
        CommunicationAreaStatisticsModel areaStatisticsModel = new CommunicationAreaStatisticsModel();

        /*//号码档案无需特殊处理
        if (arcType == Integer.parseInt(ArcTypeEnum.PHONE.getKey())) {
            resultMap.put("dst", list);
            return resultMap;
        }*/

        List<CommunicationAreaModel> srcList = Lists.newArrayList();
        List<CommunicationAreaModel> dstList = Lists.newArrayList();
        if (!list.isEmpty()) {
            Map<String, Long> srcMap = list.stream().filter(s -> s.containsKey("src_country") && s.get("src_country") != null && StringUtils.isNotBlank(s.get("src_country").toString())).collect(Collectors.groupingBy(s -> s.get("src_country").toString(), Collectors.summingLong(s -> Long.parseLong(s.get("num").toString()))));
            for (Map.Entry<String, Long> entry : srcMap.entrySet()) {
                CommunicationAreaModel communicationAreaModel = new CommunicationAreaModel();
                if (StringUtils.isBlank(entry.getKey())) {
                    communicationAreaModel.setName("");
                } else {
                    communicationAreaModel.setName(entry.getKey());

                }
                communicationAreaModel.setNum(entry.getValue());
                srcList.add(communicationAreaModel);
            }
            Map<String, Long> dstMap = list.stream().filter(s -> s.containsKey("dst_country") && s.get("dst_country") != null && StringUtils.isNotBlank(s.get("dst_country").toString())).collect(Collectors.groupingBy(s -> s.get("dst_country").toString(), Collectors.summingLong(s -> Long.parseLong(s.get("num").toString()))));
            for (Map.Entry<String, Long> entry : dstMap.entrySet()) {
                CommunicationAreaModel communicationAreaModel = new CommunicationAreaModel();
                if (StringUtils.isBlank(entry.getKey())) {
                    communicationAreaModel.setName("");
                } else {
                    communicationAreaModel.setName(entry.getKey());

                }
                communicationAreaModel.setNum(entry.getValue());
                dstList.add(communicationAreaModel);
            }
        }

        areaStatisticsModel.setSrc(srcList);
        areaStatisticsModel.setDst(dstList);
        return areaStatisticsModel;
    }
}
