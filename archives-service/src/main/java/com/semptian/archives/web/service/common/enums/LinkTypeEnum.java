package com.semptian.archives.web.service.common.enums;

import lombok.Getter;

/**
 * 通联关系类型枚举类
 *
 */
@Getter
public enum LinkTypeEnum {

    PHONE_LINK("PHONE_LINK", "号码通联关系"),

    EMAIL_LINK("EMAIL_LINK", "虚拟账号通联关系"),
    IM_LINK("IM_LINK", "虚拟账号通联关系"),

    RELATE("RELATE", "认证账号和虚拟账号关联关系"),

    // 后续可能考虑废弃
    RADIUS_LINK("RADIUS_LINK", "需要考虑RADIUS 的通联关系");

    private final String key;

    private final String value;

    LinkTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }
}
