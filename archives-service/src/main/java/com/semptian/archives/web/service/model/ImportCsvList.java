package com.semptian.archives.web.service.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ImportCsvList {

    private String method;
    private Integer choose;//1、导出当前页；2、导出选择内容；3、导出前5000条
    private Integer size; //当choose == 3时，才需要传size值
    private Integer listType;//列表种类：1、radius应用列表 2、虚拟档案数据明细列表 5、号码档案通联账号信息列表 4、应用档案访问者列表 3、网站档案访问者列表
    private List<Map<String,Object>> contentField;
}
