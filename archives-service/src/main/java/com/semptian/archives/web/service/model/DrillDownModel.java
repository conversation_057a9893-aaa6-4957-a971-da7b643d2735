package com.semptian.archives.web.service.model;

import com.semptian.archives.web.service.common.enums.DrillDownSceneEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 下钻模型
 *
 * <AUTHOR>
 * @date 2024/3/25 15:03
 **/
@ApiModel("明细下钻模型")
@Data
public class DrillDownModel {

    /**
     * 下钻场景
     */
    @ApiModelProperty(value = "下钻场景")
    private DrillDownSceneEnum drillDownSceneEnum;

    /**
     * filePathList
     */
    @ApiModelProperty(value = "filePathList")
    List<String> filePathList = new ArrayList<>();

    /**
     * 协议类型
     */
    @ApiModelProperty(value = "协议类型", required = true)
    private Integer dataType;

    /**
     * 是否全部
     */
    @ApiModelProperty(value = "是否全部")
    private Integer isAll;

    /**
     * 是否分页
     */
    @ApiModelProperty(value = "是否分页")
    private Integer onPage = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小")
    private Integer size = 10;

    @ApiModelProperty(value = "排序字段，默认按活跃次数排序，活跃次数=activeRate，采集时间= latestRelationTime，创建时间=createTime")
    private String sortField;

    @ApiModelProperty(value = "排序方式，默认为降序，1=降序；2=升序")
    private Integer sortType;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDay;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDay;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始时间戳")
    private String startTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束时间戳")
    private String endTime;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private String createDay;

    /**
     * 分析时段,多时段使用逗号进行拼接，例如1-3,2-5,7-9,12-14,18-20,默认为0-24
     */
    @ApiModelProperty(value = "分析时段")
    private String times = "0-24";

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 档案账号
     */
    @ApiModelProperty(value = "档案账号")
    private String arcAccount;

    @ApiModelProperty(value = "档案类型")
    private String arcType;

    /**
     * 档案账号类型
     */
    @ApiModelProperty(value = "档案账号类型, 认证账号类型或者子协议类型")
    private String arcAccountType;

    /**
     * 网络类型 0:全部 1：Alert 2：Reject 3：Log
     */
    @ApiModelProperty(value = "网络类型")
    private Integer netAction;

    /**
     * 虚拟账号
     */
    @ApiModelProperty(value = "虚拟账号")
    private String virtualAccount;

    @ApiModelProperty(value = "虚拟账号应用类型")
    private String virtualAccountAppType;

    @ApiModelProperty(value = "认证账号")
    private String authAccount;

    @ApiModelProperty(value = "认证账号类型")
    private String authAccountType;

    /**
     * 查询的邮箱类型，分为inbox, outbox, draft
     * */
    private String emailType;

    /**
     * IM档案LIS明细的查询目标范围。 0 = 全部， 1 = 重要目标
     * */
    private Integer scope;

    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String lang="zh_CN";

    @ApiModelProperty(value = "源账号(虚拟/号码)")
    private String srcAccount;

    @ApiModelProperty(value = "目的账号(虚拟/号码)")
    private String dstAccount;

    @ApiModelProperty(value = "通联区域（号码档案）")
    private String connectedArea;

    @ApiModelProperty(value = "源国家")
    private String srcCountry;
}
