package com.semptian.archives.web.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.core.common.enums.ArcTypeEnum;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.core.common.util.ArcIdUtil;
import com.semptian.archives.web.service.common.enums.DrillDownNormDataTypeEnum;
import com.semptian.archives.web.service.common.enums.DrillDownSceneEnum;
import com.semptian.archives.web.service.common.util.ArcCommonUtils;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.fegin.AdsFeignClient;
import com.semptian.archives.web.service.fegin.BasicFeign;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcDrillDownFieldTranslateConfigService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.base.service.ReturnModel;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/17 16:44
 **/
@Slf4j
@Service
public class EmailArcServiceImpl {

    @Resource
    ArcCommonServiceImpl arcCommonService;

    @Resource
    ArchivesInfoService archivesInfoService;

    @Resource
    private RedisOps redisOps;

    @Resource
    private BasicFeign basicFeign;

    @Resource
    ArcAttachServiceImpl arcAttachService;

    @Resource
    private AdsFeignClient adsFeignClient;

    @Resource
    ArcDrillDownFieldTranslateConfigService arcDrillDownFieldTranslateConfigService;

    public Object getRelationEmail(String arcAccount, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        //TODO test dataModel to build redis key
        String redisKey = arcCommonService.getRedisKey("getRelationEmail", arcAccount, dateModel);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("virtual_account", arcAccount);
        HashMap<String, Object> returnData = new HashMap<>();
        ArrayList<Map<String, Object>> emails = new ArrayList<>();

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("latest_relation_time");
            pageWarpEntity.setSortType(1);
        }

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_RELATION_ACCOUNT.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_RELATION_ACCOUNT.getValue(), paramMap);
        ArrayList<String> arcIds = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(data)) {
            // 返回 virtual_account
            for (Map<String, Object> map : data) {
                HashMap<String, Object> email = new HashMap<>();
                String virtualAccount = (String) map.get("virtual_account");
                email.put("email", virtualAccount);
                email.put("latestRelationTime", map.get("latest_relation_time"));
                email.put("arcId", ArcIdUtil.getEmailAccountId(virtualAccount));
                email.put("arcAccount", virtualAccount);
                email.put("arcAccountType", ArcTypeEnum.EMAIL.getKey());
                arcIds.add(ArcIdUtil.getEmailAccountId(virtualAccount));

                emails.add(email);
            }
        }


        // 查询档案是否建档，并且补全建档信息
        List<ArcInfoModel> arcInfoByIds = archivesInfoService.getArcInfoByIds(arcIds, Integer.valueOf(ArcTypeEnum.EMAIL.getKey()));
        // 基于 arcInfoByIds.id 构建 arcMap, 并且迭代 emails
        // 若 arcMap 中存在，则 email put hasArc true, 否则 put false
        Map<String, ArcInfoModel> arcMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(arcInfoByIds)) {
            for (ArcInfoModel arcInfoModel : arcInfoByIds) {
                arcMap.put(arcInfoModel.getId(), arcInfoModel);
            }
        }

        if (CollectionUtil.isNotEmpty(emails)) {
            for (Map<String, Object> email : emails) {
                String arcId = (String) email.get("arcId");
                ArcInfoModel arcInfoModel = arcMap.get(arcId);
                if (arcInfoModel != null) {
                    email.put("hasArc", 1);
                } else {
                    email.put("hasArc", 0);
                }
            }
        }

        returnData.put("emails", emails);
        returnData.put("total", total);
        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object getNickname(String arcAccount, PageWarpEntity pageWarpEntity, DateModel dateModel) {
        //TODO test dataModel, pageWarpEntity to build redis key
        String redisKey = arcCommonService.getRedisKey("getNickname", arcAccount, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("virtual_account", arcAccount);
        HashMap<String, Object> returnData = new HashMap<>();

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_NICKNAME_RECORD.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_NICKNAME_RECORD.getValue(), paramMap, true);

        returnData.put("list", data);
        returnData.put("total", total);

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object getPassword(String arcAccount, PageWarpEntity pageWarpEntity, DateModel dateModel) {
        //TODO test dataModel, pageWarpEntity to build redis key
        String redisKey = arcCommonService.getRedisKey("getPassword", arcAccount, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("virtual_account", arcAccount);
        HashMap<String, Object> returnData = new HashMap<>();

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_PASSWORD_RECORD.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_PASSWORD_RECORD.getValue(), paramMap);

        returnData.put("list", data);
        returnData.put("total", total);

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object getHighFrequencyPhonesRank(String arcAccount, String sense, PageWarpEntity pageWarpEntity, DateModel dateModel) {
        String redisKey = arcCommonService.getRedisKey("getHighFrequencyPhonesRank", sense, arcAccount, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("virtual_account", arcAccount);
        HashMap<String, Object> returnData = new HashMap<>();

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_PHONE_EXTRACT.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_PHONE_EXTRACT.getValue(), paramMap, true);

        // data 数据格式如下
        // phone|behaviorNum|fileInfo                                                                                                                                |captureTime|
        //-----+-----------+----------------------------------------------------------------------------------------------------------------------------------------+-----------+
        //  110|          4|{"file4.text":"/semptian/file-04", "file3.text":"/semptian/file-03", "file1.text":"/semptian/file-01", "file2.text":"/semptian/file-02"}| **********|

        // 需要遍历 data, 进行如下操作
        // 1 需要基于 phone , 调用 getArcInfoByIds, 参数为 phones , ArcTypeEnum.PHONE.getKey() ,判断是否建档, 若返回结果中的 hasArc 为 true, 则 put true, 否则 put false
        // 2 需要通过 json 解析 fileInfo 字段, 构成返回实体 fileInfos，其中key作为 fileName, value 作为 filePath

        List<String> phoneIds = new ArrayList<>();
        for (Map<String, Object> map : data) {
            String phone = map.getOrDefault("phone", "").toString();
            phoneIds.add(ArcIdUtil.getPhoneId(phone));

            map.put("arcId", ArcIdUtil.getPhoneId(phone));
            map.put("arcAccount", phone);
            map.put("arcAccountType", ArcTypeEnum.PHONE.getKey());
        }

        List<ArcInfoModel> arcInfoByIds = archivesInfoService.getArcInfoByIds(phoneIds, Integer.valueOf(ArcTypeEnum.PHONE.getKey()));
        Map<String, ArcInfoModel> arcMap = new HashMap<>();
        for (ArcInfoModel arcInfoModel : arcInfoByIds) {
            arcMap.put(arcInfoModel.getId(), arcInfoModel);
        }

        for (Map<String, Object> map : data) {
            String phone = map.getOrDefault("phone", "").toString();
            if (arcMap.containsKey(ArcIdUtil.getPhoneId(phone))) {
                map.put("hasArc", 1);
            } else {
                map.put("hasArc", 0);
            }

            String fileInfo = map.getOrDefault("fileInfo", "").toString();

            ArrayList<Map<String, String>> fileInfos = new ArrayList<>();

            if (StringUtils.isNotBlank(fileInfo)) {
                JSONObject fileInfoObj = JSONObject.parseObject(fileInfo);
                // 迭代 fileInfoObj, key 作为 fileName, value 作为 filePath
                fileInfoObj.forEach((k, v) -> {
                    Map<String, String> fileInfoMap = new HashMap<>();
                    fileInfoMap.put("fileName", k);
                    fileInfoMap.put("filePath", v.toString());
                    fileInfos.add(fileInfoMap);
                });
            }

            map.put("fileInfos", fileInfos);
        }

        returnData.put("list", data);
        returnData.put("total", total);

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object getEmailOriginals(String arcAccount, String phone, PageWarpEntity pageWarpEntity, DateModel dateModel) {
        //TODO test dataModel, pageWarpEntity to build redis key
        String redisKey = arcCommonService.getRedisKey("getEmailOriginals", arcAccount, phone, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("virtual_account", arcAccount);
        paramMap.put("phone", phone);
        HashMap<String, Object> returnData = new HashMap<>();

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_PHONE_EXTRACT_SOURCE.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_PHONE_EXTRACT_SOURCE.getValue(), paramMap, true);

        returnData.put("list", data);
        returnData.put("total", total);

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object getEmailDownloads(String arcAccount, String phone, PageWarpEntity pageWarpEntity, DateModel dateModel) {
        //TODO test dataModel, pageWarpEntity to build redis key
        String redisKey = arcCommonService.getRedisKey("getEmailDownloads", arcAccount, phone, dateModel, pageWarpEntity);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("virtual_account", arcAccount);
        paramMap.put("phone", phone);
        HashMap<String, Object> returnData = new HashMap<>();

        List<Map<String, Object>> data = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_PHONE_EXTRACT_FILE_LIST.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_PHONE_EXTRACT_FILE_LIST.getValue(), paramMap, true);

        returnData.put("list", data);
        returnData.put("total", total);

        redisOps.set(redisKey, returnData, DateUtils.getRedisExpireTime());
        return returnData;
    }

    public Object getEmailOriginalDetail(String authAccount, String authType, String virtualAccount, String filePath, String captureDay, String lang) {

        String redisKey = arcCommonService.getRedisKey("getEmailDownloads", authAccount, authType, filePath, captureDay, lang);
        Object result = redisOps.get(redisKey);
        if (result != null && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(result);
        }

        ArrayList<String> filePathList = new ArrayList<>();
        filePathList.add(filePath);

        DrillDownModel drillDownModel = new DrillDownModel();
        drillDownModel.setDataType(Integer.valueOf(DataTypeEnum.EMAIL.getKey()));
        drillDownModel.setOnPage(1);
        drillDownModel.setSize(1);
        drillDownModel.setStartDay(captureDay);
        drillDownModel.setEndDay(captureDay);
        drillDownModel.setArcAccount(virtualAccount);
        drillDownModel.setArcAccountType(null);
        drillDownModel.setVirtualAccount(virtualAccount);
        drillDownModel.setAuthAccount(authAccount);
        drillDownModel.setAuthAccountType(authType);
        drillDownModel.setLang(lang);
        drillDownModel.setFilePathList(filePathList);
        drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.PHONE_EXTRACT_SOURCE_DETAIL);
        drillDownModel.setSortField("capture_time");
        drillDownModel.setSortType(1);
        drillDownModel.setArcType(ArcTypeEnum.EMAIL.getKey());

        Map<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("total", 0L);
        resultMap.put("list", new ArrayList<>());

        List<Map<String, Object>> listResult = new ArrayList<>();
        HashMap<String, Object> filePathMap = new HashMap<>();
        filePathMap.put("filePath", filePath);
        HashMap<String, Object> result1 = (HashMap<String, Object>)arcAttachService.drillFilePathSource(authAccount, Integer.valueOf(DataTypeEnum.EMAIL.getKey()), lang, drillDownModel, listResult, resultMap);

        List<AttachSourceDataModel.DetailEntity> detailResult = new ArrayList<>();
        if (result1 != null) {
            List<AttachSourceDataModel> list =(List<AttachSourceDataModel>) result1.get("list");

            if (CollectionUtil.isNotEmpty(list)) {
                AttachSourceDataModel attachSourceDataModel = list.get(0);
                detailResult = attachSourceDataModel.getDetail();
                if(CollectionUtils.isNotEmpty(detailResult)){
                    AttachSourceDataModel.DetailEntity detailEntity = new AttachSourceDataModel.DetailEntity();
                    detailEntity.setField("captureTime");
                    detailEntity.setValue(attachSourceDataModel.getCaptureTime());
                    detailResult.add(detailEntity);
                }
                redisOps.set(redisKey, detailResult, DateUtils.getRedisExpireTime());
            }
        }

        return detailResult;
    }

    /**
     * @param arcAccount    邮件账号
     * @param searchType    搜索类型 0 = 全部，1 = 邮箱账号， 2 = 邮件内容，3 = 认证账号， 4 = 重要目标账号
     * @param keyword       查询关键字
     * @param onlyImportant 是否仅展示重要目标，0 = 展示所有数据， 1 = 仅展示重要目标数据
     * @param dateModel     时间选项
     * @return 收件箱、发件箱、草稿箱的邮件数量，分别对应的key为inbox. outbox, draft
     */
    public Map<String, Object> statisticEmailCount(String arcAccount, Integer searchType, String keyword, Integer onlyImportant, DateModel dateModel) {
        Map<String, Object> data = new HashMap<>(2);
        List<Map<String, Object>> result = new ArrayList<>();

        for (MailBoxType mailBox : MailBoxType.values()) {
            Map<String, Object> box = new HashMap<>(2);

            CommonParamUtil.ParamMap paramMap = buildEmailCondition(arcAccount, mailBox, searchType, keyword, onlyImportant, dateModel);

            String fromField = "mail_from";
            if ("outbox".equals(mailBox.getName()) || "draft".equals(mailBox.getName())) {
                fromField = "mail_to";
            }

            String fields = fromField + " as mailfrom, subject, text, (case when important_target_flag in (1, 2, 3) then auth_account when important_target_flag = 4 then strsrc_ip when important_target_flag = 5 then strdst_ip else '' end) as importantTarget, important_target_flag as importantTargetType, file_path as filePath, charset, capture_day as captureDay ";

            paramMap.put("fields", fields);

            try {
                Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_LIS_DETAIL_LIST.getValue(), paramMap, true);
                box.put("num", total);
            } catch (Exception e) {
                log.error("Statistic email error", e);
                box.put("num", 0);
            }

            box.put("type", mailBox.getName());

            result.add(box);
        }

        data.put("list", result);

        return data;
    }

    /**
     * @param arcAccount     邮件账号
     * @param emailType      查询的目标类型 inbox = 收件箱， outbox = 发件箱， draft = 草稿箱
     * @param searchType     搜索类型 0 = 全部，1 = 邮箱账号， 2 = 邮件内容，3 = 认证账号， 4 = 重要目标账号
     * @param keyword        查询关键字
     * @param onlyImportant  是否仅展示重要目标，0 = 展示所有数据， 1 = 仅展示重要目标数据
     * @param dateModel      时间选项
     * @param pageWarpEntity 分页参数
     * @param sense          使用场景，支持LIS页面的查询及导出的全部字段查询
     * @return 邮件详情列表
     */
    public Map<String, Object> queryEmailDetails(String arcAccount, String emailType, Integer searchType, String keyword, Integer onlyImportant, DateModel dateModel, PageWarpEntity pageWarpEntity, String sense, String lang) {
        Map<String, Object> result = new HashMap<>(2);
        CommonParamUtil.ParamMap paramMap = buildEmailCondition(arcAccount, MailBoxType.getByName(emailType), searchType, keyword, onlyImportant, dateModel);

        String fromField = "mail_from";
        if ("outbox".equals(emailType) || "draft".equals(emailType)) {
            fromField = "mail_to";
        }

        String fields = fromField + " as mailfrom, subject, text, (case when important_target_flag in (1, 2, 3) then auth_account when important_target_flag = 4 then strsrc_ip when important_target_flag = 5 then strdst_ip else '' end) as importantTarget, important_target_flag as importantTargetType, file_path as filePath, charset, capture_day as captureDay, capture_time as captureTime ";

        if ("EXPORT".equals(sense)) {
            List<Map<String, Object>> list = arcCommonService.getHeaders(DrillDownNormDataTypeEnum.EMAIL.getKey(), 1, DrillDownSceneEnum.LIS_DETAIL, lang, true);

            StringBuilder sb = new StringBuilder();
            for (Map<String, Object> stringObjectMap : list) {
                //如果字段名称为position，需要特殊加引号处理，否则DAT侧会拦截sql
                if ("_position".equals(stringObjectMap.get("field"))) {
                    sb.append("`position` as _position").append(",");
                } else {
                    sb.append(stringObjectMap.get("field")).append(",");
                }
            }
            sb.deleteCharAt(sb.length() - 1);

            fields = sb.toString();
        }

        paramMap.put("fields", fields);

        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.EMAIL_LIS_DETAIL_LIST.getValue(), paramMap, true);
        List<Map<String, Object>> commonServiceListResult = new ArrayList<>();
        if (total > 0) {
            commonServiceListResult = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.EMAIL_LIS_DETAIL_LIST.getValue(), paramMap, pageWarpEntity);

            // 对重要目标的类型进行翻译
            if (!"EXPORT".equals(sense)) {
                List<ImportantTargetModel> importantTargetModelList = commonServiceListResult.stream()
                        .filter(map -> Integer.parseInt(map.get("importantTargetType") + "") != 0)
                        .map(map -> {
                            ImportantTargetModel model = new ImportantTargetModel();
                            model.setImportantTarget((String) map.getOrDefault("importantTarget", ""));
                            // 基础库中自定义类型细分为4、5
                            if ("5".equals(map.getOrDefault("importantTargetType", "") + "")) {
                                model.setImportantTargetType("4");
                            }
                            model.setImportantTargetType(map.getOrDefault("importantTargetType", "") + "");

                            return model;
                        }).collect(Collectors.toList());
                try {
                    ReturnModel<List<ImportantTargetModel>> importantTargetCategoryByBatch = basicFeign.getImportantTargetCategoryByBatch(importantTargetModelList);
                    if (importantTargetCategoryByBatch != null && importantTargetCategoryByBatch.getData() != null && !importantTargetCategoryByBatch.getData().isEmpty()) {

                        List<ImportantTargetModel> data = importantTargetCategoryByBatch.getData();

                        commonServiceListResult.forEach(map -> {
                            String importantTarget = (String) map.getOrDefault("importantTarget", "");
                            String importantTargetType = map.getOrDefault("importantTargetType", "") + "";

                            for (ImportantTargetModel m : data) {
                                if (importantTarget.equals(m.getImportantTarget()) && importantTargetType.equals(m.getImportantTargetType())) {
                                    map.put("importantTargetType", ArcCommonUtils.listToDirectoryPath(m.getImportantTargetCategoryList()));
                                }
                            }
                        });
                    }
                } catch (Exception e) {
                    log.error("Query import target category error", e);
                }
            } else {
                commonServiceListResult = arcDrillDownFieldTranslateConfigService.fieldTranslate(commonServiceListResult, lang, Integer.valueOf(DataTypeEnum.EMAIL.getKey()));
            }
        }
        result.put("total", total);
        result.put("list", commonServiceListResult);
        return result;
    }

    CommonParamUtil.ParamMap buildEmailCondition(String arcAccount, MailBoxType mailBox, Integer searchType, String keyword, Integer onlyImportant, DateModel dateModel) {
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);

        // 构建查询关键字条件
        String keywordCondition = "";
        if (StringUtils.isNotEmpty(keyword)) {

            keyword = keyword.replaceAll("'", "\\\\'").toLowerCase();

            // 将下面所有查询字段使用lower函数包裹，如mail_from等
            if (searchType == 0) {
                List<Map<String, Object>> searchHeaders = arcCommonService.getSearchHeaders(DrillDownNormDataTypeEnum.EMAIL.getKey());

                StringBuilder sb = new StringBuilder();
                sb.append(" AND (");
                for (Map<String, Object> stringObjectMap : searchHeaders) {
                    sb.append("lower(").append(stringObjectMap.get("field")).append(") like ").append("'%").append(keyword).append("%'").append(" OR ");
                }
                sb.delete(sb.length() - 3, sb.length());
                sb.append(")");

                keywordCondition = sb.toString();
            } else if (searchType == 1) {
                keywordCondition = " AND (lower(mail_from) like '%" + keyword + "%' OR lower(mail_to) like '%" + keyword + "%' OR lower(mail_cc) like '%" + keyword + "%' OR lower(mail_bcc) like '%" + keyword
                        + "%') ";
            } else if (searchType == 2) {
                keywordCondition = " AND (lower(text) like '%" + keyword + "%' OR lower(subject) like '%" + keyword + "%' OR lower(attach_text) like '%" + keyword + "%') ";
            } else if (searchType == 3) {
                keywordCondition = " AND lower(auth_account) like '%" + keyword + "%' ";
            } else if (searchType == 4) {
                keywordCondition = " AND (lower(auth_account) like '%" + keyword + "%' OR lower(strsrc_ip) like '%" + keyword + "%' OR lower(strdst_ip) like '%" + keyword + "%') AND important_target_flag <> 0 ";
            }
        }
        paramMap.put("keyword", keywordCondition);

        String importantTarget = "";
        if (onlyImportant == 1 && (searchType != 4 || StringUtils.isEmpty(keyword))) {
            importantTarget = " AND important_target_flag <> 0 ";
        }
        paramMap.put("importantTarget", importantTarget);

        arcAccount = arcAccount.replaceAll("'", "\\\\'");

        String accountCondition = "";
        switch (mailBox) {
            case OUTBOX:
                accountCondition = " AND lower(main_account) ='" + arcAccount + "' AND `action` = '32' ";
                break;
            case INBOX:
//                accountCondition = " AND (lower(mail_to) like '%" + arcAccount + "%' OR lower(mail_cc) like '%" + arcAccount + "%' OR lower(mail_bcc) like '%" + arcAccount + "%') AND `action` = '31' ";
                accountCondition = " AND lower(main_account) ='" + arcAccount + "' AND `action` = '31' ";
                break;
            case DRAFT:
                accountCondition = " AND lower(main_account) ='" + arcAccount + "' AND `action` in ('49', 'UV') ";
                break;
            default:
                break;
        }
        paramMap.put("accountCondition", accountCondition);

        return paramMap;
    }

    public ReturnModel packageAnalysis(String filePath, Integer charset, int messageId, Boolean nestedParse) {
        return adsFeignClient.packageAnalysis(filePath, filePath, 1, messageId + "", nestedParse, false);
    }
}
