package com.semptian.archives.web.service.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/2 21:04
 * Description: 鉴权计费明细传参模型
 */
@ApiModel("鉴权计费明细传参模型")
@Data
public class AuthBillingDetailModel {
    /**
     * 计费动作
     * 1.开始计费 2.结束计费 3.更新计费 99。全部动作
     */
    @ApiModelProperty(value = "计费动作")
    private Integer action = 99;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "是否全部")
    private Integer isAll = 1;

    /**
     * 开始页
     */
    @ApiModelProperty(value = "开始页")
    private Integer onPage = 1;

    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小")
    private Integer size = 10;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String sortField;

    /**
     * 1 - DESC , 0 - ASC
     */
    @ApiModelProperty(value = "1 - DESC , 0 - ASC")
    private Integer sortType;

    /**
     * 排序条件, 例: create_time desc, 多个用逗号分隔, 用于支持多字段排序, 如果没有指定该字段，则会以 sortField 和 sortType 为准, 如果指定了, 则会以当前字段为准
     */
    @ApiModelProperty(value = "多字段排序条件, 例: create_time desc, 多个用逗号分隔, 用于支持多字段排序, 如果没有指定该字段，则会以 sortField 和 sortType 为准, 如果指定了, 则会以当前字段为准")
    private String sortCondition;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    String startDay;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    String endDay;

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption = 0;

    @ApiModelProperty(value = "查询关键字")
    String keyWord;

    @ApiModelProperty(value = "档案账号")
    String arcAccount;

    @ApiModelProperty(value = "网络类型")
    Integer networkType;

    @ApiModelProperty(value = "基站编号")
    String baseStationNo;
}
