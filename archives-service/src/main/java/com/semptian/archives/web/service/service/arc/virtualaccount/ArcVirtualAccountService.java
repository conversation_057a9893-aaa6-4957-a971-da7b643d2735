package com.semptian.archives.web.service.service.arc.virtualaccount;

import com.semptian.archives.web.service.model.CountDataTypeModel;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.ArcFeatures;
import com.semptian.archives.web.service.service.arc.ArcFunctional;

import java.util.List;

public interface ArcVirtualAccountService extends ArcFunctional {

    @Override
    default ArcFeatures feature() {
        return ArcFeatures.VIRTUAL_ACCOUNT;
    }

    /**
     * @param ctx 查询上下文
     * @return 不同数据类型的数据条数
     */
    List<CountDataTypeModel> countDataType(ArcContext ctx);
}
