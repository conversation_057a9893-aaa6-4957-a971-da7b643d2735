package com.semptian.archives.web.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.entity.ArcUserExpansionConfigEntity;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcRelationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class ArcRelationServiceImplTest {

    @Mock
    private ArcUserExpansionConfigServiceImpl arcUserExpansionConfigService;

    @InjectMocks
    private ArcRelationServiceImpl arcRelationService;

    @Mock
    private ArcCommonServiceImpl arcCommonService;

    @Mock
    private ArcConnectionTypeServiceImpl arcConnectionTypeService;

    @Mock
    private ArcRemarkServiceImpl arcRemarkService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 设置模拟对象
        when(arcCommonService.getCommonServiceListResult(anyString(), any(Map.class), any(Boolean.class)))
                .thenReturn(new ArrayList<>());
        when(arcCommonService.getCommonServiceCountResult(anyString(), any(Map.class)))
                .thenReturn(10L);
        when(arcCommonService.getCommonServicePageResult(anyString(), any(Map.class), any(PageWarpEntity.class)))
                .thenReturn(new PageResultModel() {{ setTotal(10L); }});
        when(arcConnectionTypeService.getArcConnectionTypeByArcId(anyString(), anyString()))
                .thenReturn("RELATE");
        when(arcRemarkService.getArcRemarkByArcIdsAndUserId(any(List.class), anyString()))
                .thenReturn(new ArrayList<>());
    }

    @Test
    public void saveUserExpansionConfig_NoExistingConfig_ShouldCreateNewEntity() {
        UserExpansionConfigModel userExpansionConfig = new UserExpansionConfigModel();
        userExpansionConfig.setConfigJson("testConfig");
        userExpansionConfig.setCreateUser("testUser");

        when(arcUserExpansionConfigService.getOne(any(QueryWrapper.class))).thenReturn(null);

        Object result = arcRelationService.saveUserExpansionConfig(userExpansionConfig);

        verify(arcUserExpansionConfigService, times(1)).save(any(ArcUserExpansionConfigEntity.class));
        verify(arcUserExpansionConfigService, never()).update(any(ArcUserExpansionConfigEntity.class), any(QueryWrapper.class));

        assertEquals("testConfig", ((ArcUserExpansionConfigEntity) result).getConfigJson());
        assertEquals("testUser", ((ArcUserExpansionConfigEntity) result).getCreateUser());
    }

    @Test
    public void saveUserExpansionConfig_ExistingConfig_ShouldUpdateEntity() {
        UserExpansionConfigModel userExpansionConfig = new UserExpansionConfigModel();
        userExpansionConfig.setConfigJson("updatedConfig");
        userExpansionConfig.setCreateUser("testUser");

        ArcUserExpansionConfigEntity existingEntity = new ArcUserExpansionConfigEntity();
        existingEntity.setConfigJson("oldConfig");
        existingEntity.setCreateUser("testUser");

        when(arcUserExpansionConfigService.getOne(any(QueryWrapper.class))).thenReturn(existingEntity);

        Object result = arcRelationService.saveUserExpansionConfig(userExpansionConfig);

        verify(arcUserExpansionConfigService, never()).save(any(ArcUserExpansionConfigEntity.class));
        verify(arcUserExpansionConfigService, times(1)).update(any(ArcUserExpansionConfigEntity.class), any(QueryWrapper.class));

        assertEquals("updatedConfig", ((ArcUserExpansionConfigEntity) result).getConfigJson());
        assertEquals("testUser", ((ArcUserExpansionConfigEntity) result).getCreateUser());
    }

    @Test
    public void relationExpansion_RadiusType_ReturnsResult() {
        RelationExpansionRequestModel requestModel = new RelationExpansionRequestModel();
        requestModel.setArcType(ArcTypeEnum.RADIUS.getKey());
        requestModel.setAuthAccount("testAuthAccount");
        requestModel.setAuthType("1020001");

        Object result = arcRelationService.relationExpansion(requestModel, "userId", "lang");
        assertNotNull(result);
    }

    @Test
    public void relationExpansion_PhoneType_ReturnsResult() {
        RelationExpansionRequestModel requestModel = new RelationExpansionRequestModel();
        requestModel.setArcType(ArcTypeEnum.PHONE.getKey());
        requestModel.setExpansionRule("1,2,3");

        Object result = arcRelationService.relationExpansion(requestModel, "userId", "lang");
        assertNotNull(result);
    }

    @Test
    public void relationExpansion_EmailType_ReturnsResult() {
        RelationExpansionRequestModel requestModel = new RelationExpansionRequestModel();
        requestModel.setArcType(ArcTypeEnum.EMAIL.getKey());
        requestModel.setAccount("testEmail");

        Object result = arcRelationService.relationExpansion(requestModel, "userId", "lang");
        assertNotNull(result);
    }

    @Test
    public void relationExpansion_ImType_ReturnsResult() {
        RelationExpansionRequestModel requestModel = new RelationExpansionRequestModel();
        requestModel.setArcType(ArcTypeEnum.IM.getKey());
        requestModel.setAccount("testIm");

        Object result = arcRelationService.relationExpansion(requestModel, "userId", "lang");
        assertNotNull(result);
    }

    @Test
    public void getUserExpansionConfig_UserSpecificConfigExists_ReturnsUserConfig() {
        String userId = "testUser";
        ArcUserExpansionConfigEntity userConfig = new ArcUserExpansionConfigEntity();
        userConfig.setConfigJson("{\"key\":\"value\"}");

        when(arcUserExpansionConfigService.getOne(any(QueryWrapper.class))).thenReturn(userConfig);

        Object result = arcRelationService.getUserExpansionConfig(userId);

        assertNotNull(result);
        assertEquals(userConfig, result);
    }

    @Test
    public void getUserExpansionConfig_UserSpecificConfigDoesNotExist_DefaultConfigExists_ReturnsDefaultConfig() {
        String userId = "testUser";
        ArcUserExpansionConfigEntity defaultConfig = new ArcUserExpansionConfigEntity();
        defaultConfig.setConfigJson("{\"default\":\"value\"}");

        when(arcUserExpansionConfigService.getOne(any(QueryWrapper.class))).thenReturn(null).thenReturn(defaultConfig);

        Object result = arcRelationService.getUserExpansionConfig(userId);

        assertNotNull(result);
        assertEquals(defaultConfig, result);
    }

    @Test
    public void getUserExpansionConfig_UserSpecificConfigAndDefaultConfigDoNotExist_ReturnsNull() {
        String userId = "testUser";

        when(arcUserExpansionConfigService.getOne(any(QueryWrapper.class))).thenReturn(null);

        Object result = arcRelationService.getUserExpansionConfig(userId);

        assertNull(result);
    }
}
