package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2021-01-12 19:48:39
 */
@Data
@TableName("tb_user_relation_data")
public class UserRelationDataEntity implements Serializable {

	/**
	 * 自增id
	 */
	@TableId(type = IdType.AUTO)
	private Integer id;

	/**
	 * 用户id
	 */
	@TableField(value = "user_id")
	private String userId;

	/**
	 * 用户关系扩线版本号
	 */
	@TableField(value = "version")
	private Integer version;

	/**
	 * 保存的扩线的json数据
	 */
	@TableField(value = "relation_data")
	private String relationData;

	/**
	 * 表单选择输入的参数json
	 */
	@TableField(value = "params")
	private String params;

	/**
	 * 档案id
	 */
	@TableField(value = "arc_id")
	private String arcId;

	/**
	 * 档案id
	 */
	@TableField(value = "arc_account")
	private String arcAccount;

	/**
	 * 档案类型
	 */
	@TableField(value = "arc_type")
	private Integer arcType;

	/**
	 * 新建时间
	 */
	@TableField(value = "create_time")
	private Long createTime;

	/**
	 * 保存的扩线档案名称
	 */
	@TableField(value = "relation_name")
	private String relationName;

	/**
	 * 保存的扩线档案名称
	 */
	@TableField(value = "is_del")
	private Integer isDel;

	/**
	 * 已扩线档案id
	 */
	@TableField(value = "expansion_arc_id")
	private String expansionArcId;
}
