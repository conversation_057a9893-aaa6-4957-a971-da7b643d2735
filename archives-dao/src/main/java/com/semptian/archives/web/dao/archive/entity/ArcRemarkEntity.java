package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2021-02-07 12:13:23
 */
@Data
@TableName("tb_arc_remark")
public class ArcRemarkEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private String id;

	/**
	 * 档案ID
	 */
	@TableField(value = "arc_id")
	private String arcId;

	/**
	 * 档案备注
	 */
	@TableField(value = "remark")
	private String remark;

	/**
	 * 用户ID
	 */
	@TableField(value = "user_id")
	private Integer userId;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	private Long createTime;

	@TableField(exist = false)
	private String expansionArcId;
}
