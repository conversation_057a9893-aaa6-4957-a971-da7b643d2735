package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: SunQi
 * @create: 2021/06/02
 * desc: 操作MySQL业务库
 **/
@Component
@Mapper
@DS("master_mysql")
public interface MySqlMapper {

    /**
     * 根据业务code查询业务sql
     *
     * @param code
     * @return
     */
    @Select({
            "<script>" +
                    "SELECT my_sql As mySQL,ck_sql AS ckSQL FROM tb_arc_service_sql WHERE code = #{code}" +
                    "</script>"
    })
    Map<String, String> getSQlByCode(@Param("code") String code);
}
