package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.archives.web.dao.archive.entity.SlowQueryLog;
import com.semptian.archives.web.dao.archive.entity.UserRelationDataEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/3/20 10:45
 **/
@Mapper
public interface SlowQueryLogMapper extends BaseMapper<SlowQueryLog> {

    @Insert("INSERT INTO tb_arc_slow_query_log (service_code, cost, `sql`, insert_time) \n" +
            "    VALUES (#{serviceCode}, #{cost}, #{sql}, now() ) \n")
    void insertData(SlowQueryLog log);
}
