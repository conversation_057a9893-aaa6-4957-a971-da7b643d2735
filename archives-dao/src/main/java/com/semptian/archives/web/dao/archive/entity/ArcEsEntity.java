package com.semptian.archives.web.dao.archive.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:46
 **/
@Data
public class ArcEsEntity {

    /**
     * 档案id
     */
    private String id;

    /**
     * 档案名称
     */
    private String archiveName;

    /**
     * 档案别名
     */
    private String archiveAlias;

    /**
     * 档案备注
     */
    private String remark;

    /**
     * 档案类型
     */
    private Integer archiveType;

    /**
     * 默认排序评分（活跃度）
     */
    private Long sortScore;

    /**
     * 最后关联ip
     */
    private String latestRelationIp;

    /**
     * 最近活跃时间
     */
    private Long latestRelationTime;

    /**
     * 活跃总次数
     */
    private Long behaviorNum;

    /**
     * 应用类型
     */
    private String appType;

    /**
     * 档案类型别名
     */
    private String appTypeAlias;

    /**
     * 认证账号类型 对于radius=1020001、固定IP档案=1029997
     */
    private String authType;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 最后关联国家
     */
    private String latestRelationCountry;

    /**
     * 最后关联城市
     */
    private String latestRelationCity;

    /**
     * imsi
     */
    private String imsi;

    /**
     * imei
     */
    private String imei;

    /**
     * 固定IP账号实体类型 1 个人 2 政企
     */
    private Integer accountType;

    //以下统计字段，当前版本不使用

    /**
     * 应用总个数
     */
    private Integer appNum;

    /**
     * 虚拟账号总个数
     */
    private Integer virtualAccountNum;

    /**
     * 访问者区域总个数
     */
    private Integer arcVisitorArea;

    /**
     * 访问档案行为总次数
     */
    private Long arcVisitorNum;

    /**
     * 阻断行为总次数
     */
    private Long blockNum;

    /**
     * 文件总个数
     */
    private Integer fileNum;

}
