package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户扩线配置表实体
 * <AUTHOR>
 * @date 2024/6/3
 */
@Data
@TableName("tb_arc_user_expansion_config")
public class ArcUserExpansionConfigEntity {

    /**
     * 主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 配置的JSON内容
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 创建用户
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间（时间戳）
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间（时间戳）
     */
    @TableField("update_time")
    private Long updateTime;
}