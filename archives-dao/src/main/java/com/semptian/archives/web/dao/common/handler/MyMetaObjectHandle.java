package com.semptian.archives.web.dao.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Date: 2020/9/9 17:38
 * Description:
 */
@Component
public class MyMetaObjectHandle implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        Object gmtCreate = getFieldValByName("createTime", metaObject);
        if (gmtCreate == null || gmtCreate.equals(0L)) {
            setFieldValByName("createTime", System.currentTimeMillis(), metaObject);
            setFieldValByName("modifyTime", System.currentTimeMillis(), metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Object gmtUpdate = getFieldValByName("modifyTime", metaObject);
        if (gmtUpdate == null || gmtUpdate.equals(0L)) {
            setFieldValByName("modifyTime", System.currentTimeMillis(), metaObject);
        }
    }
}
