package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.archives.web.dao.archive.entity.ArcCollectionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2024/9/4 15:46
 */
@Mapper
public interface ArcCollectionMapper extends BaseMapper<ArcCollectionEntity> {

    @Select({"<script>" +
            "select archive_id as archiveId, archive_type as archiveType,create_time as createTime from tb_arc_collection_info tb where tb.user_id = #{userId}" +
            " <if test = \"arcType != null\"> and tb.archive_type IN (${arcType})</if> " +
            " order by create_time desc "+
            "</script> "})
    List<ArcCollectionEntity> queryCareArcWithArcType(@Param("userId") Long userId, @Param("arcType") String arcType);

}
