package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/19 15:17
 * Description: 下钻字段配置实体表
 */


@Data
@TableName("tb_arc_drill_down_field_config")
public class ArcDrillDownFieldConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("data_type")
    private String dataType;

    @TableField("field")
    private String field;

    @TableField("desc_zh")
    private String descZh;

    @TableField("desc_fr")
    private String descFr;

    @TableField("desc_en")
    private String descEn;

    @TableField("sort")
    private Integer sort;

    @TableField("is_core")
    private Integer isCore;

    @TableField("is_hide")
    private Integer isHide;

    @TableField("is_search")
    private Integer isSearch;

    @TableField("create_time")
    private Long createTime;

    @TableField("modify_time")
    private Long modifyTime;

    @TableField("sortable")
    private Integer sortable;

    @TableField("is_attach_source")
    private Integer isAttachSource;


}

