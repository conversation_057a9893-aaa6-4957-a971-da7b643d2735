package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/20 10:43
 **/
@Data
@TableName("tb_arc_slow_query_log")
public class SlowQueryLog {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String serviceCode;

    private Long cost;

    private String sql;

    private Date insertTime;
}
