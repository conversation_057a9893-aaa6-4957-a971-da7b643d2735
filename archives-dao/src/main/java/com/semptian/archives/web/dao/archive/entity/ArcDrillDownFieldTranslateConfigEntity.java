package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *
 * 明细下钻字段翻译配置表
 * <AUTHOR>
 * @date 2024/4/2
 */
@Data
@TableName("tb_arc_drill_down_field_translate_config")
public class ArcDrillDownFieldTranslateConfigEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("field")
    private String field;

    @TableField("translate_type")
    private Integer translateType;

    @TableField("protocol_id")
    private Integer protocolId;

    @TableField("dict_key")
    private String dictKey;
}
