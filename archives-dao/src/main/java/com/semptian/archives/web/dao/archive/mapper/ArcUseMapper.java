package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.archives.web.dao.archive.entity.ArcUseEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ArcUseMapper extends BaseMapper<ArcUseEntity> {

    /**
     * 查询最近使用的档案TOP10
     * */
    @Select({"<script>" +
            "select distinct(arc_id) as arcId, arc_type as arcType, arc_account as arcAccount from tb_arc_use where user_id = #{userId}" +
            " and arc_type = #{arcType} " +
            "ORDER BY modify_time DESC limit 10" +
            "</script>"})
    List<ArcUseEntity> latestUseArcTop10(@Param("userId") Long userId, @Param("arcType") Integer arcType);

    @Select({"<script>" +
            "select distinct(arc_id) as arcId, arc_type as arcType, arc_account as arcAccount from tb_arc_use where user_id = #{userId}" +
            " <if test = 'arcType !=null'> and arc_type IN (${arcType})</if>" +
            "ORDER BY modify_time DESC limit 10" +
            "</script>"})
    List<ArcUseEntity> latestUseArcTopByType(@Param("userId") Long userId, @Param("arcType") String arcType);

    /**
     * 查询指定用户最频繁使用的档案Top10
     * */
    @Select({"<script>" +
            "select distinct(arc_id) as arcId, arc_type as arcType, arc_account as arcAccount from tb_arc_use where user_id = #{userId} " +
            " and arc_type = #{arcType} " +
            "ORDER BY use_number DESC limit 10" +
            "</script>"})
    List<ArcUseEntity> frequencyUseArcTop10(@Param("userId") Long userId, @Param("arcType") Integer arcType);

    @Select({"<script>" +
            "select distinct(arc_id) as arcId, arc_type as arcType, arc_account as arcAccount from tb_arc_use where user_id = #{userId} " +
            "  <if test = 'arcType !=null'> and arc_type IN (${arcType})</if> " +
            "ORDER BY use_number DESC limit 10" +
            "</script>"})
    List<ArcUseEntity> frequencyUseArcTop10ByType(@Param("userId") Long userId, @Param("arcType") String arcType);
}
