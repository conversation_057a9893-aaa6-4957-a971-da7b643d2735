package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/4 15:46
 **/
@Data
@TableName("tb_arc_collection_info")
public class ArcCollectionEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 档案id
     */
    @TableField("archive_id")
    private String archiveId;

    /**
     * 档案类型
     */
    @TableField("archive_type")
    private Integer archiveType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

}
