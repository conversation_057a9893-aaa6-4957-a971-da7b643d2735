package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tb_arc_custom_columns")
public class ArcCustomColumnsEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("user_id")
    private String userId;

    @TableField("data_type")
    private String dataType;

    @TableField("columns")
    private String columns;

    @TableField("create_time")
    private Long createTime;

    @TableField("modify_time")
    private Long modifyTime;
}