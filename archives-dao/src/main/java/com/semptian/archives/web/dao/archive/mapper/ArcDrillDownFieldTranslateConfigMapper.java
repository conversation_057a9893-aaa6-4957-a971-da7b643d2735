package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.archives.web.dao.archive.entity.ArcDrillDownFieldTranslateConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 明细下钻字段翻译mapper
 *
 * <AUTHOR>
 * @date 2024/4/2
 */
@Mapper
public interface ArcDrillDownFieldTranslateConfigMapper extends BaseMapper<ArcDrillDownFieldTranslateConfigEntity>  {

    @Select("SELECT `field`,translate_type from tb_arc_drill_down_field_translate_config group by `field`,translate_type")
    List<ArcDrillDownFieldTranslateConfigEntity> getAllFieldAndTranslateType();
}
