package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import lombok.Data;

/**
 * 档案关系实体
 */
@Data
@TableName("tb_arc_connection_type")
public class ArcConnectionTypeEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("src_arc_id")
    private String srcArcId;

    @TableField("target_arc_id")
    private String targetArcId;

    @TableField("connection_type")
    private String connectionType;

    @TableField("create_time")
    private Long createTime;

    @TableField("modify_time")
    private Long modifyTime;
}
