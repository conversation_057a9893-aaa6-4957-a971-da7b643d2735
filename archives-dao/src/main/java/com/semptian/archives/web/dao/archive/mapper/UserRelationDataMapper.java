package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.archives.web.dao.archive.entity.UserRelationDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2021-01-12 19:48:39
 */
@Mapper
public interface UserRelationDataMapper extends BaseMapper<UserRelationDataEntity> {

    /**
     * 根据用户id，档案id查询最后一个版本的扩线快照信息
     * @param userId
     * @param arcId
     * @return
     */
    @Select({"select r.id,r.arc_id as arcId,r.user_id as userId, r.params,r.relation_data as relationData,r.version,r.create_time as createTime,r.relation_name as relationName from tb_user_relation_data r where r.arc_id= #{arcId} and r.user_id = #{userId} order by r.version desc limit 1"})
    UserRelationDataEntity getTopUserRelationDetailInfo(@Param("userId") String userId, @Param("arcId") String arcId);

    /**
     * 根据条件查询用户保存的版本信息
     * @param userId 用户id
     * @param arcId 档案id
     * @param startTime 保存开始时间
     * @param endTime 保存结束时间
     * @param keyWord 关键字搜索版本名称
     * @return 版本信息
     */
    @Select({"<script> " +
            "select\n" +
            "\tr.id,\n" +
            "\tr.arc_id as arcId,\n" +
            "\tr.user_id as userId,\n" +
            "\tr.params,\n" +
            "\tr.relation_data as relationData,\n" +
            "\tr.version,\n" +
            "\tr.create_time as createTime,\n" +
            "\tr.relation_name as relationName\n" +
            "from\n" +
            "\ttb_user_relation_data r\n" +
            "where\n" +
            "<if test = 'arcId !=null and arcId !=\"\" '>" +
            "\tand arc_id = #{arcId} \n" +
            "</if>" +
            "<if test = 'startTime !=null '>" +
            "\tand create_time &gt;= #{startTime} \n" +
            "</if>" +
            "<if test = 'endTime !=null '>" +
            "\tand create_time &lt;= #{endTime} \n" +
            "</if>" +
            "<if test = 'keyWord !=null and keyWord !=\"\" '>" +
            "\tand relation_name like CONCAT('%', #{keyWord}, '%') \n" +
            "</if>" +
            "\tuser_id = #{userId} \n" +
            "\torder by create_time desc,version desc" +
            " </script>"})
    List<UserRelationDataEntity> getRelationInfoVersion(@Param("userId") String userId, @Param("arcId") String arcId, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("keyWord") String keyWord);

    /**
     * 根据档案id及用户id获取最高版本号
     * @param userId 用户id
     * @param arcId 档案id
     * @return 版本号
     */
    @Select({"SELECT r.version FROM tb_user_relation_data r WHERE user_id = #{userId} and arc_id = #{arcId} order by version desc LIMIT 1"})
    Integer getMaxVersion(@Param("userId") String userId, @Param("arcId") String arcId);
}
