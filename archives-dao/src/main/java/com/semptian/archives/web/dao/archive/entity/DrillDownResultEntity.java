package com.semptian.archives.web.dao.archive.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/19 20:21
 * Description: 下钻结果模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillDownResultEntity {
    /**
     * 下钻结果总数
     */
    private Long total;
    /**
     * 下钻结果列表
     */
    private List<Map<String, Object>> list;

}
