package com.semptian.archives.web.dao.archive.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/25 14:27
 * Description: 协议数据分布实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LisRecordDistributionEntity {
    /**
     * 数据协议类型
     */
    private String dataTypeName;
    /**
     * 数据协议类型编码
     */
    private Integer dataType;
    /**
     * 数据协议行为数量
     */
    private Long behaviorNum;
    /**
     * 数据协议行为比例
     */
    private Double rate;
}
