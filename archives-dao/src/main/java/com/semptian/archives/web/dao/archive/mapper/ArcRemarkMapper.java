package com.semptian.archives.web.dao.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2021-02-07 12:13:23
 */
@Mapper
public interface ArcRemarkMapper extends BaseMapper<ArcRemarkEntity> {

    @Select({
            "<script>",
            "SELECT t1.* FROM tb_arc_remark t1 JOIN ( SELECT arc_id, MAX(id) AS id FROM tb_arc_remark where arc_id in " +
            "<foreach collection ='arcIds' item = 'arcId' separator=',' open='(' close=')'>",
            "#{arcId}",
            "</foreach>",
            " GROUP BY arc_id ",
            ") t2 ON t1.id = t2.id",
            "</script>"
    })
    List<ArcRemarkEntity> getArcRemarkByArcIdsAndUserId(@Param("arcIds") List<String> arcIds);
}
