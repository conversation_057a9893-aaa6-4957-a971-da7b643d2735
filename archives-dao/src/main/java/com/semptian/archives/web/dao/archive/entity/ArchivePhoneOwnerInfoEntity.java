package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/21 11:07
 * Description: 号码机主信息表实体类
 */
@Data
@TableName("tb_archive_phone_owner_info")
public class ArchivePhoneOwnerInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("arc_id")
    private String arcId;

    @TableField("name")
    private String name;

    @TableField("certificate_type")
    private String certificateType;

    @TableField("certificate_no")
    private String certificateNo;

    @TableField("sex")
    private String sex;

    @TableField("age")
    private Integer age;

    @TableField("religion")
    private String religion;

    @TableField("phone_number")
    private String phoneNumber;

    @TableField("address")
    private String address;

    @TableField("work_unit")
    private String workUnit;
}
