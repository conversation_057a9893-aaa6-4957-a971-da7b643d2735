package com.semptian.archives.web.dao.archive.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/25 14:27
 * Description: NF动作类型分布实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NfActionDistributionEntity {
    /**
     * 数据动作类型
     */
    private String netAction;
    /**
     * 动作类型的行为比例
     */
    private Double rate;
    /**
     * 动作类型行为数量
     */
    private Long behaviorNum;
}
