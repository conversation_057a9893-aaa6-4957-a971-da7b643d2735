package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *
 * 字典实体
 * <AUTHOR>
 * @date 2024/4/2
 */
@Data
@TableName("tb_arc_dict")
public class ArcDictEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("dict_key")
    private String dictKey;

    @TableField("dict_code")
    private String dictCode;

    @TableField("dict_name_zh")
    private String dictNameZh;

    @TableField("dict_name_fr")
    private String dictNameFr;

    @TableField("dict_name_en")
    private String dictNameEn;
}