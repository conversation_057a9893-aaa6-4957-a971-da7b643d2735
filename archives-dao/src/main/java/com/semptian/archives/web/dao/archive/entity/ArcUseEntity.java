package com.semptian.archives.web.dao.archive.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;


/**
 * 档案使用实体
 */
@Data
@TableName("tb_arc_use")
public class ArcUseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("arc_id")
    private String arcId;

    @TableField("arc_type")
    private Integer arcType;

    @TableField("arc_account")
    private String arcAccount;

    @TableField("arc_account_type")
    private String arcAccountType;

    @TableField("data_type")
    private String dataType;

    @TableField("use_number")
    private Integer useNumber;

    @TableField("create_time")
    private Long createTime;

    @TableField("modify_time")
    private Long modifyTime;
}
