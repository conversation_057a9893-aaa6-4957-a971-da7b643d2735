package com.semptian.archives.web.dao.archive.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/1 11:58
 * Description: VPN使用排行及趋势查询结果实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VpnUseRankEntity {
    /**
     * vpn使用次数排行
     */
    private List<Map<String, Object>> rank;

    /**
     * vpn使用趋势
     */
    private List<TrendEntity> trend;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendEntity {
        /**
         * app名称
         */
        private String appName;
        /**
         * app使用次数详情
         */
        List<Map<String, Object>> detail;
    }
}
