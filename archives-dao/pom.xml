<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>deye-archives-service</artifactId>
        <groupId>com.semptian</groupId>
        <version>6.4-B08</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>archives-dao</artifactId>

    <dependencies>
        <!-- spring boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--datasource config-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.9</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>

        <!-- semptian -->
        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>base-log</artifactId>
            <version>${base-log.version}</version>
        </dependency>

        <!-- archives -->
        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>archives-core</artifactId>
            <version>${parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.semptian</groupId>
            <artifactId>base-dao</artifactId>
            <version>${base-dao.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <groupId>com.baomidou</groupId>
            <version>${mybatisplus.boot-starter.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <classifier>exec</classifier>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>