# deye-archives-service

全息档案服务   
## 更新记录

### deye6.4-B08     更新日期:2024-12-31
1. 历史遗留问题
2. 开局现场发现问题

### deye6.4-B06P05  更新日期:2024-11-04
- 1.OPS_ARCHIVES_B06P05转测


### deye6.4-B06P04  更新日期:2024-10-25
- 1.OPS_ARCHIVES_B06P04转测


### deye6.4-B06P04  更新日期:2024-10-24
- 1.修改全息bug


### deye6.4-B06P03  更新日期:2024-10-17
- 1.修改全息bug
  2.新增档案标签功能
  3。部分需求优化

## 更新记录
### deye6.4-B06P02  更新日期:2024-09-25
- 1.修改全息bug

## 更新记录
### deye6.4-B06  更新日期:2024-09-09
- 1.新增档案收藏功能

### deye6.4-B03P02  更新日期:2024-04-29
- 1.修改B03版本和BO3P01版本JIRA测试问题
- 2.修改需求验收问题

### deye6.4-B03P01  更新日期:2024-04-22
- 1.修改B03版本JIRA测试问题

### deye6.4-B03  更新日期:2024-04-09
- 1.B03版本功能需求开发
- 2.[DEYE64-318]【档案首页】统计项最近30天查询的时间范围不是30天，而是一个月，这个需要改正过来
- 3.[DEYE64-309]【固定IP重复建档】在基础库固定IP地址库通过导入的方式建档，当第一次导入的文件姓名字段不填，导入失败，但是es会建档。再次导入正确格式的文件，导入成功，es会再次建档
- 4.[DEYE64-281]【应用分析】应用分析统计条件的应用分类和应用获取的sql是写死的，而且阻断和非阻断的应用分类和应用都来源dws_element_behavior_auth_account_app_detail，建议分开
- 5.[DEYE64-272]【部署】建议:mysql数据库tb_arc_service_sql表ck_sql字段中不要包含数据库名,把库名做成配置项;

### deye6.4-B02P01  更新日期:2024-01-26
- 1.[DEYE64-231]【部署】初始化建表语句不全,建表名称与模型不一致;
- 2.[DEYE64-235]【档案首页】点击最近使用和最常使用的档案，接口没有返回arcAccountType字段，导致跳转到档案详情页面，很多数据査询不到如文件信息。访问者，应用分析等
- 3.[DEYE64-236]【应用档室】档案首页-活跃趋势分析/活跃时段分析无数据展示,后端査询sql报错
- 4.[DEYE64-238]【档案首页】最近30天失活数/最近失活筛选时间范围不一致;
- 5.[DEYE64-241]【应用/网站档案】最近7天访问数统计不准确,
- 6.[DEYE64-242]【访问者】应用和网站档案的访问者get access target lisi接口返回的dataType字段类型不是Sting，导致前端判断出现错误
- 7.[DEYE64-243]【访问者】应用和网站档案，通过关键词査询，界面可以査询出和关键字没有关系的数据，査询的SQL需要优化
- 8.[DEYE64-244]【应用/网站档案】最近7天访问者-档案访问/P访问-最近7天访问热度统计不准确
- 9.[DEYE64-258]【应用/网站/固定IP档案】文件信息-关联虚拟账号/认证账号功能展示未进行去重操作,
- 10.[DEYE64-261]【固定IP档案】档案首页-虚拟账号数查询表不正确
- 11.[DEYE64-262]【日志打印】建议打印sql时,带上查询模块名称:
- 12.[DEYE64-265]【应用档案】访问者-导出,类型未转译,
- 13.[DEYE64-266]【关注】三种档案的档案首页都是已关注状态
- 14.[DEYE64-267]【全部档案】关键词搜索不支几特殊字符查询
- 15.[DEYE64-274]【网站档案】访问者-档案访问-sql查询展示字段几余:
- 16.[DEYE64-275]【建档】建议:基础库固定IP建档生成es文档id和mysql同定IP库id主键保持一致,
- 17.[DEYE64-276]【同定PP档案】虚拟账号协议类型展示前后端未保持一致


### deye6.4-B02  更新日期:2024-01-18
- 1.优化模型设计，适配网站、应用、固定IP三种类型档案
- 2.访问数据资源方式调整，使用base-tianhe依赖访问Doris和ES