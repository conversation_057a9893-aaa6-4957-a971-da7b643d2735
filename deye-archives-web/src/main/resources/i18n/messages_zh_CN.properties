#\u8FD9\u91CC\u586B\u5199\u4E2D\u6587\u7FFB\u8BD1
group=\u7EC4\u7EC7
website=\u7F51\u7AD9
app=\u5E94\u7528
success=\u4FDD\u5B58\u6210\u529F !
error=\u4FDD\u5B58\u5931\u8D25 !
isCare\ must\ be\ 0\ or\ 1.=isCare\u5FC5\u987B\u662F0\u6216\u80051
size\ can\ not\ be\ too\ lager.=\u7FFB\u9875\u53C2\u6570size\u4E0D\u80FD\u8FC7\u5927
arc.permission.error=\u65E0\u6863\u6848\u6743\u9650
fixed.ip.exist=\u6863\u6848\u6B63\u5728\u5220\u9664\u4E2D...

fixed.ip.account.type.individual=\u4E2A\u4EBA
fixed.ip.account.type.government_enterprise=\u653F\u4F01

docName=\u6587\u6863
imgName=\u56FE\u7247
zipName=\u538B\u7F29\u6587\u4EF6
mp4Name=\u97F3\u9891\u6587\u4EF6
otherName=\u5176\u4ED6\u6587\u4EF6
paramIsBlank=\u5C5E\u6027\u4E3A\u7A7A
noInitialData=\u65E0\u521D\u59CB\u5316\u6570\u636E
interfaceInnerInvokeError=\u670D\u52A1\u8C03\u7528\u5931\u8D25
updateTagError=\u4FEE\u6539\u8BCD\u4E91\u5931\u8D25
paramIsInvalid=\u5C5E\u6027\u65E0\u6548
mergeFileIsEmpty=\u5408\u5E76\u6863\u6848\u4E3A\u7A7A
person=\u4EBA\u5458
tagIsBlank=\u6807\u7B7E\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A\uFF01
sortType\ must\ be\ 0\ or\ 1.=sortType\u5FC5\u987B\u662F0\u6216\u80051

sex.male=\u7537
sex.female=\u5973
certificate.type.id_card=\u8EAB\u4EFD\u8BC1
certificate.type.passport=\u62A4\u7167
certificate.type.driver_license=\u9A7E\u7167
certificate.type.other=\u5176\u5B83

relation.version.exists=\u6269\u7EBF\u7248\u672C\u540D\u79F0\u5DF2\u5B58\u5728
relation.version.exceed.limit=\u6700\u591A\u53EA\u80FD\u521B\u5EFA10\u4E2A\u6269\u7EBF\u7248\u672C