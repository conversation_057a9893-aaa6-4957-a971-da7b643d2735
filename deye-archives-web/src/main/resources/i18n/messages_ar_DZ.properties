#\u8FD9\u91CC\u586B\u5199\u963F\u62C9\u4F2F\u8BED\u7FFB\u8BD1
group=\u0627\u0644\u0645\u062C\u0645\u0648\u0639\u0629
app=\u0627\u0644\u062A\u0637\u0628\u064A\u0642
website=\u0645\u0648\u0642\u0639
success=\u062D\u0641\u0638 \u0628\u0646\u062C\u0627\u062D
error=\u00A0\u0641\u0634\u0644 \u062D\u0641\u0638
isCare\ must\ be\ 0\ or\ 1.=\u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 iscare 0 \u0623\u0648 1 .
size\ can\ not\ be\ too\ lager.=\u0644\u0627 \u064A\u0645\u0643\u0646 \u0623\u0646 \u062A\u0643\u0648\u0646 \u0643\u0628\u064A\u0631\u0629 \u062C\u062F\u0627 .
fixed.ip.exist=Fixed IP isn't exists
fixed.ip.account.type.individual=Personal
fixed.ip.account.type.government_enterprise=Government and Enterprise

docName=\u0645\u0633\u062A\u0646\u062F\u0627\u062A
imgName=\u0645\u0644\u0641\u0627\u062A \u0627\u0644\u0635\u0648\u0631
zipName=\u0645\u0644\u0641 \u0645\u0636\u063A\u0648\u0637
mp4Name=\u0645\u0644\u0641 \u0635\u0648\u062A\u064A
otherName=\u0648\u062B\u0627\u0626\u0642 \u0623\u062E\u0631\u0649
paramIsBlank=\u0627\u0644\u0645\u0639\u0644\u0645\u0629 \u0641\u0627\u0631\u063A\u0629
noInitialData=\u0644\u0627 \u062A\u0647\u064A\u0626\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A
interfaceInnerInvokeError=\u062E\u062F\u0645\u0629 \u0627\u0644\u0627\u062A\u0635\u0627\u0644 \u0641\u0634\u0644
updateTagError=\u062A\u0639\u062F\u064A\u0644 \u0643\u0644\u0645\u0629 \u0633\u062D\u0627\u0628\u0629 \u0641\u0634\u0644
paramIsInvalid=\u0627\u0644\u0645\u0639\u0644\u0645\u0629 \u063A\u064A\u0631 \u0635\u0627\u0644\u062D\u0629
mergeFileIsEmpty=\u062F\u0645\u062C \u0627\u0644\u0645\u0644\u0641\u0627\u062A \u0641\u0627\u0631\u063A\u0629
person=\u0627\u0644\u0645\u0648\u0638\u0641\u064A\u0646
tagIsBlank= \u0627\u0633\u0645 \u0627\u0644\u0639\u0644\u0627\u0645\u0629 \u0644\u0627 \u064A\u0645\u0643\u0646 \u0623\u0646 \u062A\u0643\u0648\u0646 \u0641\u0627\u0631\u063A\u0629
sortType\ must\ be\ 0\ or\ 1.=sorttype \u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 0 \u0623\u0648 1 .
