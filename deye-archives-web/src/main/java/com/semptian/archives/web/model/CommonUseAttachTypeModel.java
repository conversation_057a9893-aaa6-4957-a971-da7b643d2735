package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description 常用附件类型模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseAttachTypeModel implements Comparable{

    /**
     * 附件后缀
     */
    private String attachType;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        CommonUseAttachTypeModel virtualAccountCountModel = (CommonUseAttachTypeModel) o;
        int times = (int)num - (int)this.num;
        return times;
    }
}
