package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description TOP访问目标模型
 * <AUTHOR>
 * @Date 2021/06/17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopAccessTargetModel{

    /**
     * 档案访问目标
     */
    private List<TopArcAccessTargetModel> topArcTargets;

    /**
     * IP访问目标
     */
    private List<TopArcAccessTargetModel> topIpTargets;
}
