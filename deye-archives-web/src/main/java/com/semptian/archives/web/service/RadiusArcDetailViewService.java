package com.semptian.archives.web.service;

import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;

/**
 * radius档案详情实现接口
 * @author: caoyang
 * @create: 2021/06/08
 **/
public interface RadiusArcDetailViewService {

    /**
     * 查询radius档案认证记录信息
     * @param arcAccount 档案账号
     * @param ip ip地址
     * @param dateModel 日期模型参数
     * @param pageWarpEntity 分页参数
     * @return 认证记录明细信息
     */
    Object getRadiusAuthRecord(String arcAccount, String ip, DateModel dateModel, PageWarpEntity pageWarpEntity);
}
