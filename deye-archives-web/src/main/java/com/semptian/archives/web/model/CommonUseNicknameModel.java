package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description 常用昵称模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseNicknameModel implements Comparable{

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        CommonUseNicknameModel virtualAccountCountModel = (CommonUseNicknameModel) o;
        int times =  (int) virtualAccountCountModel.num - (int) this.num;
        return times;
    }
}
