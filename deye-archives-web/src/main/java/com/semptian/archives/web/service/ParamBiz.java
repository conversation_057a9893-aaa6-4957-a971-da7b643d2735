package com.semptian.archives.web.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.fegin.AdsFeignClient;
import com.semptian.archives.web.service.model.AdsModel;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.util.model.ImportTypeDto;
import com.semptian.archives.web.util.model.ParamDto;
import com.semptian.base.service.ReturnModel;
import com.semptian.redis.template.RedisOps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class ParamBiz {

    @Autowired
    private RedisOps redisOps;
    @Autowired
    private AdsFeignClient adsFeignClient;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Value("${archive.search.timeOut:10000}")
    private int timeOut;

    @Value("${archive.search.docNum:50000000}")
    private int docNum;

    @Resource
    private ArcCommonService arcCommonService;

    private static List<JSONObject> locationListByCN = new ArrayList<>();
    private static List<JSONObject> locationListBYFR = new ArrayList<>();
    private static List<JSONObject> locationListBYEN = new ArrayList<>();

    public List<JSONObject> getLocationList(String lang) {
        List<JSONObject> list;
        if ("en_US".equals(lang)) {
            list = locationListBYEN;
        } else if ("zh_CN".equals(lang)) {
            list = locationListByCN;
        } else {
            list = locationListBYFR;
        }
        if (!CollectionUtil.isEmpty(list)) {
            return list;
        } else {
            ReturnModel countryList = adsFeignClient.getCountryList(new AdsModel());
            List<JSONObject> jsonObjects = Lists.newArrayList();
            try {
                Map data = (Map) countryList.getData();
                List<Map> records = (List<Map>) data.get("records");
                for (Map record : records) {
                    JSONObject location = new JSONObject();
                    location.put("id", record.get("simpleCountryName"));
                    String name;
                    if ("en_US".equals(lang)) {
                        name = record.get("countryNameEn").toString();
                    } else if ("zh_CN".equals(lang)) {
                        name = record.get("countryNameZh").toString();
                    } else {
                        name = record.get("countryNameFr").toString();
                    }
                    location.put("name", name);
                    jsonObjects.add(location);
                }
            } catch (Exception e) {
                log.error("ParamBiz.getLocationList is fail;error is {}",e);
                return null;
            }
            if ("en_US".equals(lang)) {
                locationListBYEN = jsonObjects;
            } else if ("zh_CN".equals(lang)) {
                locationListByCN = jsonObjects;
            } else {
                locationListBYFR = jsonObjects;
            }
            return jsonObjects;
        }
    }

    public Object getAppList() {
        String key = "appByStats";
        if (redisOps.hasKey(key)) {
            return redisOps.get(key);
        } else {
            List<Map<String, Object>> result = getAllAppNameAndAppType();
            redisOps.set(key, result, 86400L);

            return result;
        }
    }

    public Object setTypeList(List<ImportTypeDto> listBean, String paramType) {
        Map<String, List<String>> paramDic = Maps.newHashMap();
        for (ImportTypeDto importTypeDto : listBean) {
            if (StringUtils.isBlank(importTypeDto.getType())) {
                continue;
            }
            List<String> value;
            if (paramDic.containsKey(importTypeDto.getType().trim())) {
                value = paramDic.get(importTypeDto.getType().trim());
            } else {
                value = Lists.newArrayList();
            }
            if (!StringUtils.isBlank(importTypeDto.getName())) {
                value.add(importTypeDto.getName().trim());
            }
            paramDic.put(importTypeDto.getType().trim(), value);
        }
        List<ParamDto> paramList = Lists.newArrayList();
        for (Map.Entry<String, List<String>> value : paramDic.entrySet()) {
            ParamDto paramTypeDto = new ParamDto();
            paramTypeDto.setName(value.getKey());
            List<ParamDto> paramNameDtoList = Lists.newArrayList();
            for (String name : value.getValue()) {
                ParamDto paramNameDto = new ParamDto();
                paramNameDto.setName(name);
                paramNameDtoList.add(paramNameDto);
            }
            paramTypeDto.setChildren(paramNameDtoList);
            paramList.add(paramTypeDto);
        }
        String key;
        if (paramType.equals("user")) {
            key = "accountByStats";
        } else {
            key = "appByStats";
        }
        return redisOps.set(key, paramList);
    }

    /**
     * 获取所有的应用名称和应用类型
     *
     * @return
     */
    private List<Map<String, Object>> getAllAppNameAndAppType() {

        Map<String, Object> params = CommonParamUtil.buildCommonTimeParam();
        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> appList = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.COMMON_GET_APP_LIST_CONDITIONS.getValue(), params, true);

        if (appList != null && !appList.isEmpty()) {
            for (Map<String, Object> appMap : appList) {
                String appType = (String) appMap.get("appType");
                String appName = (String) appMap.get("appName");
                List<Map<String, Object>> appNameMap = new ArrayList<>();
                List<String> appNameList = Arrays.asList(StringUtils.split(appName, ","));
                for (String appNameStr : appNameList) {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("name", appNameStr);
                    map.put("children", null);
                    appNameMap.add(map);
                }
                Map<String, Object> map = new HashMap<>(16);
                map.put("name", appType);
                map.put("children", appNameMap);
                result.add(map);
            }
        }
        return result;
    }

    /**
     * 获取IM协议所有的应用类型
     * @return
     */
    public List<String> getIMAppType(){
        return Lists.newArrayList();
    }
}
