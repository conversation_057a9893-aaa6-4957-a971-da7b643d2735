package com.semptian.archives.web.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RadiusAppLocalhostModel implements Comparable<RadiusAppLocalhostModel> {
    private Long latestTime;
    private Long earliestTime;
    private String authAccount;
    private String virtualAccount;
    private String groupIp;
    private String groupCountry;
    private long hotNum;
    private long hotDay;

    @Override
    public int compareTo(RadiusAppLocalhostModel o) {
        return (int) (o.hotNum - this.hotNum);
    }
}
