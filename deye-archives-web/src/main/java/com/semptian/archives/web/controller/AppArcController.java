package com.semptian.archives.web.controller;

import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.ArcStatisticModel;
import com.semptian.archives.web.service.model.ArchiveModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.AppArcService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.archives.web.service.service.impl.ImArcServiceImpl;
import com.semptian.base.service.CustomException;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: SunQi
 * @create: 2021/06/08
 * desc:
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/app_arc_detail")
@Slf4j
@Api
public class  AppArcController {

    @Resource
    private AppArcService appArcService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @ApiOperation(value = "应用批量档案维度统计", httpMethod = "POST", response = ReturnModel.class)
    @PostMapping("/get_arc_statistical_dimensions.json")
    public ReturnModel<?> getAppStatisticsInfo(
            @RequestBody ArcStatisticModel arcStatisticModel,
            HttpServletRequest request
    ) {
        Integer index = arcStatisticModel.getIndex();
        Integer isAll = arcStatisticModel.getIsAll();
        List<ArchiveModel> archiveModelList = arcStatisticModel.getArchiveModels();
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /domain_arc_detail/get arc detail info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        if (index > 13 || index < 1) {
            throw new CustomException(ArcServerReturnCode.PARAM_IS_INVALID.getCode(), I18nUtils.getMessage("index must be one of 1、2、3 、4 or 5 "));
        }
        Object result = commonArcServiceFacade.getStatisticalDimensions(ArcTypeEnum.APP, archiveModelList, isAll, index, userId);
        if (result == null) {
            return ReturnModel.getInstance().error("Arc not exists!");
        } else {
            return ReturnModel.getInstance().ok(result);
        }
    }

    @GetMapping("/get_access_arc_target_top.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appName", value = "认证账号", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appType", value = "认证类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "topSize", value = "返回档案数", dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "获取应用档案访问目标TOP--档案访问", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getAccessTargetTop(
            @RequestParam(name = "arcId") String arcId,
            @RequestParam(name = "appName") String appName,
            @RequestParam(name = "appType", required = false, defaultValue = "") String appType,
            @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize,
            HttpServletRequest request
    ) {

        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /app_arc_detail/get_access_target_top.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).appName(appName).appType(appType).topSize(topSize).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.APP, ctx, AccessTargetTopEnum.ARC));
    }

    @GetMapping("/get_access_ip_target_top.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appName", value = "认证账号", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appType", value = "认证类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "topSize", value = "返回档案数", dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "获取应用档案访问目标TOP--Ip访问", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getIpAccessTargetTop(
            @RequestParam(name = "arcId") String arcId,
            @RequestParam(name = "appName") String appName,
            @RequestParam(name = "appType", required = false, defaultValue = "") String appType,
            @RequestParam(name = "topSize", required = false, defaultValue = "10") Integer topSize,
            HttpServletRequest request
    ) {

        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /app_arc_detail/get_access_target_top.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).appName(appName).appType(appType).topSize(topSize).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.APP, ctx, AccessTargetTopEnum.IP));
    }

    @GetMapping("/get_access_important_target_top.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appName", value = "认证账号", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appType", value = "认证类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "topSize", value = "返回档案数", dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "获取应用档案访问目标TOP--重要目标访问", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getImportantAccessTargetTop(
            @RequestParam(name = "arcId") String arcId,
            @RequestParam(name = "appName") String appName,
            @RequestParam(name = "appType", required = false, defaultValue = "") String appType,
            @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize,
            HttpServletRequest request
    ) {

        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /app_arc_detail/get_access_target_top.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).appName(appName).appType(appType).topSize(topSize).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.APP, ctx, AccessTargetTopEnum.IMPORTANT));
    }

    @GetMapping("/get_access_target_list.json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "onPage", value = "页码：后台设置默认值 1", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "条数：后台设置默认值 10", dataType = "Integer", paramType = "query"),
    })
    @ApiOperation(value = "get_access_target_list", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public ReturnModel<?> getAppAccessTargetList(
            @RequestParam(name = "arcId") String arcId,
            @RequestParam(name = "appName") String appName,
            @RequestParam(name = "appType", required = false) String appType,
            @RequestParam(name = "dataType", required = false) String dataType,
            @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "netAction", required = false, defaultValue = "0") Integer netAction,
            @RequestParam(name = "keyword",required = false) String keyWord,
            @RequestParam(name = "visitorType", required = false, defaultValue = "1") Integer visitorType,
            @RequestParam(name = "sortField", required = false, defaultValue = "nfNum") String sortField,
            @RequestParam(name = "sortType", required = false, defaultValue = "1") Integer sortType,
            DateModel dateModel
    ) {

        ArcContext ctx = ArcContext.builder().arcId(arcId).appName(appName).appType(appType).dataType(dataType).onPage(onPage).size(size)
                            .netAction(netAction).visitorType(visitorType).keyWord(keyWord).dateModel(dateModel).sortField(sortField).sortType(sortType).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetList(ArcTypeEnum.APP, ctx));
    }

    @GetMapping("/get_ip_list.json")
    @ApiOperation(value = "get_ip_list", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public ReturnModel<?> getAppIpList(
            @RequestParam(name = "arcId") String arcId,
            @RequestParam(name = "appName") String appName,
            @RequestParam(name = "appType", required = false) String appType,
            @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sortField", required = false, defaultValue = "latestTime") String sortField,
            @RequestParam(name = "sortType", required = false, defaultValue = "1") Integer sortType,
            DateModel dateModel
    ) {
        ArcContext ctx = ArcContext.builder().arcId(arcId).appName(appName).appType(appType).onPage(onPage).size(size)
                .dateModel(dateModel).sortField(sortField).sortType(sortType).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getServerIpList(ArcTypeEnum.APP, ctx));
    }

    @GetMapping("/get_app_type_list.json")
    @ApiOperation(value = "get_app_type_list", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getAppTypeList() {
        return appArcService.getAppTypeInfo();
    }
}

