package com.semptian.archives.web.common.config;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.StringJoiner;

/**
 * 使用aop返回接口缓存值
 *
 * <AUTHOR>
 * @date 2022/08/29
 */
@Component
@Aspect
@Slf4j
public class RedisAopAdvice {

	//获取输入参数
    @Before("execution(* *..*ArcCommonServiceImpl.getRedisKey*(..))")
    public void before(JoinPoint jp){
        log.info("调用的缓存的类 :{}" , Thread.currentThread().getStackTrace()[19].getFileName());
        log.info("调用的缓存的方法 :{}" , Thread.currentThread().getStackTrace()[19].getMethodName());
        log.info("调用的缓存生成方法 :{}", jp.getSignature().toShortString());
        Object[] args = jp.getArgs();
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (ObjectUtil.isNotNull(args)){
            for (Object arg : args) {
                if (arg instanceof Object[]){
                    for (Object o : (Object[]) arg) {
                        if (o == null){
                            stringJoiner.add("null");
                        }else {
                            String s = JSONObject.toJSONString(o);
                            stringJoiner.add(s);
                        }
                    }
                }else {
                    if (arg == null){
                        stringJoiner.add("null");
                    }else {
                        stringJoiner.add(arg.toString());
                    }
                }
            }
        }
        log.info("输入参数 : {}",stringJoiner.toString());
    }

    /**
     * 使用aop获取返回值
     * @param r
     */
    @AfterReturning(pointcut = "execution(* *..*ArcCommonServiceImpl.getRedisKey*(..))",returning = "r")
    public void afterReturning(Object r){
        log.info("afterReturning : 生成的缓存key值 {}",r);
    }


    
    //获取异常对象
    @AfterThrowing(pointcut = "execution(* *..*ArcCommonServiceImpl.getRedisKey*(..))",throwing = "e")
    public void afterThrowing(Exception e){
        log.info("afterThrowing : 异常后置通知 ",e);
    }



}