package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description TOP档案访问目标模型
 * <AUTHOR>
 * @Date 2021/06/17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopArcAccessTargetModel implements Comparable{

    /**
     * 档案类型
     */
    private Integer arcType;

    /**
     * 档案账号
     */
    private String arcAccount;

    /**
     * 档案账号类型
     */
    private String arcAccountType;

    /**
     * 协议类型
     */
    private Integer dataType;

    /**
     * 来源国家
     */
    private String srcCountry;

    /**
     * 来源城市
     */
    private String srcCity;

    /**
     * 最早关联时间
     */
    private Long earliestTime;

    /**
     * 最后关联时间
     */
    private Long latestTime;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        TopArcAccessTargetModel virtualAccountCountModel = (TopArcAccessTargetModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
