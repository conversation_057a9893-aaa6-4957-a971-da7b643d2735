package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 虚拟账户统计信息模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VirtualStatisticInfoModel {

    /**
     * 活跃总数
     */
    private long totalActive;

    /**
     * 近一周活跃数
     */
    private long oneWeekActive;

    /**
     * 活动区域总数
     */
    private long totalActiveAreaNum;

    /**
     * 近一周活动区域数
     */
    private long oneWeekActiveAreaNum;

    /**
     * 通联区域总数
     */
    private long totalContactArea;

    /**
     * 近一周通联区域数
     */
    private long oneWeekContactArea;

    /**
     * 昵称总数
     */
    private long totalNickNameNum;

    /**
     * 近一周昵称数
     */
    private long oneWeekNickNameNum;

    /**
     * 密码总数
     */
    private long totalPasswordNum;

    /**
     * 近一周密码数
     */
    private long oneWeekPasswordNum;

    /**
     * 附件总数
     */
    private long totalAttachNum;

    /**
     * 近一周附件数
     */
    private long oneWeekAttachNum;
}
