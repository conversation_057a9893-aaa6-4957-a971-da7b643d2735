package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description 常用附件后缀模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseAttachSuffixModel implements Comparable{

    /**
     * 附件后缀
     */
    private String attachSuffix;

    /**
     * 统计次数
     */
    private long num;

    /**
     * 最晚时间
     */
    private long latestTime;

    @Override
    public int compareTo(Object o) {
        CommonUseAttachSuffixModel virtualAccountCountModel = (CommonUseAttachSuffixModel) o;
        int times = ((int)virtualAccountCountModel.num - (int)this.num);
        return times;
    }
}
