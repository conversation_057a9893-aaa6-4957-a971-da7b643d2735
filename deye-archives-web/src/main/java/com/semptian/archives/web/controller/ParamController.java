package com.semptian.archives.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.service.ParamBiz;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.util.ExcelReadUtils;
import com.semptian.archives.web.util.model.ImportTypeDto;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@CrossOrigin
@RequestMapping("/query_param")
@Slf4j
public class ParamController {

    @Resource
    private ParamBiz paramBiz;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    HttpServletRequest request;

    /**
     * 查询通联国家/通联城市
     *
     * @return Object
     */
    @ApiOperation(value = "查询通联国家/通联城市", notes = "查询通联国家/通联城市")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/get_location_list.json")
    public Object getLocationList(HttpServletRequest request) {
        String lang = CurrentUserUtil.getLang(request);
        List<JSONObject> locationList = paramBiz.getLocationList(lang);
        if(null != locationList){
            return locationList;
        }else{
            return ReturnModel.getInstance().setCode(-1).setMsg("服务调用失败");
        }
    }


    /**
     * 查询应用分类/应用
     *
     * @return Object
     */
    @ApiOperation(value = "查询应用分类/应用", notes = "查询应用分类/应用")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/get_app_list.json")
    public Object getAppList() {
        return paramBiz.getAppList();
    }

    /**
     * 查询IM协议应用类型
     *
     * @return Object
     */
    @ApiOperation(value = "查询IM协议应用类型", notes = "查询IM协议应用类型")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @GetMapping("/get_im_app_type_list.json")
    public Object getIMAppTypeList() {
        return paramBiz.getIMAppType();
    }

    /**
     * 导入应用分类/应用
     *
     * @return Object
     */
    @ApiOperation(value = "导入配置", notes = "导入")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @PostMapping("/set_param_list.json")
    public Object setAppList(@ApiParam(name = "file", value = "导入的文件对象") MultipartFile file, @ApiParam(name = "type")String type) {
        String[] title = {"type", "name"};
        Workbook sheets = ExcelReadUtils.readExcel(file);
        List<ImportTypeDto> listBean = ExcelReadUtils.getListBean(ImportTypeDto.class, sheets, 1, 0, title);
        return paramBiz.setTypeList(listBean,type);
    }


    /**
     * 获取所有协议类型
     */
    @GetMapping("/get_protocol_list.json")
    @ApiOperation(value = "获取所有协议类型", httpMethod = "GET", response = ReturnModel.class)
    @ApiImplicitParams({@ApiImplicitParam(name = "type", value = "协议分类 1=LIS相关协议 2=NF相关协议 3=VPN相关协议", dataType = "Integer", required = true, paramType = "query")})
    @OperateLog
    public Object getProtocolList(@RequestParam(name = "type") Integer type) {
        String lang = CurrentUserUtil.getLang(request);
        return ReturnModel.getInstance().ok(arcCommonService.getProtocolList(type, lang));
    }


}
