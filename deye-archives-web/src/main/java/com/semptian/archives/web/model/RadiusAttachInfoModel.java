package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description Radius附件信息模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RadiusAttachInfoModel {

    /**
     * 附件后缀统计信息
     */
    private List<CommonUseAttachSuffixModel> suffix;

    /**
     * 附件类型分布信息
     */
    private List<CommonUseAttachTypePercentModel> type;
}
