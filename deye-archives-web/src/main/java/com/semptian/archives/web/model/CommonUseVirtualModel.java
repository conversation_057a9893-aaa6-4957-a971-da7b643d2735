package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 常用虚拟账户模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseVirtualModel implements Comparable{

    /**
     * 虚拟账户
     */
    private String virtualAccount;

    /**
     * 虚拟账户类型
     */
    private String virtualAccountType;

    /**
     * 协议类型
     */
    private String dataType;

    /**
     * 统计次数
     */
    private long num;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    @Override
    public int compareTo(Object o) {
        CommonUseVirtualModel virtualAccountCountModel = (CommonUseVirtualModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
