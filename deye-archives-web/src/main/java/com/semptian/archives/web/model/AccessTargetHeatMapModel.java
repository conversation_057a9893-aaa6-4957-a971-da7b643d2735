package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 访问目标热力图模型
 * <AUTHOR>
 * @Date 2021/06/17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccessTargetHeatMapModel implements Comparable{

    /**
     * 经度
     */
    private String srcIpLongitude;

    /**
     * 纬度
     */
    private String srcIpLatitude;

    /**
     * 国家
     */
    private String srcCountry;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        AccessTargetHeatMapModel virtualAccountCountModel = (AccessTargetHeatMapModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
