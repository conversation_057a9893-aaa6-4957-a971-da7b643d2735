package com.semptian.archives.web.common.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * @Description 对feign进行拦截，在请求头中加入需要的参数
 * <AUTHOR>
 * @Date 2020/4/22
 */

@Component
public class FeignHeaderInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        /**
         * 获取头部的参数信息，在feign中赋值
         */
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if(requestAttributes==null){
            return;
        }
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        if(headerNames != null){
            String userId = httpServletRequest.getHeader("userId");
            String appId = httpServletRequest.getHeader("appId");
            requestTemplate.header("userId",userId);
            requestTemplate.header("appId",appId);
            requestTemplate.header("serverName","deye-archives-web");
        }
    }
}
