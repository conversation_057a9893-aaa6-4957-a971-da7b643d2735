package com.semptian.archives.web.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.service.impl.ArcIndexServiceImpl;
import com.semptian.base.service.ReturnModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: SunQi
 * @create: 2022/05/26
 * desc: 档案权限校验拦截器
 **/
@Component
public class ArcPermissionInterceptor implements HandlerInterceptor {
    @Autowired
    ArcIndexServiceImpl arcIndexService;

    @Value("${archive.communityDigAppId.appId:119}")
    private String communityDigAppId;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(!(handler instanceof HandlerMethod)){
            return true;
        }
        //只拦截标注@ArcPermissionCheck注解的请求
        HandlerMethod handlerMethod = (HandlerMethod)handler;
        ArcPermissionCheck methodAnnotation = handlerMethod.getMethodAnnotation(ArcPermissionCheck.class);
        if(methodAnnotation == null){
            return true;
        }
        String userId = request.getHeader("userId");
        if (StringUtils.isBlank(userId)){
            userId = CurrentUserUtil.getUserId(request);
        }
        String appId = request.getHeader("appId");
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        if(StringUtils.isBlank(userId) || StringUtils.isBlank(appId)){
            ReturnModel result = ReturnModel.getInstance().error().setData("param is blank");
            response.getWriter().append(JSON.toJSONString(result,SerializerFeature.WriteMapNullValue));
            return false;
        }
        //如果是群组调用全息接口不拦截
        if(appId.equals(communityDigAppId)){
            return true;
        }
        //TODO:调用全息接口获取用户档案权限
        List<Integer> userArchivePermission = arcIndexService.getUserArchivePermission(appId, userId);
        if(userArchivePermission == null || userArchivePermission.isEmpty()){
            ReturnModel result = ReturnModel.getInstance().error().setMsg(I18nUtils.getMessage("arc.permission.error"));
            response.getWriter().append(JSON.toJSONString(result,SerializerFeature.WriteMapNullValue));
            return false;
        }
        request.setAttribute("permission",userArchivePermission);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }
}
