package com.semptian.archives.web.common.enums;

/**
 * <AUTHOR>
 * @data 2022/6/10 16:27
 * 概要：
 * xxxxxx
 */
public enum DataPermissionReturnCode {
    NO_PERMISSION(11304,"Insufficient permissions.");


    private Integer code;
    private String msg;

    DataPermissionReturnCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getCode() {
        return code;
    }

}
