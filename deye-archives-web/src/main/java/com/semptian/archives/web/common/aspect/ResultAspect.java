package com.semptian.archives.web.common.aspect;

import com.alibaba.druid.util.StringUtils;
import com.semptian.base.service.ICode;
import com.semptian.base.service.ReturnModel;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 对请求进行切面处理,统一返回指定格式消息
 *
 * <AUTHOR>
 */
@Component
@Aspect
public class ResultAspect {

    @Autowired
    private HttpServletRequest request;

    /**
     * 控制层方法切入点
     */
    @Pointcut("execution(* com..controller..*(..)) && ( @annotation(org.springframework.web.bind.annotation.RequestMapping) || @annotation(org.springframework.web.bind.annotation.GetMapping)|| @annotation(org.springframework.web.bind.annotation.DeleteMapping) || @annotation(org.springframework.web.bind.annotation.PutMapping)  || @annotation(org.springframework.web.bind.annotation.PostMapping))")
    public void controllerMethodPointcut() {
    }

    /**
     * 控制层返回值处理类
     *
     * @param joinPoint 切点
     * @return 结果
     */
    @Around("controllerMethodPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed = joinPoint.proceed();

        //判断返回支，处理不同情况的返回值

        if (proceed instanceof Void) {
            return proceed;
        }

        if (proceed instanceof ModelAndView) {
            return proceed;
        }

        Enumeration<String> parameterNames = request.getParameterNames();
        Map<String, String> map = new HashMap(16);
        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            map.put(key, request.getParameter(key));
        }

        ReturnModel returnModel;

        if (proceed instanceof String) {
            Controller annotation = joinPoint.getTarget().getClass().getAnnotation(Controller.class);
            if (annotation != null) {
                return proceed.toString();
            } else {
                returnModel = ReturnModel.getInstance().ok().setData(proceed);
            }
        } else if (proceed instanceof ModelAndView) {
            return proceed;
        } else if (proceed instanceof ReturnModel) {
            returnModel = (ReturnModel) proceed;
        } else if (proceed instanceof ICode) {
            ICode code = (ICode) proceed;
            returnModel = ReturnModel.getInstance().setCode(code.getCode()).setMsg(code.getMsg());
        } else {
            returnModel = ReturnModel.getInstance().ok().setData(proceed);
        }

        if (!map.isEmpty()) {
            returnModel.setParams(map);
        }
//        String traceId = request.getHeader(CommonConstant.LOG_TRACE_ID);
//        if (!StringUtils.isEmpty(traceId)) {
//            returnModel.setTraceId(traceId);
//        }

        return returnModel;
    }

}
