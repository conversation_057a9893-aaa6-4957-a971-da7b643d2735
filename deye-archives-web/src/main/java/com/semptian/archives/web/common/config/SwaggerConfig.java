package com.semptian.archives.web.common.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

/**
 * Swagger 配置
 *
 * <AUTHOR>
 * @date 2018/11/5 0005 15:23
 */
@EnableSwagger2
@Configuration
public class SwaggerConfig {

    @Bean
    public Docket createRestApi() {
        ParameterBuilder userIdPar = new ParameterBuilder();
        ParameterBuilder appIdPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        userIdPar.name("userId").description("用户id").modelRef(new ModelRef("string")).parameterType("header").required(false).build();
        appIdPar.name("appId").description("登录授权id").modelRef(new ModelRef("string")).defaultValue("92").parameterType("header").required(false).build();
        pars.add(userIdPar.build());
        pars.add(appIdPar.build());
        //添加head参数end
        Predicate<RequestHandler> predicate = input -> {
            //只有添加了ApiOperation注解的method才在API中显示
            return input.isAnnotatedWith(ApiOperation.class);
        };

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(predicate::test)
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(pars);
    }

    private ApiInfo apiInfo() {

        return new ApiInfoBuilder()
                .title("全息档案接口规范")
                .description("本界面为\"全息档案\"开发接口规范 如有疑问 请联系********************")
                .termsOfServiceUrl("")
                .version("V6.2.1")
                .build();
    }

}
