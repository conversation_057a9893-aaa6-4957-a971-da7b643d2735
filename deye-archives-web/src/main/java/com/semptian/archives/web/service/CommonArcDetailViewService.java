package com.semptian.archives.web.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: caoyang
 * @create: 2021/06/13
 * @desc: 通用档案详情
 **/
public interface CommonArcDetailViewService {

    /**
     * 根据列表导出CSV文件
     * @param localLang
     * @param userId
     * @param method
     * @param listType
     * @param choose
     * @param contentField
     */
    void importWriteCsv(String localLang, String userId, String method, Integer listType, Integer choose, Integer size, List<Map<String, Object>> contentField, HttpServletRequest request, HttpServletResponse response);

}
