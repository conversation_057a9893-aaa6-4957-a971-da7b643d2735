package com.semptian.archives.web.controller;

import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.FixedIpArcDetailViewService;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.ArcStatisticModel;
import com.semptian.archives.web.service.model.ArchiveModel;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.base.service.CustomException;
import com.semptian.base.service.ReturnModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 固定IP档案访问控制器
 * @author: lmz
 * @date: 2022/8/30 15:29
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/fixed_ip")
@Slf4j
@Api
public class FixedIpArcController {

    @Resource
    private FixedIpArcDetailViewService fixedIpArcDetailViewService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @GetMapping("/exists.json")
    @ApiOperation(value = "固定IP档案是否存在", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> fixedIpExists(@RequestParam("ip") String ip) {
        return ReturnModel.getInstance().ok(true);
    }

    @PostMapping("/get_arc_statistical_dimensions.json")
    @ApiOperation(value = "固定Ip批量档案维度统计", httpMethod = "POST", response = ReturnModel.class)
    public Object queryArcInfo(@RequestBody ArcStatisticModel arcStatisticModel,
                               HttpServletRequest request
    ) {
        Integer index = arcStatisticModel.getIndex();
        Integer isAll = arcStatisticModel.getIsAll();
        List<ArchiveModel> archiveModelList = arcStatisticModel.getArchiveModels();
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /fixed_ip/get_arc_statistical_dimensions.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        if (index > 13|| index < 1) {
            throw new CustomException(ArcServerReturnCode.PARAM_IS_INVALID.getCode(), I18nUtils.getMessage("index must be one of 1、2、3 、4、5 or 6 "));
        }

        Object result = commonArcServiceFacade.getStatisticalDimensions(ArcTypeEnum.FIXED_IP, archiveModelList, isAll, index, userId);

        if (result == null) {
            return ReturnModel.getInstance().error("Arc not exists!");
        } else {
            return ReturnModel.getInstance().ok(result);
        }
    }

    @GetMapping("/query_fixed_ip_status.json")
    @ApiOperation(value = "固定Ip知识库获取建档状态", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> queryFixedIpStatus(@RequestParam(name = "ipList") String ipList
    ) {
        String[] ip = ipList.split(",");
        if (ip.length == 0) {
            log.warn("request /fixed_ip/query_fixed_ip_status.json fixed ip empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        return fixedIpArcDetailViewService.queryFixedIpStatus(ip);
    }
}
