package com.semptian.archives.web.controller;

import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.RadiusArcDetailViewService;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.archives.web.service.service.impl.PhoneArcServiceImpl;
import com.semptian.base.service.CustomException;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @Description radius archive detail controller used to support data archives api
 * <AUTHOR>
 * @Date 2021/06/02 16:15
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/radius_arc_detail")
@Slf4j
@Api
public class RadiusArcDetailController {

    @Resource
    private RadiusArcDetailViewService radiusArcDetailViewService;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @Resource
    PhoneArcServiceImpl phoneArcService;

    @Resource
    private HttpServletRequest request;

    @ApiOperation("关联虚拟账号Tab页")
    @GetMapping("/get_virtual_account_info.json")
    public Object getVirtualAccountInfo(@RequestParam String arcAccount, @RequestParam String arcAccountType,
                                        @RequestParam(required = false) String dataType, @RequestParam(required = false) String keyword,
                                        @RequestParam(required = false) String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (StringUtils.isBlank(arcAccount) ) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        String lang = CurrentUserUtil.getLang(request);
        return phoneArcService.getVirtualAccountInfo(arcAccount,arcAccountType, dataType, keyword,ArcTypeEnum.RADIUS,createDay, dateModel, pageWarpEntity, lang);
    }

    @PostMapping("/get_arc_statistical_dimensions.json")
    @ApiOperation(value = "radius批量档案维度统计", httpMethod = "POST", response = ReturnModel.class)
    public Object queryArcInfo(@RequestBody ArcStatisticModel arcStatisticModel,
                               HttpServletRequest request
    ) {
        Integer index = arcStatisticModel.getIndex();
        Integer isAll = arcStatisticModel.getIsAll();
        List<ArchiveModel> archiveModelList = arcStatisticModel.getArchiveModels();
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /radius_arc_detail/get_arc_statistical_dimensions.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        if (index > 13 || index < 1) {
            throw new CustomException(ArcServerReturnCode.PARAM_IS_INVALID.getCode(), I18nUtils.getMessage("index must be one of 1、2、3 、4、5 or 6 "));
        }

        Object result = commonArcServiceFacade.getStatisticalDimensions(ArcTypeEnum.RADIUS, archiveModelList, isAll, index, userId);

        if (result == null) {
            return ReturnModel.getInstance().error("Arc not exists!");
        } else {
            return ReturnModel.getInstance().ok(result);
        }
    }

    @GetMapping("/get_frequent_app_info.json")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "arcAccountType", value = "认证类型", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "topSize", value = "是否阻断", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "createDay",value = "建档时间",dataType = "String",paramType = "query")
    })
    @ApiOperation(value = "radius档案详情页常用应用", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentAppInfo(@RequestParam(name = "arcId") String arcId,
                                    @RequestParam(name = "arcAccount") String arcAccount,
                                    @RequestParam(name = "arcAccountType", required = false, defaultValue = "1020001") Integer authType,
                                    @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize,
                                    @RequestParam(name = "createDay",required = false) String createDay,
                                    HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /radius_arc_detail/get_frequent_app_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcAccountType(String.valueOf(authType)).topSize(topSize).createDay(createDay).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(AuthTypeEnum.RADIUS.getType().equals(authType) ? ArcTypeEnum.RADIUS : ArcTypeEnum.FIXED_IP, ctx, AccessTargetTopEnum.APP));
    }

    @GetMapping("/get_frequent_account_info.json")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "arcAccountType", value = "认证类型", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "isBlock", value = "是否阻断", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "topSize", value = "是否阻断", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "createDay",value = "建档时间",dataType = "String",paramType = "query")
    })
    @ApiOperation(value = "radius档案详情页常用账号", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentAccountInfo(@RequestParam(name = "arcId") String arcId,
                                    @RequestParam(name = "arcAccount") String arcAccount,
                                    @RequestParam(name = "arcAccountType", required = false, defaultValue = "1020001") Integer authType,
                                    @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize,
                                    @RequestParam(name = "createDay",required = false) String createDay,
                                    HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /radius_arc_detail/get_frequent_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        String lang = CurrentUserUtil.getLang(request);

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcAccountType(String.valueOf(authType)).topSize(topSize).createDay(createDay).lang(lang).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(AuthTypeEnum.RADIUS.getType().equals(authType) ? ArcTypeEnum.RADIUS : ArcTypeEnum.FIXED_IP, ctx, AccessTargetTopEnum.VIRTUAL_ACCOUNT));
    }

    /**
     * radius档案认证记录
     */
    @GetMapping("/auth_record.json")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "arcAccount", value = "档案账号", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "ip", value = "ip地址，可模糊查询", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dateOption", value = "日期快捷选项", dataType = "Integer", required = true, paramType = "query"),
            @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "页码", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "get radius auth record  ", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object authRecord(@RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "ip", required = false) String ip, DateModel dateModel, PageWarpEntity pageWarpEntity, HttpServletRequest request) {
        if (StringUtils.isBlank(arcAccount)) {
            log.warn("request /radius_arc_detail/auth_record.json   arcAccount empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        return ReturnModel.getInstance().ok(radiusArcDetailViewService.getRadiusAuthRecord(arcAccount, ip, dateModel, pageWarpEntity));
    }

    /**
     * VPN分析-使用次数日趋势
     */
    @PostMapping("/vpn_use_day_trend.json")
    @OperateLog
    public Object vpnUseDayTrend(@RequestBody VpnTrendModel vpnTrendModel) {
        Object result = arcCommonService.vpnUseDayTrend(vpnTrendModel);
        return ReturnModel.getInstance().ok(result);
    }


    /**
     * 使用次数小时趋势
     */
    @PostMapping("/vpn_use_hour_trend.json")
    @OperateLog
    public Object vpnUseHourTrend(@RequestBody VpnTrendModel vpnTrendModel) {
        Object result = arcCommonService.vpnUseHourTrend(vpnTrendModel);
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 使用次数排名及趋势
     */
    @PostMapping("/vpn_use_rank.json")
    @OperateLog
    public Object vpnUseRank(@RequestBody VpnTrendModel vpnTrendModel) {
        Object result = arcCommonService.vpnUseRank(vpnTrendModel);
        return ReturnModel.getInstance().ok(result);
    }

}
