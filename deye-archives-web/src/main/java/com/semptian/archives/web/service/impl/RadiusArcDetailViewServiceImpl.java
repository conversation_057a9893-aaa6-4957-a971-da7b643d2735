package com.semptian.archives.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.semptian.archives.web.core.common.enums.BusinessCodeEnum;
import com.semptian.archives.web.service.RadiusArcDetailViewService;
import com.semptian.archives.web.service.common.util.CommonParamUtil;
import com.semptian.archives.web.service.model.AuthRecordModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.ArcCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * radius档案详情实现类
 * @author: caoyang
 * @create: 2021/06/08
 **/
@Service
@Slf4j
public class RadiusArcDetailViewServiceImpl implements RadiusArcDetailViewService {

    @Resource
    private ArcCommonService arcCommonService;

    @Override
    public Object getRadiusAuthRecord(String arcAccount, String ip, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        CommonParamUtil.ParamMap paramMap = CommonParamUtil.buildCommonTimeParam(dateModel);
        paramMap.put("radius", arcAccount);

        //默认给空
        paramMap.put("ip", "");
        if (StrUtil.isNotEmpty(ip)) {
            paramMap.put("ip", StrUtil.format("AND (ip LIKE '%{}%')", ip));
        }

        //查询认证记录信息
        pageWarpEntity.setSortField("capture_time");
        pageWarpEntity.setSortType(1);
        List<Map<String, Object>> list = arcCommonService.getCommonServiceListResult(BusinessCodeEnum.RADIUS_AUTH_RECORD.getValue(), paramMap, pageWarpEntity);
        Long total = arcCommonService.getCommonServiceCountResult(BusinessCodeEnum.RADIUS_AUTH_RECORD.getValue(), paramMap, true);

        //查询总时长
        Double totalHour = arcCommonService.getCommonServiceSumResult(BusinessCodeEnum.RADIUS_AUTH_RECORD_DURATION_STATISTICS.getValue(), paramMap);
        //查询本周时长
        paramMap.put("start_day", DateUtil.beginOfWeek(DateUtil.date(), false).toDateStr());
        paramMap.put("end_day", DateUtil.date().toDateStr());
        Double weekHour = arcCommonService.getCommonServiceSumResult(BusinessCodeEnum.RADIUS_AUTH_RECORD_DURATION_STATISTICS.getValue(), paramMap);

        //数据组装返回
        AuthRecordModel authRecordModel = new AuthRecordModel();
        authRecordModel.setWeekHour(weekHour);
        authRecordModel.setTotalHour(totalHour);

        AuthRecordModel.Detail detail = new AuthRecordModel.Detail();
        authRecordModel.setDetail(detail);

        detail.setTotal(total.intValue());

        if (CollUtil.isNotEmpty(list)) {
            List<AuthRecordModel.AuthRecordDetail> authRecordDetailList = list.stream().map(map -> {
                AuthRecordModel.AuthRecordDetail authRecordDetail = new AuthRecordModel.AuthRecordDetail();
                authRecordDetail.setCaptureTime((Long) map.get("captureTime"));
                authRecordDetail.setAction((String) map.get("action"));
                authRecordDetail.setOnlineHour((Double) map.get("onlineHour"));
                authRecordDetail.setIp((String) map.get("ip"));
                authRecordDetail.setInternalIp((String) map.get("framed_ip"));
                authRecordDetail.setPortRange((String) map.get("port_range"));
                authRecordDetail.setMac((String) map.get("mac"));
                return authRecordDetail;
            }).collect(Collectors.toList());

            detail.setList(authRecordDetailList);
        }
        return authRecordModel;
    }
}
