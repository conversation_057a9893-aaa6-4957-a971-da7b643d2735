package com.semptian.archives.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.CommonArcDetailViewService;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.redis.config.RedisConfig;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Description common archive detail controller used to support data archives api
 * <AUTHOR>
 * @Date 2021/06/13
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/common_arc_detail")
@Slf4j
@Api
public class CommonArcDetailController {

    @Resource
    private CommonArcDetailViewService commonArcDetailViewService;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedisConfig redisConfig;

    @Resource
    HttpServletRequest request;

    @Resource
    private ArchivesInfoService archivesInfoService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @GetMapping("/get_active_trend.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案账号类型", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "behavior_type", value = "行为类型", dataType = "Integer", paramType = "query")})
    @ApiOperation(value = "get archive statistics info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryActiveTrend(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcType") Integer arcType, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "arcAccountType", required = false) String arcAccountType, @RequestParam(name = "dataType", required = false) Integer dataType, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "createDay", required = false) String createDay, @RequestParam(name = "behavior_type", required = false, defaultValue = "3") Integer behaviorType, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /common_arc_detail/get_active_trend.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcAccountType(arcAccountType).arcType(String.valueOf(arcType)).dataType(String.valueOf(dataType)).startDay(startDay).endDay(endDay).createDay(createDay).behaviorType(behaviorType).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getActiveTrend(ctx));
    }


    @GetMapping("/get_period_statistics.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案账号类型", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query")})
    @ApiOperation(value = "get period statistics info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryPeriodStatistics(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcType") Integer arcType, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "arcAccountType", required = false) String arcAccountType, @RequestParam(name = "dataType", required = false) Integer dataType, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "createDay", required = false) String createDay, @RequestParam(name = "behavior_type", required = false, defaultValue = "3") Integer behaviorType, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /common_arc_detail/get_period_statistics.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }


        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcAccountType(arcAccountType).arcType(String.valueOf(arcType)).dataType(String.valueOf(dataType)).startDay(startDay).endDay(endDay).behaviorType(behaviorType).createDay(createDay).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getPeriodStatistics(ctx));
    }

    @GetMapping("/get_arc_info_by_id.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query")})
    @ApiOperation(value = "get arc info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getArcInfo(@RequestParam(name = "arcId", required = false) String arcId, @RequestParam(name = "arcAccount", required = false) String arcAccount, @RequestParam(name = "arcType", required = false) Integer arcType, @RequestParam(name = "arcAccountType", required = false) String arcAccountType, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /common_arc_detail/get_arc_info_by_id.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        String lang = CurrentUserUtil.getLang(request);
        ArcInfoModel arcInfoModel = archivesInfoService.getArcInfoById(arcId, arcAccount, arcType, arcAccountType, userId, lang);
        return ReturnModel.getInstance().ok(arcInfoModel);
    }

    @GetMapping("/get_communication_area.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案账号类型", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "startMonth", value = "开始月份", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "endMonth", value = "结束月份", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "dateType", value = "日期类型", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "isActive", value = "是否活跃区域，0：通联，1：活跃", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query")})
    @ApiOperation(value = "get communication info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryCommunicationArea(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcType") Integer arcType, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "arcAccountType", required = false) String arcAccountType, @RequestParam(name = "dataType", required = false) Integer dataType, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "startMonth", required = false) Integer startMonth, @RequestParam(name = "endMonth", required = false) Integer endMonth, @RequestParam(name = "dateType", required = false, defaultValue = "0") Integer dateType, @RequestParam(name = "isActive", required = false, defaultValue = "0") Integer isActive, @RequestParam(name = "createDay", required = false) String createDay, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /common_arc_detail/get_communication_area.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        if (dateType == 0 && (startDay == null || endDay == null)) {
            log.warn("request /common_arc_detail/get_communication_area.json startDay or endDay empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        if (dateType == 0 && startDay.compareTo(endDay) > 0) {
            log.warn("request /common_arc_detail/get_communication_area.json startDay after endDay!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_INVALID.getMsg()));
        }
        if (dateType == 1 && (startMonth == null || endMonth == null)) {
            log.warn("request /common_arc_detail/get_communication_area.json startMonth or endMonth empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        if (dateType == 1 && startMonth > endMonth) {
            log.warn("request /common_arc_detail/get_communication_area.json startMonth after endMonth!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_INVALID.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_INVALID.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcAccountType(arcAccountType).arcType(String.valueOf(arcType)).dateType(dateType).dataType(String.valueOf(dataType)).isActive(isActive).startDay(startDay).endDay(endDay).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getCommunicationArea(ctx));
    }

    @GetMapping("/redisCacheClear.json")
    @ApiOperation(value = "redis 前缀删除", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel redisCacheClear(@RequestParam(name = "redisPrefix", required = false) String redisPrefix) {
        if (StringUtils.isBlank(redisPrefix)) {
            redisPrefix = redisConfig.getCachePrefix() + "*";
        }
        Set keys = redisTemplate.keys(redisPrefix);
        redisTemplate.delete(keys);
        String result = "删除缓存数量：" + keys.size();
        return ReturnModel.getInstance().ok(result);
    }

    @ApiOperation(value = "列表导出", notes = "列表导出")
    @ApiResponses(value = {@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/import_write_csv_by_list.json")
    public void importWriteCsv(@RequestBody ImportCsvList importCsvList, HttpServletRequest request, HttpServletResponse response) {

        //1、判断userId、ImportCsvList 是否为空
        //2、判断是哪个方法
        //3、判断是哪个列表listType（知道csv第一行的输出格式）
        //4、判断是哪种导出方式
        //5、io流读写输出
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.error("user not logged in {}", userId);
            return;
        }
        String method = "";
        if (importCsvList.getMethod() != null && StringUtils.isNotBlank(importCsvList.getMethod())) {
            method = importCsvList.getMethod();
        } else {
            log.error("method name is not exist {} or json is null {}", importCsvList.getMethod(), importCsvList);
            return;
        }
        Integer listType = 0;
        if (importCsvList.getListType() != null && importCsvList.getListType() != 0) {
            listType = importCsvList.getListType();
        } else {
            log.info("list type is not exist {} ", importCsvList.getListType());
//            return;
        }
        Integer choose = 0;
        if (importCsvList.getChoose() != null && importCsvList.getChoose() != 0) {
            choose = importCsvList.getChoose();
        } else {
            log.error("choose is error {}", importCsvList.getChoose());
            return;
        }
        Integer size = 0;
        if (choose == 3) {
            if (importCsvList.getSize() != null && importCsvList.getSize() != 0) {
                size = importCsvList.getSize();
            } else {
                log.error("size is not equals to {}", importCsvList.getSize());
                return;
            }
        }
        List<Map<String, Object>> contentField = importCsvList.getContentField();
        String localLang = CurrentUserUtil.getLang(request);
        commonArcDetailViewService.importWriteCsv(localLang, userId, method, listType, choose, size, contentField, request, response);
    }

    /**
     * 接口功能：根据协议类型获取LIS数据下钻明细表头列，会根据语种返回国际化后结果。
     */
    @ApiOperation(value = "LIS数据下钻明细表头列", notes = "LIS数据下钻明细表头列")
    @ApiImplicitParams({@ApiImplicitParam(name = "dataType", value = "协议类型", required = true, dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "isAll", value = "是否返回全部字段 1 = 是，返回全部字段（默认）0 = 否，返回部分核心业务字段", defaultValue = "1", dataType = "Integer", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/lis/get_headers.json")
    public Object getLisHeaders(HttpServletRequest request, @RequestBody DrillDownHeaderModel drillDownHeaderModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        String lang = CurrentUserUtil.getLang(request);

        return ReturnModel.getInstance().ok(arcCommonService.getHeaders(drillDownHeaderModel.getDataType(), drillDownHeaderModel.getIsAll(), DrillDownSceneEnum.LIS_DETAIL, lang, true));
    }

    /**
     * 接口功能：根据协议类型获取NF数据下钻明细表头列，会根据语种返回国际化后结果。
     */
    @ApiOperation(value = "NF数据下钻明细表头列", notes = "NF数据下钻明细表头列")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/nf/get_headers.json")
    public Object getNfHeaders(HttpServletRequest request, @RequestBody DrillDownHeaderModel drillDownHeaderModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        String lang = CurrentUserUtil.getLang(request);
        Integer isCustomField = drillDownHeaderModel.getIsCustomField();
        String userId = CurrentUserUtil.getUserId(request);

        if (isCustomField != null && isCustomField == 1) {
            drillDownHeaderModel.setIsAll(1);
        }

        return ReturnModel.getInstance().ok(arcCommonService.getHeaders(drillDownHeaderModel.getDataType(), drillDownHeaderModel.getIsAll(),
                DrillDownSceneEnum.NF_DETAIL, lang, true, isCustomField, userId));
    }

    @ApiOperation(value = "通用数据下钻明细表头列", notes = "数据下钻明细表头列")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/get_headers.json")
    public Object getHeaders(HttpServletRequest request, @RequestBody DrillDownHeaderModel drillDownHeaderModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        String lang = CurrentUserUtil.getLang(request);
        String userId = CurrentUserUtil.getUserId(request);
        return ReturnModel.getInstance().ok(arcCommonService.getHeaders(drillDownHeaderModel.getDataType(), drillDownHeaderModel.getIsAll(), null,
                lang, true, drillDownHeaderModel.getIsCustomField(), userId));
    }

    /**
     * 根据协议类型和相关参数查询LIS数据下钻明细
     */
    @ApiOperation(value = "LIS数据下钻明细", notes = "LIS数据下钻明细")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/lis/get_data_detail.json")
    public Object getLisDataDetail(HttpServletRequest request, @RequestBody DrillDownModel drillDownModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }

        drillDownModel.setLang(CurrentUserUtil.getLang(request));
        drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.LIS_DETAIL);
        return ReturnModel.getInstance().ok(arcCommonService.getDrillDownDataDetail(drillDownModel));
    }


    /**
     * 根据协议类型和相关参数查询NF数据下钻明细
     */
    @ApiOperation(value = "NF数据下钻明细", notes = "NF数据下钻明细")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/nf/get_data_detail.json")
    public Object getNfDataDetail(HttpServletRequest request, @RequestBody DrillDownModel drillDownModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        drillDownModel.setLang(CurrentUserUtil.getLang(request));
        if (drillDownModel.getDataType() == 1222 || drillDownModel.getDataType() == 1223) {
            drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.VPN_DETAIL);
        } else {
            drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.NF_DETAIL);
        }
        return ReturnModel.getInstance().ok(arcCommonService.getDrillDownDataDetail(drillDownModel));
    }

    @ApiOperation(value = "通用数据下钻明细", notes = "数据下钻明细")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/get_data_detail.json")
    public Object getDataDetail(HttpServletRequest request, @RequestBody DrillDownModel drillDownModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }

        String lang = CurrentUserUtil.getLang(request);
        String userId = CurrentUserUtil.getUserId(request);
        drillDownModel.setLang(lang);
        return ReturnModel.getInstance().ok(arcCommonService.getDrillDownDataDetail(drillDownModel, userId));
    }

    @ApiOperation(value = "协议数据量统计", notes = "数据下钻明细")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @GetMapping("/get_data_type_count.json")
    public Object getDataTypeCount(
            String arcAccount,
            DateModel dateModel

    ) {
        String lang = CurrentUserUtil.getLang(request);
        return ReturnModel.getInstance().ok(arcCommonService.getDataTypeCount(arcAccount, dateModel, lang));
    }

    /**
     * 按时间范围统计协议(默认全部)的日行为次数总计趋势
     */
    @ApiOperation(value = "按时间范围统计协议(默认全部)的日行为次数总计趋势", notes = "按时间范围统计协议(默认全部)的日行为次数总计趋势")
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型，1=radius档案，5=号码档案， 8=固定ip档案", required = true, dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案认证账号类型", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "固定IP档案创建日期，判断固定IP档案查询数据时间范围", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型，多个用逗号拼接，默认为0", required = false, dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/lis/get_trend.json")
    public Object getLisTrend(HttpServletRequest request, @RequestBody TrendAndDistributeModel trendAndDistributeModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        trendAndDistributeModel.setDataSourceEnum(DataSourceEnum.LIS);
        trendAndDistributeModel.setLang(CurrentUserUtil.getLang(request));
        return ReturnModel.getInstance().ok(arcCommonService.getTrend(trendAndDistributeModel));
    }

    /**
     * 按时间范围统计协议(默认全部)的日行为次数总计趋势
     */
    @ApiOperation(value = "按时间范围统计协议(默认全部)的日行为次数总计趋势", notes = "按时间范围统计协议(默认全部)的日行为次数总计趋势")
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型，1=radius档案，5=号码档案， 8=固定ip档案", required = true, dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案认证账号类型", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "固定IP档案创建日期，判断固定IP档案查询数据时间范围", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型，多个用逗号拼接，默认为0", required = false, dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/nf/get_trend.json")
    public Object getNfTrend(HttpServletRequest request, @RequestBody TrendAndDistributeModel trendAndDistributeModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        trendAndDistributeModel.setDataSourceEnum(DataSourceEnum.NF);
        trendAndDistributeModel.setLang(CurrentUserUtil.getLang(request));
        return ReturnModel.getInstance().ok(arcCommonService.getTrend(trendAndDistributeModel));
    }


    /**
     * LIS记录-协议数据分布
     */
    @ApiOperation(value = "LIS记录-协议数据分布", notes = "LIS记录-协议数据分布")
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型，1=radius档案，5=号码档案， 8=固定ip档案", required = true, dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案认证账号类型", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "固定IP档案创建日期，判断固定IP档案查询数据时间范围", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型，多个用逗号拼接，默认为0", required = false, dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/lis/get_distribution.json")
    public Object getLisDistribution(HttpServletRequest request, @RequestBody TrendAndDistributeModel trendAndDistributeModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        trendAndDistributeModel.setDataSourceEnum(DataSourceEnum.LIS);
        trendAndDistributeModel.setLang(CurrentUserUtil.getLang(request));
        return ReturnModel.getInstance().ok(arcCommonService.getDistribution(trendAndDistributeModel));
    }

    @ApiOperation(value = "NF记录-协议数据分布", notes = "NF记录-协议数据分布")
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型，1=radius档案，5=号码档案， 8=固定ip档案", required = true, dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "档案账号", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "档案认证账号类型", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期，格式‘yyyy-MM-dd’", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "固定IP档案创建日期，判断固定IP档案查询数据时间范围", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "dataType", value = "协议类型，多个用逗号拼接，默认为0", required = false, dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/nf/get_distribution.json")
    public Object getNfDistribution(HttpServletRequest request, @RequestBody TrendAndDistributeModel trendAndDistributeModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        trendAndDistributeModel.setDataSourceEnum(DataSourceEnum.NF);
        trendAndDistributeModel.setLang(CurrentUserUtil.getLang(request));
        return ReturnModel.getInstance().ok(arcCommonService.getNfActionDistribution(trendAndDistributeModel));
    }

    /**
     * 内容提取-高频词排名
     */
    @ApiOperation(value = "内容提取-高频词排名", notes = "内容提取-高频词排名")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/get_high_frequency_words_rank.json")
    public Object getHighFrequencyWordsRank(HttpServletRequest request, @RequestBody HighFrequencyWordsModel highFrequencyWordsModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        highFrequencyWordsModel.setLang(CurrentUserUtil.getLang(request));

        return ReturnModel.getInstance().ok(arcCommonService.getHighFrequencyWordsRank(highFrequencyWordsModel));
    }

    /**
     * 内容提取-高频词每日趋势
     */
    @ApiOperation(value = "内容提取-高频词每日趋势", notes = "内容提取-高频词每日趋势")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/get_high_frequency_words_trend.json")
    public Object getHighFrequencyWordsTrend(HttpServletRequest request, @RequestBody HighFrequencyWordsModel highFrequencyWordsModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        highFrequencyWordsModel.setLang(CurrentUserUtil.getLang(request));
        return ReturnModel.getInstance().ok(arcCommonService.getHighFrequencyWordsTrend(highFrequencyWordsModel));
    }

    /**
     * 档案编辑(修改)
     */
    @ApiOperation(value = "档案编辑(修改)", notes = "档案编辑(修改)")
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案ID", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", required = true, dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "name", value = "档案名称", required = true, dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/arc_name_update.json")
    public Object arcUpdate(HttpServletRequest request, @RequestBody ArcInfoModel arcInfoModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        String userId = CurrentUserUtil.getUserId(request);
        return ReturnModel.getInstance().ok(arcCommonService.arcUpdateBasicInfo(userId, arcInfoModel.getArcId(), arcInfoModel.getArcType(), arcInfoModel.getName()));
    }

    @GetMapping("/count_virtual_account_by_data_type.json")
    @ApiOperation(value = "count virtual account info by page ", httpMethod = "GET", response = ReturnModel.class)
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "认证类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "keyword", value = "搜索关键词，支持模糊查询", dataType = "String", required = false, paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期，格式'yyyy-MM-dd'", dataType = "String", required = false, paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期，格式'yyyy-MM-dd'", dataType = "String", required = false, paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query"),})
    @OperateLog
    public Object countVirtualAccountByDataType(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String authAccount, @RequestParam(name = "arcType") String arcType, @RequestParam(name = "keyword", required = false) String keyWord, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "createDay", required = false) String createDay, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request/radius_arc_detail/count_virtual_account_by_data_type.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(authAccount).keyWord(keyWord).arcType(arcType).startDay(startDay).endDay(endDay).createDay(createDay).lang(CurrentUserUtil.getLang(request)).build();


        return ReturnModel.getInstance().ok(commonArcServiceFacade.countDataType(ArcTypeEnum.getArcTypeByKey(arcType), ctx));
    }

    @GetMapping("/get_app_list.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccountType", value = "认证类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "isBlock", value = "数据类型 0非阻断 1阻断", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "content", value = "搜索内容", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "onPage", value = "onPage", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "size", value = "size", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "sortField", value = "排序字段,默认排序为降序，支持时间及关联次数", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "sortType", value = "0：正序；1：逆序 (默认)", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query"),})
    @ApiOperation(value = "get app list", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getAppList(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String authAccount, @RequestParam(name = "arcType") String arcType, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "isBlock", required = false, defaultValue = "0") Integer isBlock, @RequestParam(name = "content", required = false, defaultValue = "") String content, @RequestParam(name = "onPage", required = false, defaultValue = "1") Integer onPage, @RequestParam(name = "size", required = false, defaultValue = "10") Integer size, @RequestParam(name = "sortField", required = false, defaultValue = "value") String sortField, @RequestParam(name = "sortType", required = false, defaultValue = "1") Integer sortType, @RequestParam(name = "createDay", required = false) String createDay, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /common_arc_detail/get_localhost_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(authAccount).arcType(arcType).startDay(startDay).endDay(endDay).isBlock(isBlock != 0).onPage(onPage).size(size).sortField(sortField).keyWord(content).sortType(sortType).createDay(createDay).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAppList(ArcTypeEnum.getArcTypeByKey(arcType), ctx));
    }

    @GetMapping("/get_app_time_info.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "isBlock", value = "数据类型 0行为 1阻断", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "showDimension", value = "展示类型 0分类 1应用", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "appName", value = "应用名称", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "appType", value = "应用类型", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "topSize", value = "topN", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "interval", value = "时间间隔", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query")})
    @ApiOperation(value = "get app time info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getAppTimeInfo(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "arcType") String arcType, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "isBlock", required = false, defaultValue = "0") Integer isBlock, @RequestParam(name = "showDimension", required = false, defaultValue = "0") Integer showDimension, @RequestParam(name = "appName", required = false, defaultValue = "") String appName, @RequestParam(name = "appType", required = false, defaultValue = "") String appType, @RequestParam(name = "topSize", required = false, defaultValue = "10") Integer topSize, @RequestParam(name = "interval", required = false, defaultValue = "1") Integer interval, @RequestParam(name = "createDay", required = false) String createDay, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /radius_arc_detail/get_app_img_table.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcType(arcType).startDay(startDay).endDay(endDay).isBlock(isBlock != 0).showDimension(showDimension).appName(appName).appType(appType).interval(interval).topSize(topSize).createDay(createDay).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAppTimeInfo(ArcTypeEnum.getArcTypeByKey(arcType), ctx));

    }

    @GetMapping("/get_app_img_table.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "isBlock", value = "数据类型 0非阻断 1阻断", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "showDimension", value = "展示类型 0应用分类 1应用", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "appName", value = "应用名称", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "appType", value = "应用类型", dataType = "String", paramType = "query"), @ApiImplicitParam(name = "topSize", value = "topN", dataType = "Integer", paramType = "query"), @ApiImplicitParam(name = "createDay", value = "建档时间", dataType = "String", paramType = "query")})
    @ApiOperation(value = "get app img table", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getAppImgTable(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "arcType") String arcType, @RequestParam(name = "startDay", required = false) String startDay, @RequestParam(name = "endDay", required = false) String endDay, @RequestParam(name = "isBlock", required = false, defaultValue = "0") Integer isBlock, @RequestParam(name = "showDimension", required = false, defaultValue = "0") Integer showDimension, @RequestParam(name = "appName", required = false, defaultValue = "") String appName, @RequestParam(name = "appType", required = false, defaultValue = "") String appType, @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize, @RequestParam(name = "createDay", required = false) String createDay, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /radius_arc_detail/get_app_img_table.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcType(arcType).startDay(startDay).endDay(endDay).isBlock(isBlock != 0).showDimension(showDimension).appName(appName).appType(appType).topSize(topSize).createDay(createDay).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAppImgTable(ArcTypeEnum.getArcTypeByKey(arcType), ctx));

    }

    /**
     * 查询档案账号是否是重要目标
     */
    @GetMapping("/is_important_target.json")
    @ApiOperation(value = "查询档案账号是否是重要目标", httpMethod = "GET", response = ReturnModel.class)
    @ApiImplicitParams({@ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query")})
    @OperateLog
    public Object isImportantTarget(@RequestParam(name = "arcType") Integer arcType, @RequestParam(name = "arcAccount") String arcAccount) {
        return arcCommonService.isImportantTarget(arcType, arcAccount);
    }

    @ApiOperation(value = "获取头像", notes = "获取头像")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户 id", dataType = "String", paramType = "query")
    })
    @PostMapping("/get_multipart_file.json")
    public Object getMultipartFile(@RequestBody JSONObject archive) {
        return Lists.newArrayList();
    }

    @GetMapping("/get_attach_info.json")
    @ApiImplicitParams({@ApiImplicitParam(name = "arcAccount", value = "档案账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "startDay", value = "开始日期", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "endDay", value = "结束日期", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "onPage", value = "当前页，默认第一页", dataType = "Int", paramType = "query"), @ApiImplicitParam(name = "size", value = "每页显示数量，默认10", dataType = "Int", paramType = "query")})
    @ApiOperation(value = "get attach info", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryAttachInfo(@RequestParam(name = "arcAccount") String arcAccount, @RequestParam(value = "arcAccountType",required = false, defaultValue = "") String arcAccountType, @RequestParam(name = "archiveType") String archiveType, @RequestParam(value = "createDay", required = false, defaultValue = "") String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity, HttpServletRequest request) {

        dateModel.setDateOption(DateOptionEnum.CUSTOM.getValue());
        if (StringUtils.isEmpty(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("latestRelationTime");
            pageWarpEntity.setSortType(1);
        }

        String lang = CurrentUserUtil.getLang(request);
        ArcContext ctx = ArcContext.builder().arcAccount(arcAccount).createDay(createDay).arcAccountType(arcAccountType).dateModel(dateModel).pageWarpEntity(pageWarpEntity).arcType(archiveType).lang(lang).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAttachInfo(ctx, BusinessTypeEnum.OVERVIEW));
    }

    /**
     *
     * @param keyword 查询关键字
     * @param type
     * 查询账号类型
     * 0 = 全部
     * 1 = 固网Radius
     * 2 = 移动网Radius
     * 3 = 固定IP
     * @param arcAccount
     * @param arcAccountType
     * @param dateModel
     * @param pageWarpEntity
     * @return
     */
    @GetMapping("/auth_record.json")
    @ResponseBody
    @ApiOperation(value = "认证记录列表查询", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object authRecord(String keyword, String type, String arcAccount, String arcAccountType,
                             DateModel dateModel, PageWarpEntity pageWarpEntity) {
        String lang = CurrentUserUtil.getLang(request);
        return ReturnModel.getInstance().ok(arcCommonService.authRecord(keyword, type, arcAccount, arcAccountType,lang, dateModel, pageWarpEntity));
    }


    @GetMapping("/get_tags_top.json")
    @ApiOperation(value = "get tags top", httpMethod = "GET", response = ReturnModel.class)
    @ApiImplicitParams({ @ApiImplicitParam(name = "arcAccount", value = "档案账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcType", value = "档案类型", dataType = "Integer", required = true, paramType = "query")})
    @OperateLog
    public Object getTagsTop(@RequestParam(name = "arcAccount") String authAccount, @RequestParam(name = "arcType") Integer arcType, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request/radius_arc_detail/count_virtual_account_by_data_type.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        String lang = CurrentUserUtil.getLang(request);
        return ReturnModel.getInstance().ok(arcCommonService.getTagTopList(arcType, authAccount, lang));
    }

    @GetMapping("/get_tags_list.json")
    @ApiOperation(value = "get tag list", httpMethod = "GET", response = ReturnModel.class)
   @OperateLog
    public Object getTagList(String keyword,  String arcAccount, Integer arcType,
                             DateModel dateModel, PageWarpEntity pageWarpEntity, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request/radius_arc_detail/count_virtual_account_by_data_type.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        String lang = CurrentUserUtil.getLang(request);
        return ReturnModel.getInstance().ok(arcCommonService.getTagList(arcType, arcAccount, keyword, dateModel, pageWarpEntity, lang));
    }

    @ApiOperation(value = "明细下钻-列定制-保存")
    @PostMapping("/save_custom_columns.json")
    public Object saveCustomColumns(
                                   @RequestBody CustomColumnsModel columns,
                                   HttpServletRequest request) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }

        String userId = CurrentUserUtil.getUserId(request);

        // 调用服务层保存自定义列配置
        boolean result = arcCommonService.saveCustomColumns(columns, userId);
        
        return ReturnModel.getInstance().ok(result);
    }
}
