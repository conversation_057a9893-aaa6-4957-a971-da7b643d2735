package com.semptian.archives.web.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RadiusAppListModel implements Comparable<RadiusAppListModel> {
    private String authAccount;
    private String virtualAccount;
    private String appName;
    private String appType;
    private long latestTime;
    private long earliestTime;
    private long value;

    @Override
    public int compareTo(RadiusAppListModel o) {
        return (int) (o.value - this.value);
    }
}
