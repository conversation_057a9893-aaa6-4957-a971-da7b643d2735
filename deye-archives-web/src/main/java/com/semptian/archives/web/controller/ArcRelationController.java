package com.semptian.archives.web.controller;

import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.core.common.enums.ArcTypeEnum;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.interceptor.ArcPermissionCheck;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.common.util.PermissionUtil;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcRelationService;
import com.semptian.archives.web.service.service.UserRelationDataService;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Currency;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26 14:32
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/arc_relation")
@Slf4j
@Api
public class ArcRelationController {

    @Resource
    private HttpServletRequest request;

    @Resource
    private ArcRelationService arcRelationService;

    @Resource
    private UserRelationDataService userRelationDataService;

    @GetMapping("/get_relation_expansion.json")
    @ApiOperation("关系扩线")
    public Object getRelationExpansion(RelationExpansionRequestModel relationExpansionRequestModel) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isEmpty(userId)) {
            log.warn("request /arc_info/arc_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        String lang = CurrentUserUtil.getLang(request);

        if (ArcTypeEnum.PHONE.getKey().equals(relationExpansionRequestModel.getArcType())) {
            String account = relationExpansionRequestModel.getAuthAccount();

            if (StringUtils.isNotBlank(account)) {
                account = account.replace("+", "");
                relationExpansionRequestModel.setAuthAccount(account);
            }
        }

        Object result = arcRelationService.relationExpansion(relationExpansionRequestModel, userId, lang);
        return new ReturnModel<>().ok(result);
    }

    @PostMapping("/has_more_relation.json")
    @ApiOperation("关系扩线")
    public Object hasMoreRelation(@RequestBody HasMoreRelationModel hasMoreRelationModel) {
        Object result = arcRelationService.hasMoreRelation(hasMoreRelationModel);
        return new ReturnModel<>().ok(result);
    }

    /**
     * 传档案ID时，表示从档案的关系扩线页面调用
     * 传开始日期、结束日期、关键字时表示从扩线记录页面调用
     *
     * @param arcId 档案ID
     * @return 扩线版本信息
     */
    @GetMapping("/get_relation_info_version.json")
    @OperateLog
    @ApiOperation("扩线版本记录接口")
    @ArcPermissionCheck
    public Object getRelationInfoVersion(@RequestParam(value = "arcId", required = false) String arcId, DateModel dateModel, @RequestParam(value = "keyWord", required = false) String keyWord, PageWarpEntity pageWarpEntity) {
        String userId = request.getHeader("userId");
        if (StringUtils.isEmpty(userId)) {
            log.warn("request /arc_relation/get_relation_expansion.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        //获取用户对应档案权限
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        return ReturnModel.getInstance().ok(userRelationDataService.getRelationInfoVersion(arcId, userId, dateModel, keyWord, pageWarpEntity, permissionList));
    }

    /**
     * 保存关系扩线快照信息
     */
    @PostMapping("/relation_config/config/save.json")
    @ApiOperation("保存关系扩线快照信息")
    @OperateLog
    public Object save(@RequestBody UserRelationDataModel userRelationData, HttpServletRequest request) {
        //需要做用户隔离
        String userId = request.getHeader("userId");
        if (StringUtils.isBlank(userId)) {
            log.warn("request /arc_relation//relation_config/config/save.json userId is empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        userRelationData.setUserId(userId);
        return userRelationDataService.saveRelationData(userRelationData);
    }

    @ApiOperation("保存社会关系")
    @GetMapping("/connect_create.json")
    @OperateLog
    public Object connectCreate(@RequestParam(value = "arc1Id") String arc1Id, @RequestParam(value = "arc2Id") String arc2Id, @RequestParam(value = "connectType") String connectType, @RequestParam(value = "expansionArcId") String expansionArcId) {
        if (StringUtils.isBlank(arc1Id) || StringUtils.isBlank(arc2Id) || StringUtils.isBlank(connectType) || StringUtils.isBlank(expansionArcId)) {
            return ReturnModel.getInstance().error().setMsg("arc1Id or arc1Id or connectType not be null");
        }

        String lang = CurrentUserUtil.getLang(request);
        return userRelationDataService.connectCreate(arc1Id, arc2Id, connectType, expansionArcId, lang);
    }

    /**
     * 保存节点备注信息
     */
    @PostMapping("/remark_save.json")
    @OperateLog
    @ApiOperation("保存关系扩线备注信息")
    public Object remarkSave(@RequestBody ArcRemarkEntity arcRemark, HttpServletRequest request) {
        String userId = request.getHeader("userId");
        return userRelationDataService.remarkSave(arcRemark, userId);
    }

    /**
     * 根据版本ID批量删除扩线版本
     */
    @PostMapping("/remark_delete.json")
    @ApiOperation("删除关系扩线版本")
    public Object delete(@RequestBody DeleteModel deleteModel) {
        userRelationDataService.delete(deleteModel);
        return ReturnModel.getInstance().ok();
    }

    @GetMapping("/get_user_expansion_config.json")
    @ApiOperation("关系扩线-用户扩线配置查询")
    public Object getUserExpansionConfig(HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /arc_relation/get_user_expansion_config.json userId is empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        Object result = arcRelationService.getUserExpansionConfig(userId);
        return new ReturnModel<>().ok(result);
    }

    @PostMapping("/save_user_expansion_config.json")
    @ApiOperation("关系扩线-用户扩线配置保存")
    public Object saveUserExpansionConfig(@RequestBody UserExpansionConfigModel userExpansionConfig, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /arc_relation/save_user_expansion_config.json userId is empty!");
            return ReturnModel.getInstance()
                    .setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode())
                    .setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        userExpansionConfig.setCreateUser(userId);
        Object result = arcRelationService.saveUserExpansionConfig(userExpansionConfig);
        return new ReturnModel<>().ok(result);
    }
}
