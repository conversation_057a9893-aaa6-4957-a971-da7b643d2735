package com.semptian.archives.web.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.common.enums.*;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.archives.web.service.service.impl.PhoneArcServiceImpl;
import com.semptian.base.service.CustomException;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/3 10:40
 **/
@Slf4j
@RestController
@RequestMapping(value = "/phone_arc_detail")
public class PhoneArcController {

    @Resource
    PhoneArcServiceImpl phoneArcService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private HttpServletRequest request;

    @ApiOperation(value = "号码批量档案维度统计", httpMethod = "POST", response = ReturnModel.class)
    @PostMapping("/get_arc_statistical_dimensions.json")
    public Object getArcDetailInfo(@RequestBody ArcStatisticModel arcStatisticModel, HttpServletRequest request) {
        Integer index = arcStatisticModel.getIndex();
        Integer isAll = arcStatisticModel.getIsAll();
        List<ArchiveModel> archiveModelList = arcStatisticModel.getArchiveModels();
        String userId = CurrentUserUtil.getUserId(request);
        if (index > 13 || index < 1) {
            throw new CustomException(ArcServerReturnCode.PARAM_IS_INVALID.getCode(), I18nUtils.getMessage("index must be one of 1、2、3 、4 or 5 "));
        }

        Object result = commonArcServiceFacade.getStatisticalDimensions(ArcTypeEnum.PHONE, archiveModelList, isAll, index, userId);

        if (result == null) {
            return ReturnModel.getInstance().error("Arc not exists!");
        } else {
            return ReturnModel.getInstance().ok(result);
        }
    }


    @ApiOperation("通联归属区域")
    @GetMapping("/get_communication_area.json")
    public Object getCommunicationArea(@RequestParam(value = "arcAccount") String arcAccount, DateModel dateModel) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        String lang = CurrentUserUtil.getLang(request);
        return phoneArcService.getCommunicationArea(arcAccount, dateModel, lang);
    }

    @ApiOperation("常活动区域(基站信息)")
    @GetMapping("/get_frequent_active_area.json")
    public Object getFrequentActiveArea(@RequestParam(value = "arcAccount") String arcAccount, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        return phoneArcService.getFrequentActiveArea(arcAccount, dateModel, pageWarpEntity);
    }

    @ApiOperation("实时轨迹")
    @GetMapping("/real_time_trajectory.json")
    public Object realTimeTrajectory(@RequestParam String arcAccount, @RequestParam(required = false) String keyword, String baseStationNo,
                                     DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");

        return phoneArcService.realTimeTrajectory(arcAccount, keyword, baseStationNo, dateModel, pageWarpEntity);
    }

    /**
     * 历史轨迹
     * @param trajectoryType 1 天 2 周 3 月
     */
    @ApiOperation("历史轨迹")
    @GetMapping("/his_trajectory.json")
    public Object hisTrajectory(@RequestParam String arcAccount, @RequestParam(required = false) String keyword,
                                 String baseStationNo, String trajectoryType,
                                DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (StringUtils.isBlank(arcAccount) || StringUtils.isBlank(trajectoryType)) {
            return ReturnModel.getInstance().error().setMsg("*** arcAccount and trajectoryType 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");

        return phoneArcService.hisTrajectory(arcAccount, keyword, baseStationNo, trajectoryType, dateModel, pageWarpEntity);
    }

    @ApiOperation("区域热力图")
    @GetMapping("/area_hot_graph.json")
    public Object areaHotGraph(@RequestParam String arcAccount, @RequestParam(required = false) String keyword,
                               String startHour, String endHour, String baseStationNo,
                               DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");

        return phoneArcService.areaHotGraph(arcAccount, keyword,  startHour, endHour, baseStationNo, dateModel, pageWarpEntity);
    }

    @ApiOperation("关联虚拟账号Tab页")
    @GetMapping("/get_virtual_account_info.json")
    public Object getVirtualAccountInfo(@RequestParam String arcAccount, @RequestParam(required = false) String dataType, @RequestParam(required = false) String keyword, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        String lang = CurrentUserUtil.getLang(request);
        return phoneArcService.getVirtualAccountInfo(arcAccount, "", dataType, keyword, ArcTypeEnum.PHONE, null, dateModel, pageWarpEntity, lang);
    }

    @ApiOperation("通联记录-通联次数日趋势")
    @GetMapping("/communication_trend.json")
    public Object communicationTrend(@RequestParam String arcAccount, @RequestParam(required = false) String callTag, DateModel dateModel) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        return phoneArcService.communicationTrend(arcAccount, callTag, dateModel);
    }

    @ApiOperation("通联记录-号码通联次数排名Top10")
    @GetMapping("/communication_rank.json")
    public Object communicationRank(@RequestParam String arcAccount, @RequestParam(required = false) String callTag, DateModel dateModel) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        return phoneArcService.communicationRank(arcAccount, callTag, dateModel);
    }

    @ApiOperation("鉴权计费记录-网络类型分布")
    @GetMapping("/network_type_distribution.json")
    public Object networkTypeDistribution(@RequestParam String arcAccount, DateModel dateModel) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        return phoneArcService.networkTypeDistribution(arcAccount, dateModel);
    }

    @ApiOperation("鉴权计费记录-使用设备排名Top10")
    @GetMapping("/use_device_rank.json")
    public Object useDeviceRank(@RequestParam String arcAccount, DateModel dateModel) {
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        arcAccount = arcAccount.replace("+", "");
        return phoneArcService.useDeviceRank(arcAccount, dateModel);
    }

    /**
     * 计费明细表头列
     */
    @ApiOperation(value = "鉴权计费明细表头列", notes = "鉴权计费明细表头列")
    @ApiResponses({@ApiResponse(code = 1, message = "请求成功"), @ApiResponse(code = 0, message = "请求失败")})
    @PostMapping("/authentication_billing/get_headers.json")
    public Object getNfHeaders(HttpServletRequest request, @RequestBody AuthBillingHeaderModel authBillingHeaderModel) {
        if (StringUtils.isBlank(CurrentUserUtil.getUserId(request))) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.CURRENT_USERID_BLANK.getCode()).setMsg(I18nUtils.getMessage("current.userid.blank"));
        }
        String lang = CurrentUserUtil.getLang(request);
        return ReturnModel.getInstance().ok(arcCommonService.getHeaders(authBillingHeaderModel.getAction(), authBillingHeaderModel.getIsAll(), DrillDownSceneEnum.AUTHENTICATION_BILLING, lang, true));
    }

    /**
     * 号码档案鉴权计费记录-计费明细数据
     * 接口功能：查询计费数据明细，查询搜索系统location和移动网radius资源库获取
     */
    @PostMapping("/authentication_billing/get_data_detail.json")
    @ApiOperation(value = "号码档案鉴权计费记录-计费明细数据", httpMethod = "POST", response = ReturnModel.class)
    @OperateLog
    public Object getCommunicationDataDetail(HttpServletRequest request,@RequestBody AuthBillingDetailModel authBillingDetailModel) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /phone_arc_detail/get_data_detail.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        String lang = CurrentUserUtil.getLang(request);
        return phoneArcService.getCommunicationDataDetail(authBillingDetailModel,lang);
    }


    @GetMapping("/get_frequent_number_info.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "topSize", value = "是否阻断", dataType = "Integer", paramType = "query")})
    @ApiOperation(value = "号码档案通联号码Top5", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentNumber(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /phone_arc_detail/get_frequent_number_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).topSize(topSize).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.PHONE, ctx, AccessTargetTopEnum.PHONE));
    }

    @GetMapping("/get_frequent_account_info.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "topSize", value = "是否阻断", dataType = "Integer", paramType = "query")})
    @ApiOperation(value = "号码档案详情页常用账号", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentAccountInfo(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /phone_arc_detail/get_frequent_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        String lang = CurrentUserUtil.getLang(request);

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).topSize(topSize).lang(lang).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.PHONE, ctx, AccessTargetTopEnum.VIRTUAL_ACCOUNT));
    }

    @GetMapping("/get_frequent_app_info.json")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案id", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "arcAccount", value = "认证账号", dataType = "String", required = true, paramType = "query"), @ApiImplicitParam(name = "topSize", value = "是否阻断", dataType = "Integer", paramType = "query")})
    @ApiOperation(value = "radius档案详情页常用应用", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentAppInfo(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "topSize", required = false, defaultValue = "5") Integer topSize, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /phone_arc_detail/get_frequent_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).topSize(topSize).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.PHONE, ctx, AccessTargetTopEnum.APP));
    }


    /**
     * 归属区域信息Tab页
     */
    @GetMapping("/connect_area_belong.json")
    @ApiOperation(value = "归属区域信息Tab页", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object connectAreaBelong(@RequestParam(value = "arcAccount") String arcAccount, DateModel dateModel, PageWarpEntity pageWarpEntity, HttpServletRequest request) {
        String userId = request.getHeader("userId");
        if (StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().error().setMsg("*** arcId and phone 参数不能为空 ***");
        }
        if (StringUtils.isBlank(userId)) {
            log.warn("request /count_connect_account.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }
        String lang = CurrentUserUtil.getLang(request);
        return phoneArcService.connectAreaBelong(arcAccount, dateModel, pageWarpEntity, lang);
    }

    /**
     * 通联归属区域-通联号码个数下钻
     */
    @GetMapping("/connect_area_belong_drill_down.json")
    @ApiOperation(value = "通联归属区域-通联号码个数下钻", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object connectAreaBelongDrillDown(@RequestParam("phoneNumber") String phoneNumber,
                                             @RequestParam("countryCode") String countryCode,
                                             PageWarpEntity pageWarpEntity,
                                             DateModel dateModel) {
        if (StringUtils.isBlank(phoneNumber) || StringUtils.isBlank(countryCode)) {
            return ReturnModel.getInstance().error().setMsg("*** phoneNumber and countryCode 参数不能为空 ***");
        }
        return phoneArcService.connectAreaBelongDrillDown(phoneNumber, countryCode, pageWarpEntity, dateModel);
    }

    /**
     * 获取通联区域明细下钻
     *
     */
    @GetMapping("/get_connect_area_detail_info.json")
    @OperateLog
    public ReturnModel getConnectAreaDetailInfo(@RequestParam("phoneNumber") String srcNumber, @RequestParam("country") String country, @RequestParam("startDay") String startDay, @RequestParam("endDay") String endDay, @RequestParam(value = "onPage", required = false, defaultValue = "1") Integer onPage, @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        Object result = phoneArcService.getPhoneConnectAreaDetailInfo(srcNumber, country, startDay, endDay, onPage, size);
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * VPN分析-使用次数日趋势
     */
    @PostMapping("/vpn_use_day_trend.json")
    @OperateLog
    public Object vpnUseDayTrend(@RequestBody VpnTrendModel vpnTrendModel) {
        Object result = arcCommonService.vpnUseDayTrend(vpnTrendModel);
        return ReturnModel.getInstance().ok(result);
    }


    /**
     * 使用次数小时趋势
     */
    @PostMapping("/vpn_use_hour_trend.json")
    @OperateLog
    public Object vpnUseHourTrend(@RequestBody VpnTrendModel vpnTrendModel) {
        Object result = arcCommonService.vpnUseHourTrend(vpnTrendModel);
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 使用次数排名及趋势
     */
    @PostMapping("/vpn_use_rank.json")
    @ApiOperation(value = "使用次数排名及趋势", httpMethod = "POST", response = ReturnModel.class)
    @OperateLog
    public Object vpnUseRank(@RequestBody VpnTrendModel vpnTrendModel) {
        Object result = arcCommonService.vpnUseRank(vpnTrendModel);
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 号码档案内容提取-银行卡信息
     */
    @PostMapping("/get_bank_card_info.json")
    @OperateLog
    public Object getBankCardInfo(@RequestBody HighFrequencyWordsModel highFrequencyWordsModel) {
        Object result = arcCommonService.getBankCardInfo(highFrequencyWordsModel);
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 号码档案内容提取-航班信息
     */
    @PostMapping("/get_flight_info.json")
    @OperateLog
    public Object getFlightInfo(@RequestBody HighFrequencyWordsModel highFrequencyWordsModel) {
        Object result = arcCommonService.getFlightInfo(highFrequencyWordsModel);
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 号码档案-活跃地查询条件
     */
    @GetMapping("/get_active_city_info.json")
    @OperateLog
    public Object getActiveCityInfo() {
        Object result = phoneArcService.getActiveCityInfo();
        return ReturnModel.getInstance().ok(result);
    }

    /**
     * 号码档案-号码机主信息
     *
     * @param arcInfoModel 档案信息
     * @return 号码机主信息
     */
    @PostMapping("/get_phone_owner_info.json")
    @OperateLog
    @ApiImplicitParams({@ApiImplicitParam(name = "arcId", value = "档案ID", dataType = "String", required = true, paramType = "query")})
    @ApiOperation(value = "get phone owner info", httpMethod = "POST", response = ReturnModel.class)
    public Object getPhoneOwnerInfo(@RequestBody ArcInfoModel arcInfoModel) {
        return ReturnModel.getInstance().ok(phoneArcService.getPhoneOwnerInfo(arcInfoModel));
    }

    /**
     *
     * @param arcAccount
     * @param hourInterval 间隔小时
     * @param statisticsType 1-时长统计,2-次数统计
     * @param top 传值则取 top N, 否则取全量数据
     * @param dateModel
     * @return
     */
    @GetMapping("/area_hot/time_statistics.json")
    @ApiOperation(value = "活动位置-区域热力图-时段统计", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object timeStatistics(
            String arcAccount,
            String hourInterval,
            String statisticsType,
            String top,
            String keyword,
            DateModel dateModel
    ) {
        String startDay = dateModel.getStartDay();
        String endDay = dateModel.getEndDay();

        DateTime startDayTime = DateUtil.beginOfDay(DateUtil.parse(startDay));
        DateTime endDayTime = DateUtil.endOfDay(DateUtil.parse(endDay));

        dateModel.setStartDay(startDayTime.toString("yyyy-MM-dd HH:mm:ss"));
        dateModel.setEndDay(endDayTime.toString("yyyy-MM-dd HH:mm:ss"));

        Object result = phoneArcService.timeStatistics(arcAccount, hourInterval, statisticsType, top, keyword, dateModel);
        return ReturnModel.getInstance().ok(result);
    }

    @GetMapping("area_hot/stay_record.json")
    @ApiOperation(value = "活动位置-活动区域表-驻留记录", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object stayRecord(
            String arcAccount,
            String stationNo,
            String startHour,
            String endHour,
            DateModel dateModel,
            PageWarpEntity pageWarpEntity
    ) {
        Object result = phoneArcService.stayRecord(arcAccount, stationNo, startHour, endHour, dateModel, pageWarpEntity);
        return ReturnModel.getInstance().ok(result);
    }

    @GetMapping("get_imei_info.json")
    @ApiOperation(value = "查询设备列表", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object getImeiInfo(
            String arcAccount,
            String keyword,
            PageWarpEntity pageWarpEntity
    ) {
        Object result = phoneArcService.getImeiInfo(arcAccount, keyword, pageWarpEntity);
        return ReturnModel.getInstance().ok(result);
    }
}
