package com.semptian.archives.web.model;

import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/31 14:26
 **/
@Data
public class EmailPhoneExtractQueryModel {

    String arcAccount;
    String sense;

    /**
     * 开始页
     */
    @ApiModelProperty(value = "开始页")
    private Integer onPage = 1;

    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小")
    private Integer size = 10;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String sortField;

    /**
     * 1 - DESC , 0 - ASC
     */
    @ApiModelProperty(value = "1 - DESC , 0 - ASC")
    private Integer sortType;

    @ApiModelProperty(value = "开始日期")
    String startDay;

    /**
     * 结束日期 (支持多种格式 详见 cn.hutool.core.date.DateUtil.parse)
     */
    @ApiModelProperty(value = "结束日期")
    String endDay;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始月份")
    String startMonth;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束月份")
    String endMonth;

    /**
     * 时间快捷选项，枚举值为
     * 1=近7天
     * 2=近15天
     * 3=本月
     * 4=上月
     * 5=近二月
     * 6=过去7天
     * 7=过去15天
     * 0=自定义选项
     * 选择日期非自定义选项时不用传递日期参数，以枚举值日期为准
     */
    @ApiModelProperty(value = "时间快捷选项 1=近7天 2=近15天 3=本月 4=上月 5=近二月 6=过去7天 7=过去15天 0=自定义选项")
    Integer dateOption=0;
}
