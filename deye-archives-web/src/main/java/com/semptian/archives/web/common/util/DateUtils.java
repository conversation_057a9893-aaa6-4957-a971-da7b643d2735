package com.semptian.archives.web.common.util;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * Date: 2020/09/08 15:00
 * Description:
 */
@Slf4j
public class DateUtils {
    private static Logger logger = LoggerFactory.getLogger(DateUtils.class);

    private static String dayFormat = "yyyyMMdd";

    private static String monthFormat = "yyyyMM";

    public static final String dateFormat = "yyyy-MM-dd";

    public static Long getOneDayEnd(int index){
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.set(Calendar.HOUR_OF_DAY , 23);
        calendarEnd.set(Calendar.MINUTE , 59);
        calendarEnd.set(Calendar.SECOND , 59);
        calendarEnd.set(Calendar.MILLISECOND , 000);
        calendarEnd.add(Calendar.DATE,index);
        Date time = calendarEnd.getTime();
        return time.getTime();
    }

    public static Long getOneDayStart(Date date, int index){
        Calendar calendarStart = Calendar.getInstance();
        calendarStart.set(Calendar.HOUR_OF_DAY , 00);
        calendarStart.set(Calendar.MINUTE , 00);
        calendarStart.set(Calendar.SECOND , 00);
        calendarStart.add(Calendar.DATE,index);
        Date time = calendarStart.getTime();
        return time.getTime();
    }

    public static Long dateMinusDate(Date endDate,Date startDate){
        Long day = (endDate.getTime()-startDate.getTime()+10000)/(3600*24*1000);
        return day;
    }

    public static Date longToDateStart(Long longTime){
        Calendar calendarStart = Calendar.getInstance();
        calendarStart.setTimeInMillis(longTime);
        calendarStart.set(Calendar.HOUR_OF_DAY , 00);
        calendarStart.set(Calendar.MINUTE , 00);
        calendarStart.set(Calendar.SECOND , 00);
        Date time = calendarStart.getTime();
        return time;
    }

    public static Date longToDateEnd(Long longTime){
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTimeInMillis(longTime);
        calendarEnd.set(Calendar.HOUR_OF_DAY , 23);
        calendarEnd.set(Calendar.MINUTE , 59);
        calendarEnd.set(Calendar.SECOND , 59);
        Date time = calendarEnd.getTime();
        return time;
    }

    /**
     * 返回当前小时的开始时间戳
     *
     * @param date
     * @return
     */
    public static Long getCurrentHourStart(Date date) {
        Calendar calendar = DateUtil.calendar(date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return DateUtil.date(calendar).getTime();
    }

    /**
     * 返回下一小时的开始时间
     *
     * @param date
     * @return
     */
    public static Long getNextHourStart(Date date) {
        Calendar calendar = DateUtil.calendar(DateUtil.offsetHour(date, 1));
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return DateUtil.date(calendar).getTime();
    }

    /**
     * 获取通用日期过滤参数
     * @param date 基准日期
     * @param dayInterval 间隔日期
     * @return 日期过滤参数
     */
    public static Map<String, Object> getParamMap(Date date, int dayInterval) {
        Map<String, Object> params = new HashMap<>(16);
        //开始日期
        String startDay = DateUtil.offsetDay(date, 0 - dayInterval).toString(dateFormat);
        //当前日期
        String captureDay = DateUtil.format(date, dateFormat);
        //结束日期
        String endDay = DateUtil.offsetDay(date, -1).toString(dateFormat );
        Long startTime = getOneDayStart(date, 0);
        params.put("startDay", startDay);
        params.put("endDay", endDay);
        params.put("today", captureDay);
        //修改
        params.put("startTime",startTime);
        params.put("endTime",date.getTime());
        params.put("startCaptureDay",endDay);
        params.put("im_data_type", Integer.parseInt(DataTypeEnum.IM.getKey()));
        params.put("eml_data_type", Integer.parseInt(DataTypeEnum.EMAIL.getKey()));
        return params;
    }

    /**
     * 获取两个日期间所有天
     * @param startDay 开始日期
     * @param endDay 结束日期
     * @return 两个日期间所有天
     */
    public static List<Integer> getDayList(String startDay, String endDay){

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        SimpleDateFormat simpleDayFormat = new SimpleDateFormat(dayFormat);
        List<Integer> dayList = Lists.newArrayList();
        try {
            Date startD = simpleDateFormat.parse(startDay);
            Date endD = simpleDateFormat.parse(endDay);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(startD);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endD);
            while (tempStart.before(tempEnd)){
                dayList.add(Integer.parseInt(simpleDayFormat.format(tempStart.getTime())));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
            if(tempStart.equals(tempEnd)){
                dayList.add(Integer.parseInt(simpleDayFormat.format(tempStart.getTime())));
            }
        } catch (ParseException e) {
            logger.error("invalid day, startDayStr:{}, endDayStr:{}", startDay, endDay);
            return Collections.emptyList();
        }
        return dayList;
    }

    /**
     * 获取两个月份间所有月
     * @param startMonth 开始月份 格式‘yyyyMM’
     * @param endMonth 结束月份 格式‘yyyyMM’
     * @return 两个月份间所有月
     */
    public static List<Integer> getMonthList(int startMonth, int endMonth){

        SimpleDateFormat simpleDayFormat = new SimpleDateFormat(monthFormat);
        List<Integer> monthList = Lists.newArrayList();
        try {
            Date startD = simpleDayFormat.parse(startMonth + "");
            Date endD = simpleDayFormat.parse(endMonth + "");
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(startD);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endD);
            tempStart.set(tempStart.get(Calendar.YEAR), tempStart.get(Calendar.MONTH), 1);
            tempEnd.set(tempEnd.get(Calendar.YEAR), tempEnd.get(Calendar.MONTH), 2);
            while (tempStart.before(tempEnd)){
                monthList.add(Integer.parseInt(simpleDayFormat.format(tempStart.getTime())));
                tempStart.add(Calendar.MONTH, 1);
            }
        } catch (ParseException e) {
            logger.error("invalid month, startMonth:{}, endMonth:{}", startMonth, endMonth);
            return Collections.emptyList();
        }
        return monthList;
    }

    public static void main(String[] args) {
        System.out.println(getMonthList(202011, 202106));
    }
}
