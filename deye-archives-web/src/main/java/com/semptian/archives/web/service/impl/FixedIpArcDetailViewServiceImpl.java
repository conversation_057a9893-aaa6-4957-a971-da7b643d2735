package com.semptian.archives.web.service.impl;

import com.google.common.collect.Lists;
import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.archives.web.service.FixedIpArcDetailViewService;
import com.semptian.archives.web.service.common.enums.ArcQueryEsConditionEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.base.builders.QueryBuilders;
import com.semptian.base.service.ReturnModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 固定Ip方法实现类
 * @author: lmz
 * @since 2022/9/6 21:15
 */
@Service
@Slf4j
public class FixedIpArcDetailViewServiceImpl implements FixedIpArcDetailViewService {

    @Resource
    private ArcCommonService arcCommonService;

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public ReturnModel queryFixedIpStatus(String[] ipList) {
        Map<String, List<String>> result = new HashMap<>(8);
        //查询ES判断当前IP是否建档
        StringBuilder queryString = new StringBuilder("(");

        //组装查询queryString
        for (int i = 0; i < ipList.length; i++) {

            queryString.append(ArcQueryEsConditionEnum.ARCHIVE_NAME.getFieldName())
                    .append(":")
                    .append("\\")
                    .append("\"")
                    .append(ipList[i])
                    .append("\\")
                    .append("\"");
            if (i == ipList.length - 1) {
                queryString.append(")");
            } else {
                queryString.append(" OR ");
            }
        }

        int fixIpArc = Integer.parseInt(ArcTypeEnum.FIXED_IP.getKey());
        Map<String, Object> queryResult = arcCommonService.query(0, ipList.length, QueryBuilders.queryStringQuery("( " + queryString + ")"), null, fixIpArc, Lists.newArrayList(fixIpArc));

        List<ArcEsEntity> arcInfoList = (List) queryResult.get("data");
        List<String> arcFixedIpList = arcInfoList.stream().map(ArcEsEntity::getArchiveName).collect(Collectors.toList());

        result.put("arcIp", arcFixedIpList);
        return ReturnModel.getInstance().ok().setData(result);
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<ArcEsEntity> queryFixedIpList(String[] ipList) {
        Map<String, List<String>> result = new HashMap<>(8);
        //查询ES判断当前IP是否建档
        StringBuilder queryString = new StringBuilder("(");

        //组装查询queryString
        for (int i = 0; i < ipList.length; i++) {

            queryString.append(ArcQueryEsConditionEnum.ARCHIVE_NAME.getFieldName())
                    .append(":")
                    .append("\\")
                    .append("\"")
                    .append(ipList[i])
                    .append("\\")
                    .append("\"");
            if (i == ipList.length - 1) {
                queryString.append(")");
            } else {
                queryString.append(" OR ");
            }
        }

        int fixIpArc = Integer.parseInt(ArcTypeEnum.FIXED_IP.getKey());
        Map<String, Object> queryResult = arcCommonService.query(0, ipList.length, QueryBuilders.queryStringQuery("( " + queryString + ")"), null, fixIpArc, Lists.newArrayList(fixIpArc));

        List<ArcEsEntity> arcInfoList = (List) queryResult.get("data");
        return arcInfoList;
    }
}
