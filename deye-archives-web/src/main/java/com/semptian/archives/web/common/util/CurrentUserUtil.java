package com.semptian.archives.web.common.util;

import com.semptian.archives.web.service.common.enums.LanguageEnum;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2020/10/26 15:34
 * @Description
 **/
public class CurrentUserUtil {

    public static String getUserId(HttpServletRequest request) {
        String userId = "";
        userId = request.getHeader("userId");

        if (StringUtils.isBlank(userId)) {
            userId = request.getParameter("userId");
        }

        return userId;
    }

    public static String getLang(HttpServletRequest request) {
        String lang = request.getHeader("lang");
        if (StringUtils.isBlank(lang)) {
            lang = request.getParameter("lang");
        }

        //避免header中取出的lang和param中lang参数值不一致,对参数值进行归一处理
        return LanguageEnum.getNormalizedLang(lang);
    }

}
