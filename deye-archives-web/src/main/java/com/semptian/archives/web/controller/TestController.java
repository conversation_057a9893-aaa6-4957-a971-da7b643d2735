package com.semptian.archives.web.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.meiya.whalex.db.builder.DatabaseConfigurationBuilder;
import com.meiya.whalex.db.template.ani.DorisDbConfTemplate;
import com.meiya.whalex.interior.db.constant.CloudVendorsEnum;
import com.meiya.whalex.interior.db.constant.DbResourceEnum;
import com.meiya.whalex.interior.db.operation.out.DbHandleResult;
import com.meiya.whalex.interior.db.search.in.DbConfSqlOperationCondition;
import com.meiya.whalex.sdk.DAT;
import com.meiya.whalex.sdk.api.OperationApi;
import com.meiya.whalex.sdk.builder.DbConfSqlOperationConditionBuilder;
import com.semptian.archives.web.dao.archive.entity.ArcDrillDownFieldConfigEntity;
import com.semptian.archives.web.service.service.ArcDrillDownFieldConfigService;
import com.semptian.archives.web.service.service.impl.EmailArcServiceImpl;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.redis.config.RedisConfig;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/29 16:11
 **/
@Slf4j
@RestController
@RequestMapping(value = "/test")
public class TestController {

    @Resource
    private TianHeDorisTemplate tianHeDorisTemplate;

    @Resource
    private ArcDrillDownFieldConfigService arcDrillDownFieldConfigService;

    //表名和sql字段的映射关系
    private static final HashMap<String, String> TABLE_FIELD_MAP = new HashMap<>();

    //将需要调整的表名和字段名映射关系存入map
    static {
        TABLE_FIELD_MAP.put("通话", "call");
        TABLE_FIELD_MAP.put("短信", "sms");
        TABLE_FIELD_MAP.put("传真", "fax");
        TABLE_FIELD_MAP.put("网络电话", "voip");
        TABLE_FIELD_MAP.put("Email", "email");
        TABLE_FIELD_MAP.put("RADIUS", "mobile_radius");
        TABLE_FIELD_MAP.put("位置", "mobile_location");
        TABLE_FIELD_MAP.put("VPN", "vpn");
        TABLE_FIELD_MAP.put("IM", "im");
        TABLE_FIELD_MAP.put("社交", "social");
        TABLE_FIELD_MAP.put("出行", "travel");
        TABLE_FIELD_MAP.put("工具", "tool");
        //TABLE_FIELD_MAP.put("终端信息BAK", "terminal");
        TABLE_FIELD_MAP.put("终端", "terminal");
        TABLE_FIELD_MAP.put("RemoteCTRL", "remote");
        TABLE_FIELD_MAP.put("Location", "lbs");
        TABLE_FIELD_MAP.put("HTTP", "http");
        TABLE_FIELD_MAP.put("FTP", "ftp");
        TABLE_FIELD_MAP.put("Telnet", "telnet");
        TABLE_FIELD_MAP.put("Engine", "engine");
        TABLE_FIELD_MAP.put("多媒体", "multimedia");
        TABLE_FIELD_MAP.put("资讯", "information");
        TABLE_FIELD_MAP.put("娱乐", "entertainment");
        TABLE_FIELD_MAP.put("网购", "shop");
        TABLE_FIELD_MAP.put("金融", "finance");
        TABLE_FIELD_MAP.put("其他", "other");
        TABLE_FIELD_MAP.put("NF_Email", "nf_email");
        TABLE_FIELD_MAP.put("NF_URL", "nf_url");
        TABLE_FIELD_MAP.put("NF_Chat", "nf_chat");
        TABLE_FIELD_MAP.put("NF_BBS_Weibo", "nf_bbs_weibo");
        TABLE_FIELD_MAP.put("NF_other_log", "nf_other_log");
    }

    @Autowired
    EmailArcServiceImpl emailArcService;

    @GetMapping("/parse_package_test.json")
    @ResponseBody
    @ApiOperation(value = "doris sql test", httpMethod = "GET", response = ReturnModel.class)
    public Object parsePackageTest(String filePath) {
        ReturnModel returnModel = emailArcService.packageAnalysis(filePath, 1, filePath.hashCode(), false);
        return returnModel;
    }

    @GetMapping("/doris_query_test.json")
    @ResponseBody
    @ApiOperation(value = "doris sql test", httpMethod = "GET", response = ReturnModel.class)
    public Object cleanExcelFindPhone(String sql) {
        List<Map<String, Object>> result = tianHeDorisTemplate.queryForList(sql);
        log.info("Execute SQL: {}", sql);
        DbConfSqlOperationCondition sqlOperationCondition = this.getSqlOperationCondition(sql);
        DbHandleResult dbHandleResult = (DbHandleResult)((OperationApi) DAT.create().api(OperationApi.class)).executeSqlByDb(sqlOperationCondition).execute();
        return dbHandleResult;
    }

    @Resource
    private DorisDbConfTemplate dorisDbConfTemplate;

    private DbConfSqlOperationCondition getSqlOperationCondition(String sql) {
        return (DbConfSqlOperationCondition) DbConfSqlOperationConditionBuilder.builder().databaseSetting(DatabaseConfigurationBuilder.builder().cloud(CloudVendorsEnum.MY).database(this.dorisDbConfTemplate).type(DbResourceEnum.doris).build()).sql(sql).build();
    }

    /**
     * 根据可视化模型文件修改下钻字段配置
     */
    @ApiOperation(value = "根据可视化模型文件修改下钻字段配置", httpMethod = "POST", response = ReturnModel.class)
    @ApiImplicitParams({@ApiImplicitParam(name = "modelPath", value = "可视化模型文件路径,默认值D:\\D-EYE V6.4_E23_CBP页面可视化_业务协议模型V3.3-20240321.xlsx", dataType = "String")})
    @PostMapping("/read_model_file_and_update_drill_down_config.json")
    @ResponseBody
    public Object readModelFileAndUpdateDrillDown(@RequestBody String modelPath) {
        //读取可视化模型文件并更新数据库
        return ReturnModel.getInstance().ok(readModelFileAndUpdateDrillDownConfig(modelPath));
    }

    @Autowired
    private RedisConfig redisConfig;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @GetMapping("/redisCacheClear.json")
    @ApiOperation(value = "redis 前缀删除", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel redisCacheClear(@RequestParam(name = "redisPrefix", required = false) String redisPrefix) {
        if (org.apache.commons.lang3.StringUtils.isBlank(redisPrefix)) {
            redisPrefix = redisConfig.getCachePrefix() + "*";
        }
        Set keys = redisTemplate.keys(redisPrefix);
        redisTemplate.delete(keys);
        String result = "删除缓存数量：" + keys.size();
        return ReturnModel.getInstance().ok(result);
    }


    //读取可视化模型文件并更新数据库
    private boolean readModelFileAndUpdateDrillDownConfig(String modelPath) {
        //读取可视化模型文件,该文件为excel文件，需要读取excel文件每页的数据
        try (FileInputStream excelFile = new FileInputStream(modelPath);Workbook workbook = new XSSFWorkbook(excelFile)){
            //依次获取每一页内容并打印
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                //如果sheet名称在表名和字段名映射关系中，则读取该sheet的内容
                if (TABLE_FIELD_MAP.containsKey(workbook.getSheetName(i))) {
                    Sheet sheet = workbook.getSheetAt(i);
                    //找到storeColumn列是第几列
                    int storeColumn = -1;
                    //找到是否可排序列是第几列
                    int sortColumn = -1;
                    //找到是否可搜索列是第几列
                    int searchColumn = -1;
                    //找到业务价值列是第几列
                    int busValueColumn = -1;
                    //找到业务字段是第几列
                    int busFieldColumn = -1;
                    //文件来源显示列是第几列
                    int fileSourceColumn = -1;
                    //找到业务名称列是第几列
                    int busNameColumn = -1;
                    for (Row row : sheet) {
                        for (Cell cell : row) {
                            if (cell.getCellType() == CellType.STRING.getCode()) {
                                if (cell.getStringCellValue().equals("ADS全息字段")) {
                                    storeColumn = cell.getColumnIndex();
                                }
                                if (cell.getStringCellValue().equals("是否可排序")) {
                                    sortColumn = cell.getColumnIndex();
                                }
                                //取是否是字典值来确定是否可搜索，如果是字典值则不支持搜索
                                if (cell.getStringCellValue().equals("是否可搜索")) {
                                    searchColumn = cell.getColumnIndex();
                                }
                                if (cell.getStringCellValue().equals("业务价值")) {
                                    busValueColumn = cell.getColumnIndex();
                                }
                                if (cell.getStringCellValue().equals("业务字段")) {
                                    busFieldColumn = cell.getColumnIndex();
                                }
                                if (cell.getStringCellValue().equals("文件来源显示")) {
                                    fileSourceColumn = cell.getColumnIndex();
                                }
                                if (cell.getStringCellValue().equals("业务名称")) {
                                    busNameColumn = cell.getColumnIndex();
                                }
                            }
                        }
                    }
                    //输出storeColumn列，是否可排序列，是否可搜索列，业务价值列的具体值
                    for (Row row : sheet) {
                        if (row.getRowNum() == 0 || row.getRowNum() == 1) {
                            continue;
                        }
                        //如果storeColumn列，是否可排序列，是否可搜索列，业务价值列有一个为-1，则打印出表名和字段名
                        if (storeColumn == -1 || sortColumn == -1 || searchColumn == -1 || busValueColumn == -1) {
                            log.info("表名：{}字段名：{}", workbook.getSheetName(i), TABLE_FIELD_MAP.get(workbook.getSheetName(i)));
                            continue;
                        }

                        if (row.getCell(storeColumn) == null || row.getCell(sortColumn) == null || row.getCell(searchColumn) == null || row.getCell(busValueColumn) == null) {
                            continue;
                        }
                        StringBuilder fieldMessage = new StringBuilder();

                        //判断cell的类型
                        if (row.getCell(storeColumn).getCellType() == CellType.STRING.getCode()) {
                            //如果字段名称为空，则跳过
                            if (row.getCell(storeColumn).getStringCellValue().equals("")) {
                                continue;
                            }
                            fieldMessage.append("字段名称:" + row.getCell(storeColumn).getStringCellValue()).append("    ");
                        }

                        if (row.getCell(sortColumn).getCellType() == CellType.STRING.getCode()) {
                            fieldMessage.append("是否可排序:" + row.getCell(sortColumn).getStringCellValue()).append("    ");
                        }

                        if (row.getCell(searchColumn).getCellType() == CellType.STRING.getCode()) {
                            fieldMessage.append("是否可搜索:" + row.getCell(searchColumn).getStringCellValue()).append("    ");
                        }

                        if (row.getCell(busValueColumn).getCellType() == CellType.STRING.getCode()) {
                            fieldMessage.append("业务价值:" + row.getCell(busValueColumn).getStringCellValue());
                        } else if (row.getCell(busValueColumn).getCellType() == CellType.NUMERIC.getCode()) {
                            fieldMessage.append("业务价值:" + row.getCell(busValueColumn).getNumericCellValue());
                        }
                        if (fileSourceColumn != -1) {
                            if (row.getCell(fileSourceColumn).getCellType() == CellType.STRING.getCode()) {
                                fieldMessage.append("文件来源显示:" + row.getCell(fileSourceColumn).getStringCellValue());
                            }
                        }
                        if (busNameColumn != -1) {
                            if (row.getCell(busNameColumn).getCellType() == CellType.STRING.getCode()) {
                                fieldMessage.append("业务名称:" + row.getCell(busNameColumn).getStringCellValue());
                            }
                        }

                        //打印出表名对应的协议名称，storeColumn列，是否可排序列，是否可搜索列，业务价值列的具体值
                        log.info("协议名称：{}  字段名称：{}", TABLE_FIELD_MAP.get(workbook.getSheetName(i)), fieldMessage);

                        //根据data_type列和field列，确定唯一的一条记录
                        //校验基本参数不为空
                        if (row.getCell(storeColumn) == null || row.getCell(sortColumn) == null || row.getCell(searchColumn) == null || row.getCell(busValueColumn) == null) {
                            continue;
                        }

                        //如果非业务字段，则跳过
                        if (row.getCell(busFieldColumn).getStringCellValue().equals("否") || StringUtils.isEmpty(row.getCell(busFieldColumn).getStringCellValue())) {
                            continue;
                        }

                        //如果字段为空，或者字段名称为data_type则跳过
                        if (StringUtils.isEmpty(row.getCell(storeColumn).getStringCellValue()) || row.getCell(storeColumn).getStringCellValue().equals("data_type")) {
                            continue;
                        }


                        //nf_url跳过title、keyword、file_name、phone_number字段
                        if (TABLE_FIELD_MAP.get(workbook.getSheetName(i)).equals("nf_url")) {
                            if (row.getCell(storeColumn).getStringCellValue().equals("title") || row.getCell(storeColumn).getStringCellValue().equals("keyword") || row.getCell(storeColumn).getStringCellValue().equals("file_name") || row.getCell(storeColumn).getStringCellValue().equals("phone_number")) {
                                continue;
                            }
                        }

                        //nf_other_log跳过file_type、text、trace_t、file_name字段
                        if (TABLE_FIELD_MAP.get(workbook.getSheetName(i)).equals("nf_other_log")) {
                            if (row.getCell(storeColumn).getStringCellValue().equals("file_type") || row.getCell(storeColumn).getStringCellValue().equals("text") || row.getCell(storeColumn).getStringCellValue().equals("trace_t") || row.getCell(storeColumn).getStringCellValue().equals("file_name")) {
                                continue;
                            }
                        }

                        ArcDrillDownFieldConfigEntity ob = arcDrillDownFieldConfigService.getOne(new QueryWrapper<ArcDrillDownFieldConfigEntity>().eq("data_type", TABLE_FIELD_MAP.get(workbook.getSheetName(i))).eq("field", row.getCell(storeColumn).getStringCellValue()));

                        if (ob == null) {
                            log.warn("未找到唯一的一条记录,协议名称：{}  字段名称：{}", TABLE_FIELD_MAP.get(workbook.getSheetName(i)), row.getCell(storeColumn).getStringCellValue());
                            continue;
                        }
                        int id = ob.getId();
                        if (id == -1) {
                            log.warn("未找到唯一的一条记录，跳过");
                        } else {
                            log.info("确定唯一的一条记录，准备修改数据库");
                            //根据id修改数据库
                            ArcDrillDownFieldConfigEntity arcDrillDownFieldConfigEntity = new ArcDrillDownFieldConfigEntity();
                            arcDrillDownFieldConfigEntity.setId(id);
                            //设置可排序值
                            if (row.getCell(sortColumn).getStringCellValue().equals("是")) {
                                arcDrillDownFieldConfigEntity.setSortable(1);
                            } else if (row.getCell(sortColumn).getStringCellValue().equals("否") || StringUtils.isEmpty(row.getCell(sortColumn).getStringCellValue())) {
                                arcDrillDownFieldConfigEntity.setSortable(0);
                            }
                            //设置是否可搜索值
                            if (row.getCell(searchColumn).getStringCellValue().equals("是")) {
                                arcDrillDownFieldConfigEntity.setIsSearch(1);
                            } else {
                                arcDrillDownFieldConfigEntity.setIsSearch(0);
                            }
                            //如果业务价值是1和3开头的数字，则设置is_core为1，否则设置为0
                            if (row.getCell(busValueColumn).getCellType() == CellType.STRING.getCode()) {
                                if ((row.getCell(busValueColumn).getStringCellValue() + "").startsWith("1") || (row.getCell(busValueColumn).getStringCellValue() + "").startsWith("3")) {
                                    arcDrillDownFieldConfigEntity.setIsCore(1);
                                } else {
                                    arcDrillDownFieldConfigEntity.setIsCore(0);
                                }
                            } else if ((row.getCell(busValueColumn).getNumericCellValue() + "").startsWith("1") || (row.getCell(busValueColumn).getNumericCellValue() + "").startsWith("3")) {
                                arcDrillDownFieldConfigEntity.setIsCore(1);
                            } else {
                                arcDrillDownFieldConfigEntity.setIsCore(0);
                            }
                            //根据业务价值编码调整sort字段
                            try {
                                if (row.getCell(busValueColumn).getCellType() == CellType.STRING.getCode()) {

                                    if (!row.getCell(busValueColumn).getStringCellValue().isEmpty()) {
                                        arcDrillDownFieldConfigEntity.setSort(Integer.valueOf(row.getCell(busValueColumn).getStringCellValue()));
                                    }
                                    arcDrillDownFieldConfigEntity.setSort(Integer.valueOf(row.getCell(busValueColumn).getStringCellValue()));
                                } else if (row.getCell(busValueColumn).getCellType() == CellType.NUMERIC.getCode()) {
                                    arcDrillDownFieldConfigEntity.setSort((int) row.getCell(busValueColumn).getNumericCellValue());
                                }
                            } catch (Exception e) {
                                log.error("sort字段转换失败，error is {}", e.getMessage());
                            }
                            //根据业务名称调整desc_zh字段
                            if (busNameColumn != -1) {
                                if (row.getCell(busNameColumn).getCellType() == CellType.STRING.getCode()) {
                                    arcDrillDownFieldConfigEntity.setDescZh(row.getCell(busNameColumn).getStringCellValue());
                                }
                            }
                            //根据文件来源显示调整is_attach_source字段
                            if (fileSourceColumn != -1) {
                                if (row.getCell(fileSourceColumn).getCellType() == CellType.STRING.getCode()) {
                                    if (row.getCell(fileSourceColumn).getStringCellValue().equals("是")) {
                                        arcDrillDownFieldConfigEntity.setIsAttachSource(1);
                                    } else {
                                        arcDrillDownFieldConfigEntity.setIsAttachSource(0);
                                    }
                                }
                            }
                            arcDrillDownFieldConfigService.updateById(arcDrillDownFieldConfigEntity);
                        }
                    }
                }

            }
        } catch (IOException e) {
            log.error("readModelFile is fail;error is {}", e.getMessage());
            return false;
        }
        return true;
    }

}
