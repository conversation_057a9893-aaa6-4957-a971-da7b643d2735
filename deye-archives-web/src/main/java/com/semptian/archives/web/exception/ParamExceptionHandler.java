package com.semptian.archives.web.exception;

import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.base.service.ReturnModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @data 2022/3/23 15:57
 * 概要：
 * 处理接口参数异常
 */
@RestControllerAdvice
@Slf4j
public class ParamExceptionHandler {
    //数值类型映射异常信息前缀
    private static final String NUM_FORMAT_EXCEPTION_MSG_PREFIX = "The parameter is not a legal numeric type or exceeds the upper limit of the corresponding type. Exception message is ";

    /**
     * 处理数值类型的转换错误
     * @param e
     * @return
     */
    @ExceptionHandler(value = {NumberFormatException.class})
    public ReturnModel exceptionHandler(NumberFormatException e) {
        log.error("参数异常", e);
        return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_INVALID.getCode())
                .setMsg(NUM_FORMAT_EXCEPTION_MSG_PREFIX + e.getMessage());
    }



}
