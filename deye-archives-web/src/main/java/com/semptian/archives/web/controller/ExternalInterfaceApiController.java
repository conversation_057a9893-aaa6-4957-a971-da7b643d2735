package com.semptian.archives.web.controller;

import cn.hutool.core.util.StrUtil;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.core.common.enums.ArcTypeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.interceptor.ArcPermissionCheck;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.common.util.PermissionUtil;
import com.semptian.archives.web.service.model.ExternalArcQueryModel;
import com.semptian.archives.web.service.model.TargetModel;
import com.semptian.archives.web.service.service.ArcIndexService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.archives.web.service.service.ExternalInterfaceApiService;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.base.util.JsonUtil;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

@RestController
@CrossOrigin
@RequestMapping(value = "/ext_inter_api")
@Slf4j
@Api(tags = "ExternalInterfaceApiController", value = "全息档案外部接口，供外部系统调用")
public class ExternalInterfaceApiController {

    @Resource
    private ExternalInterfaceApiService externalInterfaceApiService;

    @Resource
    private ArchivesInfoService arcInfoService;

    @Value("${archive.communityDigAppId.appId:119}")
    private String communityDigAppId;

    @Resource
    private ArcIndexService arcIndexService;

    @Resource
    private TianHeDorisTemplate tianHeDorisTemplate;

    @PostMapping("/phoneAndAuthAccountQuery.json")
    @ResponseBody
    @ApiOperation(value = "根据手机号或者认证账号批量查询基本信息接口", httpMethod = "POST", response = ReturnModel.class)
    public Object phoneAndAuthAccountQuery(@RequestBody TargetModel targetModel) {
        return externalInterfaceApiService.phoneAndAuthAccountQuery(targetModel);
    }

    @GetMapping("/fixIpAccountDelete.json")
    @ApiOperation(value = "删除固定ip账号对应的doris数据", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> fixIpAccountDelete(
            String fixIpAccount
    ) {
        if (StringUtils.isNotBlank(fixIpAccount)) {
            tianHeDorisTemplate.queryForList(String.format("delete from ads_archive_auth_account_statistics where auth_account = '%s' and auth_type = '1029997'", fixIpAccount));
        }

        return ReturnModel.getInstance().ok("删除成功");
    }

    @GetMapping("/dorisDeleteByCondition.json")
    @ApiOperation(value = "根据表名和条件删除 doris 数据", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> dorisDeleteById(
            String tableName,
            String condition
    ) {
        tianHeDorisTemplate.queryForList("delete from " + tableName + " where " + condition);
        return ReturnModel.getInstance().ok("删除成功");
    }

    @GetMapping("/arc_query.json")
    @ResponseBody
    @ApiOperation(value = "arc_query", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    @ArcPermissionCheck
    public Object arcQuery(ExternalArcQueryModel externalArcQueryModel, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        String appId = request.getHeader("appId");

        //如果是群组挖掘调用该接口赋予全息所有档案权限
        List<Integer> permissionList;
        if (communityDigAppId.equals(appId)) {
            permissionList = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
        } else {
            permissionList = PermissionUtil.getArcPermission(request);
        }
        log.debug("request ext_inter_api/arc_query.json parameter:{}", JsonUtil.toJsonString(externalArcQueryModel));

        if (StringUtils.isBlank(userId)) {
            log.warn("request ext_inter_api/arc_query.json parameter");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        if (StringUtils.isNotBlank(externalArcQueryModel.getAuthAccount())) {
            String authAccount = StrUtil.removeAllLineBreaks(externalArcQueryModel.getAuthAccount()).trim();
            // 去除换行符
            externalArcQueryModel.setAuthAccount(authAccount);
        }

        externalArcQueryModel.setArcTypes("0");

        if (StringUtils.isNotBlank(externalArcQueryModel.getAuthAccount())) {
            externalArcQueryModel.setArcTypes(ArcTypeEnum.PHONE.getKey() + "," + ArcTypeEnum.RADIUS.getKey()+ "," + ArcTypeEnum.FIXED_IP.getKey());
        } else if (StringUtils.isNotBlank(externalArcQueryModel.getVirtualAccount())) {
            if (DataTypeEnum.EMAIL.getKey().equals(externalArcQueryModel.getDataType())) {
                externalArcQueryModel.setArcTypes(ArcTypeEnum.EMAIL.getKey());
            } else if (DataTypeEnum.IM.getKey().equals(externalArcQueryModel.getDataType())) {
                externalArcQueryModel.setArcTypes(ArcTypeEnum.IM.getKey());
            }
        } else if (StringUtils.isNotBlank(externalArcQueryModel.getAppName())
                && StringUtils.isNotBlank(externalArcQueryModel.getAppType())) {
            externalArcQueryModel.setArcTypes(ArcTypeEnum.APP.getKey() + "," + ArcTypeEnum.NET_PROTOCOL.getKey());
        }

        return arcInfoService.arcQuery(externalArcQueryModel, permissionList);
    }
}
