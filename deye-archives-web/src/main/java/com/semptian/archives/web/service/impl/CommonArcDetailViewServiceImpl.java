package com.semptian.archives.web.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.semptian.archives.web.core.common.enums.ArcTypeEnum;
import com.semptian.archives.web.core.common.enums.DataTypeEnum;
import com.semptian.archives.web.dao.archive.entity.DrillDownResultEntity;
import com.semptian.archives.web.service.CommonArcDetailViewService;
import com.semptian.archives.web.service.common.enums.DrillDownSceneEnum;
import com.semptian.archives.web.service.common.enums.ImportMethodEnum;
import com.semptian.archives.web.service.common.enums.ListTypeEnum;
import com.semptian.archives.web.service.common.enums.WriteCsvLangModel;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.archives.web.service.service.impl.EmailArcServiceImpl;
import com.semptian.archives.web.service.service.impl.PhoneArcServiceImpl;
import com.semptian.base.service.ReturnModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: caoyang
 * @create: 2021/06/13
 * @desc: 通用档案详情
 **/
@Slf4j
@Service
public class CommonArcDetailViewServiceImpl implements CommonArcDetailViewService {

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    PhoneArcServiceImpl phoneArcService;

    @Autowired
    private EmailArcServiceImpl emailArcService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @Override
    public void importWriteCsv(String localLang, String userId, String method, Integer listType, Integer choose, Integer size, List<Map<String, Object>> contentField, HttpServletRequest request, HttpServletResponse response) {
        //1、判断userId、ImportCsvList 是否为空
        //2、判断是哪个方法
        //3、判断是哪个列表listType（知道csv第一行的输出格式）
        //4、判断是哪种导出方式
        //5、io流读写输出

        //1、校验方法
        try {
            method = method.replaceAll("'", "\\\\'");
            method = URLDecoder.decode(method, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            log.error("URLDecoder decode {} error:{}", method, e.getMessage());
        }

        Map<String, String> params = getParamByMethod(method);

        if (method.contains("email_arc_detail/get_email_details.json")) {
            choose = ImportMethodEnum.ORDERBY.getValue();
        }
        //2、判断是哪个列表哪个方法
        if (choose.equals(ImportMethodEnum.ONPAGE.getValue()) || choose.equals(ImportMethodEnum.CONTENT.getValue())) {
            // 根据当前页返回结果集
            if (contentField == null || contentField.isEmpty()) {
                log.error("field  is null by selected {}", contentField);
                return;
            }

            List<Map<String, Object>> headers = new ArrayList<>();
            String fileName = "";
            // NF 明细
            if (method.contains("nf/get_data_detail.json")) {
                String sceneEnum = params.get("drillDownSceneEnum");

                if (StringUtils.isNotBlank(sceneEnum) && (
                        DrillDownSceneEnum.WEBSITE_ARC_VISITOR_DETAIL.getCode().equals(sceneEnum) ||
                        DrillDownSceneEnum.APP_ARC_VISITOR_DETAIL.getCode().equals(sceneEnum)
                        ) ) {
                    DrillDownSceneEnum sceneEnumObj = DrillDownSceneEnum.getEnumByKey(sceneEnum);
                    headers = arcCommonService.getHeaders(Integer.valueOf(params.get("dataType")), Integer.valueOf(params.get("isAll")), sceneEnumObj, localLang,
                            true, 1, userId);
                    Iterator<Map<String, Object>> iterator = headers.iterator();
                    while (iterator.hasNext()) {
                        Map<String, Object> next = iterator.next();
                        Object isShowObj = next.get("isShow");
                        if (isShowObj != null && "0".equals(isShowObj.toString())) {
                            iterator.remove();
                        }
                    }
                    listType = 1;
                } else {
                    headers = arcCommonService.getHeaders(Integer.valueOf(params.get("dataType")), Integer.valueOf(params.get("isAll")), DrillDownSceneEnum.NF_DETAIL, localLang, true);
                }

                fileName = "nfDetails";
            } else if (method.contains("lis/get_data_detail.json")) { // LIS 明细
                headers = arcCommonService.getHeaders(Integer.valueOf(params.get("dataType")), Integer.valueOf(params.get("isAll")), DrillDownSceneEnum.LIS_DETAIL, localLang, true);
                fileName = "lisDetails";
            } else if (method.contains("authentication_billing/get_data_detail.json")) { // 鉴权计费
                headers = arcCommonService.getHeaders(Integer.valueOf(params.get("action")), Integer.valueOf(params.get("isAll")), DrillDownSceneEnum.AUTHENTICATION_BILLING, localLang, true);
                fileName = "autherticationBillingDetails";
            }

            AccordingCONTENT(params, listType, contentField, request, response, localLang, headers, fileName);
        } else if (choose.equals(ImportMethodEnum.ORDERBY.getValue())) {
            List<Map<String, Object>> headers = new ArrayList<>();
            List<Map<String, Object>> contents = new ArrayList<>();
            String fileName = "";
            // NF 明细
            if (method.contains("nf/get_data_detail.json")) {
                String sceneEnum = params.get("drillDownSceneEnum");
                if (StringUtils.isNotBlank(sceneEnum) && (
                        DrillDownSceneEnum.WEBSITE_ARC_VISITOR_DETAIL.getCode().equals(sceneEnum) ||
                                DrillDownSceneEnum.APP_ARC_VISITOR_DETAIL.getCode().equals(sceneEnum)
                )) {
                    listType = 1;
                    DrillDownSceneEnum sceneEnumObj = DrillDownSceneEnum.getEnumByKey(sceneEnum);
                    headers = arcCommonService.getHeaders(Integer.valueOf(params.get("dataType")), Integer.valueOf(params.get("isAll")),
                            sceneEnumObj, localLang, true, 1, userId);
                    Iterator<Map<String, Object>> iterator = headers.iterator();
                    while (iterator.hasNext()) {
                        Map<String, Object> next = iterator.next();
                        Object isShowObj = next.get("isShow");
                        if (isShowObj != null && "0".equals(isShowObj.toString())) {
                            iterator.remove();
                        }
                    }
                    fileName = "nfDetails";
                    DrillDownModel drillDownModel = new DrillDownModel();
                    drillDownModel.setArcAccount(params.get("arcAccount"));
                    drillDownModel.setArcAccountType(params.get("arcAccountType"));
                    drillDownModel.setAuthAccount(params.get("authAccount"));
                    drillDownModel.setAuthAccountType(params.get("authAccountType"));
                    drillDownModel.setDrillDownSceneEnum(sceneEnumObj);
                    drillDownModel.setDataType(Integer.valueOf(params.get("dataType")));
                    drillDownModel.setCreateDay(params.get("createDay"));
                    drillDownModel.setStartDay(params.get("startDay"));
                    drillDownModel.setEndDay(params.get("endDay"));
                    drillDownModel.setNetAction(Integer.valueOf(params.getOrDefault("netAction", "0")));
                    drillDownModel.setDateOption(Integer.valueOf(params.get("dateOption")));
                    drillDownModel.setIsAll(Integer.valueOf(params.get("isAll")));
                    drillDownModel.setKeyWord(params.get("keyWord"));
                    if (StringUtils.isNotEmpty(params.getOrDefault("sortField", ""))) {
                        drillDownModel.setSortField(params.get("sortField"));
                        drillDownModel.setSortType(Integer.parseInt(params.getOrDefault("sortType", "1")));
                    }
                    drillDownModel.setOnPage(1);
                    drillDownModel.setSize(size);
                    drillDownModel.setLang(localLang);

                    String startTime = params.get("startTime");
                    String endTime = params.get("endTime");

                    if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                        drillDownModel.setStartTime(startTime);
                        drillDownModel.setEndTime(endTime);
                    }
                    drillDownModel.setSrcCountry(params.get("srcCountry"));

                    DrillDownResultEntity drillDownDataDetail = arcCommonService.getDrillDownDataDetail(drillDownModel, userId);
                    contents = drillDownDataDetail.getList();
                } else {
                    headers = arcCommonService.getHeaders(Integer.valueOf(params.get("dataType")), Integer.valueOf(params.get("isAll")), DrillDownSceneEnum.NF_DETAIL, localLang, true);
                    fileName = "nfDetails";
                    DrillDownModel drillDownModel = new DrillDownModel();
                    drillDownModel.setArcAccount(params.get("arcAccount"));
                    drillDownModel.setArcAccountType(params.get("arcAccountType"));
                    drillDownModel.setAuthAccount(params.get("arcAccount"));
                    drillDownModel.setAuthAccountType(params.get("arcAccountType"));
                    drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.NF_DETAIL);
                    drillDownModel.setDataType(Integer.valueOf(params.get("dataType")));
                    drillDownModel.setCreateDay(params.get("createDay"));
                    drillDownModel.setStartDay(params.get("startDay"));
                    drillDownModel.setEndDay(params.get("endDay"));
                    drillDownModel.setNetAction(Integer.valueOf(params.getOrDefault("netAction", "0")));
                    drillDownModel.setDateOption(Integer.valueOf(params.get("dateOption")));
                    drillDownModel.setIsAll(Integer.valueOf(params.get("isAll")));
                    drillDownModel.setKeyWord(params.get("keyWord"));
                    if (StringUtils.isNotEmpty(params.getOrDefault("sortField", ""))) {
                        drillDownModel.setSortField(params.get("sortField"));
                        drillDownModel.setSortType(Integer.parseInt(params.getOrDefault("sortType", "1")));
                    }
                    drillDownModel.setOnPage(1);
                    drillDownModel.setSize(size);
                    drillDownModel.setLang(localLang);

                    String startTime = params.get("startTime");
                    String endTime = params.get("endTime");

                    if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                        drillDownModel.setStartTime(startTime);
                        drillDownModel.setEndTime(endTime);
                    }

                    DrillDownResultEntity drillDownDataDetail = arcCommonService.getDrillDownDataDetail(drillDownModel);
                    contents = drillDownDataDetail.getList();
                }
            } else if (method.contains("lis/get_data_detail.json")) { // LIS 明细
                headers = arcCommonService.getHeaders(Integer.valueOf(params.get("dataType")), Integer.valueOf(params.get("isAll")), DrillDownSceneEnum.LIS_DETAIL, localLang, true);
                fileName = "lisDetails";
                DrillDownModel drillDownModel = new DrillDownModel();

                drillDownModel.setArcAccount(params.get("arcAccount"));
                drillDownModel.setArcAccountType(params.get("arcAccountType"));
                drillDownModel.setArcType(params.get("archiveType"));
                if (params.get("archiveType") != null) {
                    if (ArcTypeEnum.IM.getKey().equals(params.get("archiveType"))) {
                        drillDownModel.setVirtualAccount(params.get("arcAccount"));
                    }
                }

                if (params.get("drillDownSceneEnum") != null) {
                    if (DrillDownSceneEnum.VIRTUAL_ACCOUNT_DETAIL.getCode().equals(params.get("drillDownSceneEnum"))) {
                        drillDownModel.setVirtualAccount(params.get("virtualAccount"));
                        drillDownModel.setVirtualAccountAppType(params.get("virtualAccountAppType"));
                        drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.VIRTUAL_ACCOUNT_DETAIL);
                    } else {
                        drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.LIS_DETAIL);
                    }
                } else {
                    drillDownModel.setDrillDownSceneEnum(DrillDownSceneEnum.LIS_DETAIL);
                }

                drillDownModel.setDataType(Integer.valueOf(params.get("dataType")));
                drillDownModel.setCreateDay(params.get("createDay"));
                drillDownModel.setStartDay(params.get("startDay"));
                drillDownModel.setEndDay(params.get("endDay"));
                drillDownModel.setDateOption(Integer.valueOf(params.get("dateOption")));
                drillDownModel.setIsAll(Integer.valueOf(params.get("isAll")));
                drillDownModel.setKeyWord(params.get("keyWord"));
                drillDownModel.setScope(Integer.parseInt(params.getOrDefault("scope", "0")));
                if (StringUtils.isNotEmpty(params.getOrDefault("sortField", ""))) {
                    drillDownModel.setSortField(params.get("sortField"));
                    drillDownModel.setSortType(Integer.parseInt(params.getOrDefault("sortType", "1")));
                }
                drillDownModel.setOnPage(1);
                drillDownModel.setSize(size);
                drillDownModel.setLang(localLang);
                String startTime = params.get("startTime");
                String endTime = params.get("endTime");

                if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                    drillDownModel.setStartTime(startTime);
                    drillDownModel.setEndTime(endTime);
                }

                DrillDownResultEntity drillDownDataDetail = arcCommonService.getDrillDownDataDetail(drillDownModel);
                contents = drillDownDataDetail.getList();
            } else if (method.contains("authentication_billing/get_data_detail.json")) { // 鉴权计费
                headers = arcCommonService.getHeaders(Integer.valueOf(params.get("action")), Integer.valueOf(params.get("isAll")), DrillDownSceneEnum.AUTHENTICATION_BILLING, localLang, true);
                fileName = "autherticationBillingDetails";

                AuthBillingDetailModel authBillingDetailModel = new AuthBillingDetailModel();
                authBillingDetailModel.setArcAccount(params.get("arcAccount"));
                authBillingDetailModel.setOnPage(1);
                authBillingDetailModel.setSize(size);
                authBillingDetailModel.setIsAll(Integer.parseInt(params.get("isAll")));
                authBillingDetailModel.setStartDay(params.get("startDay"));
                authBillingDetailModel.setEndDay(params.get("endDay"));
                authBillingDetailModel.setKeyWord(params.get("keyWord"));
                authBillingDetailModel.setDateOption(Integer.valueOf(params.get("dateOption")));
                authBillingDetailModel.setAction(Integer.valueOf(params.get("action")));
                authBillingDetailModel.setNetworkType(Integer.valueOf(params.get("networkType")));
                authBillingDetailModel.setSortCondition(params.get("sortCondition"));

                if (StringUtils.isNotEmpty(params.getOrDefault("sortField", ""))) {
                    authBillingDetailModel.setSortField(params.get("sortField"));
                    authBillingDetailModel.setSortType(Integer.parseInt(params.getOrDefault("sortType", "1")));
                }

                Map<String, Object> communicationDataDetail = (Map<String, Object>) phoneArcService.getCommunicationDataDetail(authBillingDetailModel,localLang);

                contents =  (List<Map<String, Object>>)communicationDataDetail.get("list");
            } else if (method.contains("email_arc_detail/get_email_details.json")) {
                headers = arcCommonService.getHeaders(Integer.valueOf(DataTypeEnum.EMAIL.getKey()), 1, DrillDownSceneEnum.LIS_DETAIL, localLang, true);
                fileName = "lisDetails";

                String arcAccount = "";
                try {
                    arcAccount = URLDecoder.decode(params.get("arcAccount"), StandardCharsets.UTF_8.toString());
                } catch (UnsupportedEncodingException ignored) {
                    arcAccount = params.get("arcAccount");
                }

                PageWarpEntity pageWarpEntity = new PageWarpEntity();
                pageWarpEntity.setOnPage(1);
                pageWarpEntity.setSize(size);
                Map<String, Object> stringObjectMap = emailArcService.queryEmailDetails(arcAccount, params.get("emailType"), Integer.valueOf(params.get("searchType")), params.get("keyword").replaceAll("'", "\\\\'"), Integer.valueOf(params.get("onlyImportant")), new DateModel(params.get("startDay"), params.get("endDay"), Integer.valueOf(params.get("dateOption"))), pageWarpEntity, "EXPORT", localLang);
                contents = (List<Map<String, Object>>) stringObjectMap.get("list");
            }

            AccordingORDERBY(params, listType, size, request, response, localLang, headers, fileName, contents);
        } else {
            log.error("choose does not comply with the specification {}", choose);
        }
    }


    public void AccordingORDERBY(Map<String, String> params, Integer listType, Integer size, HttpServletRequest request, HttpServletResponse response, String localLang, List<Map<String, Object>> headers, String fileName, List<Map<String, Object>> contents) {
        ReturnModel dataList = new ReturnModel();
        if (listType.equals(ListTypeEnum.RADIUS.getValue()) || listType.equals(ListTypeEnum.FIXED_IP.getValue())) {

            String arcAccount = null;
            try {
                arcAccount = URLDecoder.decode(params.get("arcAccount"), StandardCharsets.UTF_8.toString());
            } catch (UnsupportedEncodingException ignored) {
                arcAccount = params.get("arcAccount");
            }

            List<Map<String, Object>> list = new ArrayList<>();
            String fName = ListTypeEnum.RADIUS.getDescription();
            String firstLine = WriteCsvLangModel.getListStr(localLang, ListTypeEnum.RADIUS.getValue());
            List<String> keys = null;

            if (headers.isEmpty() || contents.isEmpty()) {

                ArcContext ctx = ArcContext.builder().arcId(params.getOrDefault("arcId", "")).arcAccount(arcAccount).createDay(params.get("createDay"))
                        .startDay(params.get("startDay")).endDay(params.get("endDay")).arcType(params.get("arcType")).isBlock(Integer.valueOf(params.getOrDefault("isBlock", "0")) != 0).keyWord(params.get("content"))
                        .sortField(params.get("sortField")).sortType(Integer.valueOf(StringUtils.isEmpty(params.get("sortType")) ? "1" : params.get("sortType"))).onPage(1).size(size).build();
                JSONObject t = commonArcServiceFacade.getAppList(com.semptian.archives.web.service.common.enums.ArcTypeEnum.getArcTypeByKey(params.get("arcType")), ctx);

                Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(t), Map.class);
                list = JSONObject.parseObject(JSONObject.toJSONString(map.get("list")), List.class);
            } else {
                list = contents;
                fName = fileName;
                List<String> titles = headers.stream().map(map -> (String) map.get("title")).collect(Collectors.toList());
                firstLine = String.join(",", titles);
                keys = headers.stream().map(map -> (String) map.get("field")).collect(Collectors.toList());
            }

            //支持IO流读写
            productCsvFile(list, fName, firstLine, response, keys);
        } else if (listType.equals(ListTypeEnum.EMAIL.getValue())) {

            String firstLine = "";
            List<String> keys = headers.stream().map(map -> (String) map.get("field")).collect(Collectors.toList());

            List<String> titles = headers.stream().map(map -> (String) map.get("title")).collect(Collectors.toList());
            firstLine = String.join(",", titles);

            //支持IO流读写
            productCsvFile(contents, fileName, firstLine, response, keys);
        } else if (listType.equals(ListTypeEnum.PHONE.getValue())) {

            List<Map<String, Object>> list = new ArrayList<>();
            String fName = ListTypeEnum.RADIUS.getDescription();
            String firstLine = WriteCsvLangModel.getListStr(localLang, ListTypeEnum.RADIUS.getValue());
            List<String> keys = null;
            if (headers.isEmpty() || contents.isEmpty()) {
                ArcContext ctx = ArcContext.builder().arcId(params.getOrDefault("arcId", "")).arcAccount(params.get("arcAccount"))
                        .startDay(params.get("startDay")).endDay(params.get("endDay")).arcType(params.get("arcType")).isBlock(Integer.valueOf(params.getOrDefault("isBlock", "0")) != 0).keyWord(params.get("content"))
                        .sortField(params.get("sortField")).sortType(Integer.valueOf(StringUtils.isEmpty(params.get("sortType")) ? "1" : params.get("sortType"))).onPage(1).size(size).build();
                JSONObject t = commonArcServiceFacade.getAppList(com.semptian.archives.web.service.common.enums.ArcTypeEnum.getArcTypeByKey(params.get("arcType")), ctx);

                Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(t), Map.class);
                list = JSONObject.parseObject(JSONObject.toJSONString(map.get("list")), List.class);
            } else {
                list = contents;
                fName = fileName;
                List<String> titles = headers.stream().map(map -> (String) map.get("title")).collect(Collectors.toList());
                firstLine = String.join(",", titles);
                keys = headers.stream().map(map -> (String) map.get("field")).collect(Collectors.toList());
            }
            //支持IO流读写
            productCsvFile(list, fName, firstLine, response, keys);
        } else if (listType.equals(ListTypeEnum.WEB_SITE.getValue())) {
            if (StringUtils.isBlank(params.get("dateType"))) {
                params.put("dateType", "0");
            }
            if (StringUtils.isBlank(params.get("sortField"))) {
                params.put("sortField", "nfNum");
            } else if (params.get("sortField").equals("nfNum")) {
                params.put("sortField", "nfNum");
            }
            if (StringUtils.isBlank(params.get("sortType"))) {
                params.put("sortType", "1");
            }


            String domain = "";
            try {
                domain = URLDecoder.decode(params.get("domain"), StandardCharsets.UTF_8.toString());
            } catch (UnsupportedEncodingException ignored) {
                domain = params.get("domain");
            }

            DateModel dateModel = new DateModel();
            dateModel.setDateOption(Integer.valueOf(params.get("dateOption")));
            dateModel.setStartDay(params.get("startDay"));
            dateModel.setEndDay(params.get("endDay"));
            ArcContext ctx = ArcContext.builder().arcId(params.get("arcId")).domain(domain).onPage(1).size(size)
                    .isBlock(Boolean.valueOf(params.get("isBlock")))
                    .netAction(Integer.valueOf(params.get("netAction")))
                    .visitorType(Integer.valueOf(params.get("visitorType"))).keyWord(params.get("keyword")).dateModel(dateModel).sortField(params.get("sortField")).sortType(Integer.valueOf(params.get("sortType"))).build();

            Map<String, Object> accessTargetList = commonArcServiceFacade.getAccessTargetList(com.semptian.archives.web.service.common.enums.ArcTypeEnum.WEB_SITE, ctx);

            List<Map<String, Object>> list = JSONObject.parseObject(JSONObject.toJSONString(accessTargetList.get("list")), List.class);
            String visitorType = params.get("visitorType");

            list.forEach(content -> {
                if ("3".equals(visitorType)) {
                    content.put("arcType", content.getOrDefault("importantTargetCategory", ""));
                } else {
                    if (content.containsKey("arcType")) {
                        try {
                            Integer arcType = (Integer) content.get("arcType");
                            if (arcType == 0) {
                                content.remove("arcType");
                            } else {
                                content.put("arcType", ArcTypeEnum.getByKey(String.valueOf(arcType)));
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }
            });
            productCsvFile(list, ListTypeEnum.WEB_SITE.getDescription(), WriteCsvLangModel.getListStr(localLang, ListTypeEnum.WEB_SITE.getValue(), Integer.parseInt(params.getOrDefault("visitorType", "0")) == 2), response, null);
        } else if (listType.equals(ListTypeEnum.APP.getValue())) {
            if (StringUtils.isBlank(params.get("dateType"))) {
                params.put("dateType", "0");
            }
            if (StringUtils.isBlank(params.get("sortField"))) {
                params.put("sortField", "nfNum");
            } else if (params.get("sortField").equals("nfNum")) {
                params.put("sortField", "nfNum");
            }
            if (StringUtils.isBlank(params.get("sortType"))) {
                params.put("sortType", "1");
            }

            String appName = "";
            String appType = "";
            try {
                appName = URLDecoder.decode(params.get("appName"), StandardCharsets.UTF_8.toString());
                appType = URLDecoder.decode(params.get("appType"), StandardCharsets.UTF_8.toString());
            } catch (UnsupportedEncodingException ignored) {
                appName = params.get("appName");
                appType = params.get("appType");
            }


            DateModel dateModel = new DateModel();
            dateModel.setDateOption(Integer.valueOf(params.get("dateOption")));
            dateModel.setStartDay(params.get("startDay"));
            dateModel.setEndDay(params.get("endDay"));
            ArcContext ctx = ArcContext.builder().arcId(params.get("arcId")).appName(appName).appType(appType).dataType(params.get("dataType")).onPage(1).size(size)
                    .isBlock(Boolean.valueOf(params.get("isBlock")))
                    .netAction(Integer.valueOf(params.get("netAction")))
                    .visitorType(Integer.valueOf(params.get("visitorType"))).keyWord(params.get("keyword")).dateModel(dateModel).sortField(params.get("sortField")).sortType(Integer.valueOf(params.get("sortType"))).build();

            Map<String, Object> accessTargetList = commonArcServiceFacade.getAccessTargetList(com.semptian.archives.web.service.common.enums.ArcTypeEnum.APP, ctx);

            List<Map<String, Object>> list = JSONObject.parseObject(JSONObject.toJSONString(accessTargetList.get("list")), List.class);
            String visitorType = params.get("visitorType");

            list.forEach(content -> {
                if (content.containsKey("arcType")) {
                    if ("3".equals(visitorType)) {
                        content.put("arcType", content.getOrDefault("importantTargetCategory", ""));
                    } else  {
                        try {
                            Integer arcType = (Integer) content.get("arcType");
                            if (arcType == 0) {
                                content.remove("arcType");
                            } else {
                                content.put("arcType", ArcTypeEnum.getByKey(String.valueOf(arcType)));
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }
            });
            productCsvFile(list, ListTypeEnum.APP.getDescription(), WriteCsvLangModel.getListStr(localLang, ListTypeEnum.APP.getValue(), Integer.parseInt(params.getOrDefault("visitorType", "0")) == 2), response, null);
        } else {
            return;
        }

    }

    public void AccordingCONTENT(Map<String, String> params, Integer listType, List<Map<String, Object>> contentField, HttpServletRequest request, HttpServletResponse response, String localLang, List<Map<String, Object>> headers, String fileName) {
        ReturnModel dataList = new ReturnModel();
        if (listType.equals(ListTypeEnum.RADIUS.getValue()) || listType.equals(ListTypeEnum.FIXED_IP.getValue())) {
            //支持IO流读写
            String firstLine = WriteCsvLangModel.getListStr(localLang, ListTypeEnum.RADIUS.getValue());
            List<String> keys = new ArrayList<>();
            if (!headers.isEmpty()) {
                List<String> titles = headers.stream().map(map -> (String) map.get("title")).collect(Collectors.toList());
                firstLine = String.join(",", titles);

                keys = headers.stream().map(map -> (String) map.get("field")).collect(Collectors.toList());
            }
            productCsvFile(contentField, StringUtils.isNotEmpty(fileName) ? fileName : ListTypeEnum.RADIUS.getDescription(), firstLine, response, keys);
        } else if (listType.equals(ListTypeEnum.VIRTUAL.getValue())) {
            //支持IO流读写
            productCsvFile(contentField, ListTypeEnum.VIRTUAL.getDescription(), WriteCsvLangModel.getListStr(localLang, ListTypeEnum.VIRTUAL.getValue()), response, null);
        } else if (listType.equals(ListTypeEnum.PHONE.getValue())) {

            String firstLine = WriteCsvLangModel.getListStr(localLang, ListTypeEnum.RADIUS.getValue());

            List<String> keys = new ArrayList<>();
            if (!headers.isEmpty()) {
                List<String> titles = headers.stream().map(map -> (String) map.get("title")).collect(Collectors.toList());
                firstLine = String.join(",", titles);
                keys = headers.stream().map(map -> (String) map.get("field")).collect(Collectors.toList());
            }

            productCsvFile(contentField, StringUtils.isNotEmpty(fileName) ? fileName : ListTypeEnum.RADIUS.getDescription(), firstLine, response, keys);
        } else if (listType.equals(ListTypeEnum.WEB_SITE.getValue())) {
            //支持IO流读写 去除非列表数据
            String visitorType = params.getOrDefault("visitorType", "");

            contentField.forEach(content -> {
                if ("3".equals(visitorType)) {
                    content.put("arcType", content.getOrDefault("importantTargetCategory", ""));
                } else {
                    if (content.containsKey("arcType")) {
                        try {
                            Integer arcType = (Integer) content.get("arcType");
                            if (arcType == 0) {
                                content.remove("arcType");

                            } else {
                                content.put("arcType", ArcTypeEnum.getByKey(String.valueOf(arcType)));
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }
            });

            productCsvFile(contentField, ListTypeEnum.WEB_SITE.getDescription(), WriteCsvLangModel.getListStr(localLang, ListTypeEnum.WEB_SITE.getValue(), Integer.parseInt(params.getOrDefault("visitorType", "0")) == 2), response, null);
        } else if (listType.equals(ListTypeEnum.APP.getValue())) {
            String visitorType = params.getOrDefault("visitorType", "");

            contentField.forEach(content -> {
                if ("3".equals(visitorType)) {
                    content.put("arcType", content.getOrDefault("importantTargetCategory", ""));
                } else {
                    if (content.containsKey("arcType")) {
                        try {
                            Integer arcType = (Integer) content.get("arcType");
                            if (arcType == 0) {
                                content.remove("arcType");
                            } else {
                                content.put("arcType", ArcTypeEnum.getByKey(String.valueOf(arcType)));
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }
            });

            productCsvFile(contentField, ListTypeEnum.APP.getDescription(), WriteCsvLangModel.getListStr(localLang, ListTypeEnum.APP.getValue(), Integer.parseInt(params.getOrDefault("visitorType", "0")) == 2), response, null);
        }
    }

    public Map<String, String> getParamByMethod(String method) {
        String params = method.substring(method.lastIndexOf("?") + 1);
        Map<String, String> map = Maps.newHashMap();
        for (String param : params.split("&")) {
            String key = param.substring(0, param.lastIndexOf("="));
            String value = param.substring(param.lastIndexOf("=") + 1);
            if (value.contains("+")) {
                value = value.replace("+", " ");
            }
            map.put(key, value);
        }
        return map;
    }

    public Integer isExistInteger(String param) {
        return StringUtils.isBlank(param) ? null : Integer.valueOf(param);
    }


    public void productCsvFile(List<Map<String, Object>> rows, String fileName, String firstLine, HttpServletResponse response, List<String> keys) {

        //文件输出流
        BufferedWriter fileOutputStream = null;


        try {
            //含文件名的全路径
            String fullPath = fileName + DateUtil.date().toString("ddMMyyyyHHmmSS") + ".csv";
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fullPath);
            //格式化浮点数据
            NumberFormat formatter = NumberFormat.getNumberInstance();
            //设置最大小数位为10；
            formatter.setMaximumFractionDigits(13);
            //格式化日期数据
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");

            //实例化文件输出流 CSV编码格式为GB2312
            fileOutputStream = new BufferedWriter(new OutputStreamWriter(response.getOutputStream(), "UTF-8"), 1024);

            //添加BOM头
            fileOutputStream.write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));

            //首行输出
            StringJoiner str = new StringJoiner(",");
            for (String firstCell : firstLine.split(",")) {
                str.add("\"" + firstCell + "\"");
            }
            fileOutputStream.write(str.toString());
            fileOutputStream.newLine();

            if (rows == null || rows.size() == 0) {
                return;
            }

            //遍历输出每行
            Iterator<Map<String, Object>> ite = rows.iterator();
            // 顺序问题

            Integer i = 1;
            while (ite.hasNext()) {
                Map<String, Object> rowData = ite.next();
                //自定义选择器，对key进行排序
                TreeMap<String, Object> data = new TreeMap<>(new RadiusComparator());
                data.putAll(rowData);
                StringJoiner stringJoiner = new StringJoiner(",");

                if (keys == null || keys.isEmpty()) {
                    stringJoiner.add("\"" + i + "\"");
                    for (Iterator<Map.Entry<String, Object>> iterator = data.entrySet().iterator(); iterator.hasNext(); ) {
                        Map.Entry<String, Object> entry = iterator.next();
                        Object obj = entry.getValue();
                        //格式化数据
                        String field = "";
                        if (null != obj) {
                            if (obj.getClass() == String.class) {
                                //如果是字符串
                                field = "\t" + ((String) obj).replaceAll(",", " ").replaceAll("\\n", " ").replaceAll("\\r", " ");
                            } else if (obj.getClass() == Double.class || obj.getClass() == Float.class) {
                                //格式化浮点数，使浮点数不以科学计数法输出
                                field = formatter.format(obj);
                            } else if (obj.getClass() == Integer.class || obj.getClass() == Short.class || obj.getClass() == Byte.class) {
                                //如果是整型
                                field += obj;
                            } else if (obj.getClass() == Long.class) {
                                //如果是时间戳，以时间的形式显示
                                if ((Long) obj / 1000000000000L > 0 && (entry.getKey().toLowerCase().contains("date") || entry.getKey().toLowerCase().contains("time"))) {
                                    field = "\t" + DateUtil.format(new Date((Long) obj), sdf) + "\t";
                                } else {
                                    field += obj;
                                }
                            } else if (obj.getClass() == Date.class) {
                                //如果是日期类型
                                field = sdf.format(obj);
                            } else {
                                //null时给一个空格占位
                                field = " ";
                            }
                            //拼接所有字段为一行数据
                            //排除不需要打印的数据
                            if (RadiusComparator.PARAMALL.contains(entry.getKey())) {
                                stringJoiner.add("\"" + field + "\"");
                            }
                        }else {
                            //null时给一个空格占位
                            field = " ";
                            stringJoiner.add("\"" + field + "\"");
                        }
                    }
                } else {
                    for (String key : keys) {
                        Object obj = rowData.get(key);
                        //格式化数据
                        String field = "";
                        if (null != obj) {
                            if (obj.getClass() == String.class) {
                                //如果是字符串
                                field = "\t" + ((String) obj).replaceAll(",", " ").replaceAll("\\n", " ").replaceAll("\\r", " ");
                            } else if (obj.getClass() == Double.class || obj.getClass() == Float.class) {
                                //格式化浮点数，使浮点数不以科学计数法输出
                                field = formatter.format(obj);
                            } else if (obj.getClass() == Integer.class || obj.getClass() == Short.class || obj.getClass() == Byte.class) {
                                //如果是整型
                                field += obj;
                            } else if (obj.getClass() == Long.class) {
                                //如果是时间戳，以时间的形式显示
                                if ((Long) obj / 1000000000000L > 0 && (key.toLowerCase().contains("date") || key.toLowerCase().contains("time"))) {
                                    field = "\t" + DateUtil.format(new Date((Long) obj), sdf) + "\t";
                                } else {
                                    field += obj;
                                }
                            } else if (obj.getClass() == Date.class) {
                                //如果是日期类型
                                field = sdf.format(obj);
                            } else if (obj.getClass() == ArrayList.class) {
                                //如果是数组类型
                                field = "\t" + (obj.toString()).replaceAll("\\n", " ").replaceAll("\\r", " ");
                            } else {
                                //null时给一个空格占位
                                field = " ";
                            }
                            if (field.endsWith("\"")) {
                                stringJoiner.add(field);
                            } else {
                                stringJoiner.add("\"" + field + "\"");
                            }
                        } else {
                            //null时给一个空格占位
                            field = " ";
                            stringJoiner.add("\"" + field + "\"");
                        }
                    }
                }
                fileOutputStream.write(stringJoiner.toString());
                fileOutputStream.newLine();     //换行，创建一个新行；
                i++;
                fileOutputStream.flush();
            }
        } catch (Exception e) {
            log.error("csv is export failure {}", e);
        } finally {
            try {
                fileOutputStream.close();
            } catch (IOException e) {
                log.error("IO  is close failure {}", e);
            }
        }
    }
}
