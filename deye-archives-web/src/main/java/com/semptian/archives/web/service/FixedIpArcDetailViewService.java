package com.semptian.archives.web.service;

import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.base.service.ReturnModel;

import java.util.List;
import java.util.Map;

/**
 * 固定Ip方法接口
 * @author: lmz
 * @date: 2022/9/6 21:15
 */
public interface FixedIpArcDetailViewService {

    /**
     * 固定IP外用接口--批量查询固定IP建档状态
     *
     * @param ipList ip数组
     * @return ip是否已建档
     */
    ReturnModel<Map<String,Object>> queryFixedIpStatus(String[] ipList);

    public List<ArcEsEntity> queryFixedIpList(String[] ipList);
}
