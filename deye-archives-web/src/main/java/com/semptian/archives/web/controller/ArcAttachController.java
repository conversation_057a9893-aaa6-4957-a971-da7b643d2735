package com.semptian.archives.web.controller;

import cn.hutool.core.util.ObjectUtil;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.common.enums.BusinessTypeEnum;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.ArcAttachService;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: SunQi
 * @create: 2021/03/26
 * desc:
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/attach_info")
@Slf4j
@Api
public class ArcAttachController {
    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @Resource
    @Lazy
    private ArcAttachService arcAttachService;

    @ApiOperation(value = "获取档案下的附件信息", notes = "获取档案下的附件信息")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "archiveType", value = "档案类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "arcAccount", value = "认证账号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "arcAccountType", value = "认证账号类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "查询关键字", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sortField", value = "排序字段，可选：attachSize,firstAppearTime,lastAppearTime。默认firstAppearTime", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sortType", value = "排序方式，0 升序 1 降序，默认降序", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "startDay", value = "开始日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDay", value = "结束日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fileType", value = "文件类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "当前页，默认第一页", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页显示数量，默认10", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "createDay",value = "固定IP档案建档日期",dataType = "String",paramType = "query"),
    })
    @OperateLog
    @GetMapping("/get_attach_info.json")
    public Object getAttachDetailInfo(
            @RequestParam("archiveType") Integer archiveType,
            @RequestParam("arcAccount") String arcAccount,
            @RequestParam(value = "arcAccountType", required = false) String arcAccountType,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "fileType") String fileType,
            @RequestParam(name = "createDay",required = false) String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity,
            HttpServletRequest request
    ) {
        if (ObjectUtil.isNull(archiveType) || StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        if (StringUtils.isEmpty(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("lastAppearTime");
            pageWarpEntity.setSortType(1);
        }

        String lang = CurrentUserUtil.getLang(request);
        ArcContext ctx = ArcContext.builder().arcType(archiveType.toString()).arcAccount(arcAccount).keyWord(keyword).arcAccountType(arcAccountType)
                .fileType(fileType).createDay(createDay).dateModel(dateModel).pageWarpEntity(pageWarpEntity).lang(lang).build();

        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAttachInfo(ctx, BusinessTypeEnum.TAB));
    }

    @ApiOperation(value = "查看附件来源", notes = "查看附件来源")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "请求成功"),
            @ApiResponse(code = 0, message = "请求失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "archiveType", value = "档案类型", required = true, dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "arcAccount", value = "认证账号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataType", value = "协议编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachMd5", value = "附件MD5值", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dateOption", value = "日期选项", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDay", value = "开始日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDay", value = "结束日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sortField", value = "排序字段，默认captureTime", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sortType", value = "排序方式，0 升序 1 降序，默认降序", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "onPage", value = "当前页，默认第一页", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页显示数量，默认10", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "createDay",value = "固定IP档案建档日期",dataType = "String",paramType = "query")})
    @OperateLog
    @GetMapping("/get_attach_source.json")
    public Object getAttachSource(HttpServletRequest request,
                                       @RequestParam("archiveType") Integer archiveType,
                                       @RequestParam("arcAccount") String arcAccount,
                                       @RequestParam(name = "arcAccountType", required = false, defaultValue = "") String arcAccountType,
                                       @RequestParam("dataType") Integer dataType,
                                       @RequestParam("attachMd5") String attachMd5,
                                       @RequestParam(name = "createDay",required = false) String createDay, DateModel dateModel, PageWarpEntity pageWarpEntity) {
        if (ObjectUtil.isNull(archiveType) || StringUtils.isBlank(arcAccount)) {
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        String lang = CurrentUserUtil.getLang(request);
        return arcAttachService.getAttachSourceInfo(archiveType, arcAccount, arcAccountType, dataType, attachMd5, createDay, dateModel, pageWarpEntity, lang);
    }
}
