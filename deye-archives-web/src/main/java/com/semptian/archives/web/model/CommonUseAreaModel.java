package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 常用通联区域模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseAreaModel implements Comparable{

    /**
     * 通联国家
     */
    private String dstCountry;

    /**
     * 通联城市
     */
    private String dstCity;

    /**
     * 经度
     */
    private String dstIpLongitude;

    /**
     * 纬度
     */
    private String dstIpLatitude;

    /**
     * 统计次数
     */
    private long num;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    @Override
    public int compareTo(Object o) {
        CommonUseAreaModel virtualAccountCountModel = (CommonUseAreaModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
