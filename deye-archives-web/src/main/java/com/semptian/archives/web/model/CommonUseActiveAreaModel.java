package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 常用活动区域模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseActiveAreaModel implements Comparable{

    /**
     * 活动国家
     */
    private String activeCountry;

    /**
     * 活动城市
     */
    private String activeCity;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    /**
     * 经度
     */
    private String dstIpLongitude;

    /**
     * 纬度
     */
    private String dstIpLatitude;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        CommonUseActiveAreaModel virtualAccountCountModel = (CommonUseActiveAreaModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
