package com.semptian.archives.web.controller;

import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.*;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.archives.web.service.service.impl.ImArcServiceImpl;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@CrossOrigin
@RequestMapping(value = "/im_arc_detail")
@Slf4j
@Api
public class ImArcController {


    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @Resource
    HttpServletRequest request;

    @Autowired
    ImArcServiceImpl imArcService;

    @GetMapping("/get_app_name_list.json")
    @ApiOperation(value = "get_app_name_list", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public ReturnModel<?> getAppNameList() {
        String lang = CurrentUserUtil.getLang(request);
        Object result = imArcService.getAppNameList(lang);
        return ReturnModel.getInstance().ok(result);
    }

    @PostMapping("/get_arc_statistical_dimensions.json")
    public Object getArcDetailInfo(@RequestBody ArcStatisticModel arcStatisticModel, HttpServletRequest request) {
        Integer index = arcStatisticModel.getIndex();
        Integer isAll = arcStatisticModel.getIsAll();
        List<ArchiveModel> archiveModelList = arcStatisticModel.getArchiveModels();
        String userId = CurrentUserUtil.getUserId(request);


        Object result = commonArcServiceFacade.getStatisticalDimensions(ArcTypeEnum.IM, archiveModelList, isAll, index, userId);

        if (result == null) {
            return ReturnModel.getInstance().error("Arc not exists!");
        } else {
            return ReturnModel.getInstance().ok(result);
        }
    }

    @GetMapping("/get_frequent_number_info.json")
    @ResponseBody
    @ApiOperation(value = "IM档案常通联账号", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentAccount(@RequestParam(name = "arcId") String arcId,
                                       @RequestParam(name = "arcAccount") String arcAccount,
                                       @RequestParam(name = "arcAccountType") String arcAccountType,
                                       @RequestParam(name = "size", required = false, defaultValue = "5") Integer topSize,
                                       DateModel dateModel,
                                       HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /im_arc_detail/get_frequent_number_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).arcAccountType(arcAccountType).topSize(topSize).dateModel(dateModel).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.IM, ctx, AccessTargetTopEnum.VIRTUAL_ACCOUNT));
    }

    @PostMapping("/communication_trend.json")
    @ResponseBody
    @ApiOperation(value = "IM档案通联日趋势", httpMethod = "POST", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentAccount(@RequestBody ImCommunicationModel model,
                                       HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /im_arc_detail/communication_trend.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcType(ArcTypeEnum.IM.getKey()).arcAccount(model.getArcAccount())
                .arcAccountType(model.getArcAccountType()).targetAccount(model.getTargetAccount()).behaviorType(2)
                .startDay(model.getStartDay()).endDay(model.getEndDay()).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getActiveTrend(ctx));
    }

}
