package com.semptian.archives.web.common.config;

import com.semptian.archives.web.interceptor.ArcPermissionInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Locale;


/**
 * @author: SunQi
 * @create: 2022/05/26
 * desc: 拦截器配置
 **/
@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private ArcPermissionInterceptor arcPermissionInterceptor;

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver slr = new SessionLocaleResolver();
        // 设置默认语言(此处可不用设置，浏览器会根据浏览器的区域参数默认选择语言包)
        slr.setDefaultLocale(Locale.US);
        return slr;
    }

    //国际化拦截器
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor lci = new LocaleChangeInterceptor();
        // 设置参数名
        lci.setParamName("lang");
        return lci;
    }

    //权限校验拦截器
//    @Bean
//    public ArcPermissionInterceptor arcPermissionInterceptor(){
//        return new ArcPermissionInterceptor();
//    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor());
        registry.addInterceptor(arcPermissionInterceptor).addPathPatterns("/**");
    }

}
