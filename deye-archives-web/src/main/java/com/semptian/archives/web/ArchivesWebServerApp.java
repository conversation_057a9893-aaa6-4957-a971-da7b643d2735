package com.semptian.archives.web;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * Date: 2020/9/10 9:00
 * Description: the main entrance of archives engine
 */
//@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.semptian.*"})
@ComponentScan(value = "com.semptian.*")
@EnableScheduling
@EnableAsync
@EnableSwagger2
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
public class ArchivesWebServerApp {

    @Autowired
    private RestTemplateBuilder builder;

    @Bean
    public RestTemplate restTemplate(){
        return builder.build();
    }

    public static void main(String[] args) {
        SpringApplication.run(ArchivesWebServerApp.class, args);
    }
}
