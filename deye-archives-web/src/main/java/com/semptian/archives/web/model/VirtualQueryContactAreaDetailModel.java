package com.semptian.archives.web.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/7/13 12:05
 * @description
 */
@Data
public class VirtualQueryContactAreaDetailModel {
    /**
     * 源ip
     */
    private String strsrcIp;

    /**
     * 目标ip
     */
    private String strdstIp;

    /**
     * 目标ip
     */
    private int ipReverse;

    /**
     * 源账号
     */
    private String virtualAccount;

    /**
     * 目标账号
     */
    private String targetAccount;

    /**
     * 目标账号
     */
    private String mainAccount;

    /**
     * 源国家
     */
    private String srcCountry;

    /**
     * 目的国家
     */
    private String dstCountry;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    /**
     * 统计次数
     */
    private long num;

    /**
     * 协议类型
     */
    private Integer dataType;
}
