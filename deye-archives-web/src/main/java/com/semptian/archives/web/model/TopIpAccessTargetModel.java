package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description TOP IP访问目标模型
 * <AUTHOR>
 * @Date 2021/06/17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopIpAccessTargetModel implements Comparable{

    /**
     * ip
     */
    private String strsrcIp;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        TopIpAccessTargetModel virtualAccountCountModel = (TopIpAccessTargetModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
