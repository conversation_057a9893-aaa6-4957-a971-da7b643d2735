package com.semptian.archives.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.enums.ArcServerReturnCode;
import com.semptian.archives.web.model.EmailPhoneExtractQueryModel;
import com.semptian.archives.web.service.common.enums.AccessTargetTopEnum;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.model.ArcStatisticModel;
import com.semptian.archives.web.service.model.ArchiveModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.arc.ArcContext;
import com.semptian.archives.web.service.service.arc.CommonArcServiceFacade;
import com.semptian.archives.web.service.service.impl.EmailArcServiceImpl;
import com.semptian.base.enums.FieldLightTypeEnum;
import com.semptian.base.enums.ReturnCode;
import com.semptian.base.model.FieldLightHit;
import com.semptian.base.model.HitWordInfo;
import com.semptian.base.service.ReturnModel;
import com.semptian.base.util.HighLightUtils;
import com.semptian.operatelog.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/17 16:37
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/email_arc_detail")
@Slf4j
@Api
public class EmailArcController {

    @Autowired
    EmailArcServiceImpl emailArcService;

    @Resource
    private CommonArcServiceFacade commonArcServiceFacade;

    @Value("${archive.big.package.analysis.threshold:1000000}")
    private int analysisThreshold;

    @Value("${archive.email.package.nestedParse:false}")
    private Boolean nestedParse;


    @GetMapping("/get_relation_email.json")
    @ApiOperation(value = "相关邮箱", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getRelationEmail(
            @RequestParam String arcAccount,
            PageWarpEntity pageWarpEntity,
            DateModel dateModel
    ) {
        return ReturnModel.getInstance().ok(emailArcService.getRelationEmail(arcAccount, dateModel, pageWarpEntity));
    }

    @GetMapping("/get_nickname.json")
    @ApiOperation(value = "昵称记录", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getNickname(
            @RequestParam String arcAccount,
            PageWarpEntity pageWarpEntity,
            DateModel dateModel
    ) {
        return ReturnModel.getInstance().ok(emailArcService.getNickname(arcAccount, pageWarpEntity, dateModel));
    }

    @GetMapping("/get_password.json")
    @ApiOperation(value = "密码记录", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getPassword(
            @RequestParam String arcAccount,
            PageWarpEntity pageWarpEntity,
            DateModel dateModel
    ) {
        return ReturnModel.getInstance().ok(emailArcService.getPassword(arcAccount, pageWarpEntity, dateModel));
    }

    @PostMapping("/get_arc_statistical_dimensions.json")
    @ApiOperation(value = "密码统计", httpMethod = "POST", response = ReturnModel.class)
    public Object getArcDetailInfo(@RequestBody ArcStatisticModel arcStatisticModel, HttpServletRequest request) {
        Integer index = arcStatisticModel.getIndex();
        Integer isAll = arcStatisticModel.getIsAll();
        List<ArchiveModel> archiveModelList = arcStatisticModel.getArchiveModels();
        String userId = CurrentUserUtil.getUserId(request);


        Object result = commonArcServiceFacade.getStatisticalDimensions(ArcTypeEnum.EMAIL, archiveModelList, isAll, index, userId);

        if (result == null) {
            return ReturnModel.getInstance().error("Arc not exists!");
        } else {
            return ReturnModel.getInstance().ok(result);
        }
    }

    @GetMapping("/get_frequent_email_info.json")
    @ResponseBody
    @ApiOperation(value = "邮箱档案常通联邮箱", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryFrequentEmail(@RequestParam(name = "arcId") String arcId, @RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "size", required = false, defaultValue = "5") Integer topSize, DateModel dateModel, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /email_arc_detail/get_frequent_email_info.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        ArcContext ctx = ArcContext.builder().arcId(arcId).arcAccount(arcAccount).topSize(topSize).dateModel(dateModel).build();
        return ReturnModel.getInstance().ok(commonArcServiceFacade.getAccessTargetTop(ArcTypeEnum.EMAIL, ctx, AccessTargetTopEnum.EMAIL));
    }

    @PostMapping("/get_high_frequency_phones_rank.json")
    @ApiOperation(value = "邮件内容提取-号码", httpMethod = "POST", response = ReturnModel.class)
    public Object getHighFrequencyPhonesRank(
            @RequestBody EmailPhoneExtractQueryModel queryModel
    ) {
        PageWarpEntity pageWarpEntity = PageWarpEntity.build(queryModel.getOnPage(), queryModel.getSize(), queryModel.getSortField(), queryModel.getSortType());
        DateModel dateModel = new DateModel(queryModel.getStartDay(), queryModel.getEndDay(), queryModel.getDateOption());

        if (StringUtils.isBlank(pageWarpEntity.getSortField())) {
            pageWarpEntity.setSortField("behaviorNum");
            pageWarpEntity.setSortType(1);
        }

        return emailArcService.getHighFrequencyPhonesRank(queryModel.getArcAccount(), queryModel.getSense(), pageWarpEntity, dateModel);
    }

    @GetMapping("/get_email_originals.json")
    @ApiOperation(value = "查询号码对应的源文件列表", httpMethod = "GET", response = ReturnModel.class)
    public Object getEmailOriginals(
            String arcAccount,
            String phone,
            PageWarpEntity pageWarpEntity,
            DateModel dateModel
    ) {
        return emailArcService.getEmailOriginals(arcAccount, phone, pageWarpEntity, dateModel);
    }

    @GetMapping("/get_email_downloads.json")
    @ApiOperation(value = "查询号码对应的下载文件列表", httpMethod = "GET", response = ReturnModel.class)
    public Object getEmailDownloads(
            @RequestParam String arcAccount,
            String phone,
            PageWarpEntity pageWarpEntity,
            DateModel dateModel
    ) {
        return emailArcService.getEmailDownloads(arcAccount, phone, pageWarpEntity, dateModel);
    }

    @GetMapping("/get_email_statistical.json")
    @ResponseBody
    @ApiOperation(value = "邮箱档案邮件数量统计", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryEmailStatistic(@RequestParam(name = "arcAccount") String arcAccount, @RequestParam(name = "searchType", required = false, defaultValue = "0") Integer searchType,
                                      @RequestParam(name = "keyword", required = false, defaultValue = "") String keyword, @RequestParam(name = "onlyImportant", required = false, defaultValue = "0") Integer onlyImportant,  DateModel dateModel, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /email_arc_detail/get_email_statistical.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        return ReturnModel.getInstance().ok(emailArcService.statisticEmailCount(arcAccount, searchType, keyword, onlyImportant, dateModel));
    }

    @GetMapping("/get_email_details.json")
    @ResponseBody
    @ApiOperation(value = "邮箱档案邮件明细", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object queryEmailDetails(@RequestParam(name = "arcAccount") String arcAccount,
                                    @RequestParam("searchType") Integer searchType,
                                    @RequestParam("emailType") String emailType,
                                    @RequestParam(name = "keyword", required = false, defaultValue = "") String keyword,
                                    @RequestParam("onlyImportant") Integer onlyImportant,
                                    DateModel dateModel, PageWarpEntity pageWarpEntity, HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request /email_arc_detail/get_email_statistical.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        return ReturnModel.getInstance().ok(emailArcService.queryEmailDetails(arcAccount, emailType, searchType, keyword, onlyImportant, dateModel, pageWarpEntity, "LIS", ""));
    }

    @GetMapping("/get_email_original_detail.json")
    @ApiOperation(value = "查询号码对应的下载文件列表", httpMethod = "GET", response = ReturnModel.class)
    public Object getEmailOriginalDetail(
            String authAccount,
            String authType,
            String virtualAccount,
            String filePath,
            String captureDay,
            HttpServletRequest request
    ) {
        String lang = CurrentUserUtil.getLang(request);
        return emailArcService.getEmailOriginalDetail(authAccount, authType,virtualAccount, filePath, captureDay, lang);
    }

    @GetMapping("/parse_package.json")
    @ResponseBody
    @ApiOperation(value = "邮箱档案邮件报文解析", httpMethod = "GET", response = ReturnModel.class)
    @OperateLog
    public Object highlightPackageAnalysis(@RequestParam(name = "filePath") String filePath,
                                           @RequestParam(name = "charset", required = false, defaultValue = "1") String charset,
                                           HttpServletRequest request) {
        String userId = CurrentUserUtil.getUserId(request);
        if (StringUtils.isBlank(userId)) {
            log.warn("request parse_package.json userId empty!");
            return ReturnModel.getInstance().setCode(ArcServerReturnCode.PARAM_IS_BLANK.getCode()).setMsg(I18nUtils.getMessage(ArcServerReturnCode.PARAM_IS_BLANK.getMsg()));
        }

        long start = System.currentTimeMillis();
        ReturnModel returnModel = emailArcService.packageAnalysis(filePath, 1, filePath.hashCode(), nestedParse);
        log.info("request deye-dimension-service packageAnalysis cost [{}]ms, filePath is [{}], nestedParse[{}]",
                (System.currentTimeMillis()-start), filePath, nestedParse);
        if (returnModel.getCode() == ReturnCode.SUCCESS.getCode() && returnModel.getData() != null) {
            JSONObject data = (JSONObject) JSONObject.toJSON(returnModel.getData());
            JSONObject emailModel = data.getJSONObject("emailModel");

            if (emailModel != null) {

                JSONArray attachments = emailModel.getJSONArray("attachments");
                if (attachments != null && !attachments.isEmpty()) {
                    List<String> imgUrls = attachments.stream().map(attachment -> (JSONObject) attachment).filter(attachment -> attachment.getInteger("position") == 1)
                            .map(attachment -> attachment.getString("viewUrl"))
                            .collect(Collectors.toList());

                    String htmlContents = base64ImgReplace(emailModel.getString("htmlContents"), imgUrls);
                    emailModel.put("htmlContents", htmlContents);
                }

//                if (emailModel.getString("htmlContents") != null && emailModel.getString("htmlContents").length() > analysisThreshold) {
//                    emailModel.put("htmlContents", emailModel.getString("htmlContents").substring(0, analysisThreshold));
//                    emailModel.put("isTruncated", 1);
//                } else {
//                    emailModel.put("isTruncated", 0);
//                }
            }
            return data;
        } else {
            return null;
        }
    }

    /**
     * 这块代码存在性能问题
     * 将邮件正文中base64编码的图片内容使用预览URL替换
     * */
//    private String base64ImgReplace(String htmlContent, List<String> imgUrls) {
//        if (imgUrls.isEmpty()) {
//            return htmlContent;
//        }
//
//        for (String imgUrl : imgUrls) {
//            htmlContent = htmlContent.replaceFirst("data:image.*?base64.*?\"", imgUrl + "\"");
//        }
//
//        return htmlContent;
//    }

    private String base64ImgReplace(String htmlContent, List<String> imgUrls) {
        if (imgUrls.isEmpty()) {
            return htmlContent;
        }

        // 预编译正则提升效率
        Pattern pattern = Pattern.compile("data:image[^\"']*?base64[^\"']*?\"");
        Matcher matcher = pattern.matcher(htmlContent);

        StringBuffer sb = new StringBuffer();
        int index = 0;

        while (matcher.find() && index < imgUrls.size()) {
            matcher.appendReplacement(sb, Matcher.quoteReplacement(imgUrls.get(index++) + "\""));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

//    private String base64ImgReplace(String htmlContent, List<String> imgUrls) {
//        if (imgUrls.isEmpty()) {
//            return htmlContent;
//        }
//
//        StringBuilder sb = new StringBuilder(htmlContent.length());
//        int lastIndex = 0;
//        int count = 0;
//
//        for (String imgUrl : imgUrls) {
//            // 查找下一个base64图片位置
//            int start = htmlContent.indexOf("data:image", lastIndex);
//            if (start == -1) break;
//
//            int end = htmlContent.indexOf('"', start + 20); // 20是"data:image"最小长度
//            if (end == -1) break;
//
//            // 追加非替换部分 + 新URL
//            sb.append(htmlContent, lastIndex, start)
//                    .append(imgUrl).append('"');
//
//            lastIndex = end + 1; // 跳过原结尾引号
//            count++;
//
//            // 提前终止循环
//            if (count >= imgUrls.size()) break;
//        }
//
//        // 追加剩余内容
//        if (lastIndex < htmlContent.length()) {
//            sb.append(htmlContent.substring(lastIndex));
//        }
//
//        return sb.toString();
//    }
}
