package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description Radius统计信息模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RadiusStatisticInfoModel {

    /**
     * 活跃总数
     */
    private long totalActive;

    /**
     * 近一周活跃数
     */
    private long oneWeekActive;

    /**
     * 应用总数
     */
    private long totalAppNum;

    /**
     * 近一周应用数
     */
    private long oneWeekAppNum;

    /**
     * 通联区域总数
     */
    private long totalContactArea;

    /**
     * 近一周通联区域数
     */
    private long oneWeekContactArea;

    /**
     * 虚拟账户总数
     */
    private long totalVirtualNum;

    /**
     * 近一周虚拟账户数数
     */
    private long oneWeekVirtualNum;

    /**
     * 阻断总数
     */
    private long totalBlockNum;

    /**
     * 近一周阻断数
     */
    private long oneWeekBlockNum;

    /**
     * 附件总数
     */
    private long totalAttachNum;

    /**
     * 近一周附件数
     */
    private long oneWeekAttachNum;
}
