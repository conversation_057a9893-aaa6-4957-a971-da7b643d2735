package com.semptian.archives.web.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 公共配置信息
 *
 * <AUTHOR>
 * @date 2021/06/09
 */
@Configuration
@ConfigurationProperties("archives.common")
@Data
public class CommonPros {

    /**
     * 常用最近日期间隔天数
     */
    private int commonDayInterval;
}
