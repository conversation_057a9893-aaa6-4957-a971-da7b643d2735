package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 常用密码模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUsePasswordModel implements Comparable{

    /**
     * 密码
     */
    private String password;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        CommonUsePasswordModel virtualAccountCountModel = (CommonUsePasswordModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
