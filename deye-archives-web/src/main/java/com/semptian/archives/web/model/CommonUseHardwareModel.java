package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description 常用硬件类型模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseHardwareModel implements Comparable{

    /**
     * 硬件类型
     */
    private String hardwareType;

    /**
     * 统计次数
     */
    private long num;

    @Override
    public int compareTo(Object o) {
        CommonUseHardwareModel virtualAccountCountModel = (CommonUseHardwareModel) o;
        int times = (int)virtualAccountCountModel.num - (int)this.num;
        return times;
    }
}
