package com.semptian.archives.web.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.semptian.archives.web.common.util.CurrentUserUtil;
import com.semptian.archives.web.core.common.constent.CommonConstent;
import com.semptian.archives.web.dao.archive.entity.ArcUseEntity;
import com.semptian.archives.web.interceptor.ArcPermissionCheck;
import com.semptian.archives.web.service.common.util.ArcCommonUtils;
import com.semptian.archives.web.service.common.util.DateUtils;
import com.semptian.archives.web.service.common.util.I18nUtils;
import com.semptian.archives.web.service.common.util.PermissionUtil;
import com.semptian.archives.web.service.model.ArcInfoModel;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.ArcIndexService;
import com.semptian.archives.web.service.service.ArcUseService;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import com.semptian.archives.web.service.service.impl.ArcCommonServiceImpl;
import com.semptian.base.service.ReturnModel;
import com.semptian.operatelog.annotation.OperateLog;
import com.semptian.redis.template.RedisOps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 档案统计相关的接口
 * <AUTHOR>
 * date 2024/01/10
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/arc_index")
@Slf4j
public class ArcIndexController {

    @Resource
    private ArcIndexService arcIndexService;

    @Resource
    private ArcUseService arcUseService;

    @Resource
    private ArcCommonService arcCommonService;

    @Resource
    private RedisOps redisOps;

    @Resource
    HttpServletRequest request;

    @Resource
    private ArchivesInfoService archivesInfoService;

    //1.1档案维度统计
    @GetMapping("/count_arc_info.json")
    @ApiOperation(value = "arc count", httpMethod = "GET", response = ReturnModel.class)
    @ArcPermissionCheck
    public ReturnModel<?> countArcInfo(HttpServletRequest request) {
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        String userId = CurrentUserUtil.getUserId(request);

        //设置缓存key
        String redisKey = arcCommonService.getRedisKeyByIndexCount(true, "ArcIndexController", "countArcInfo", permissionList);

        if (redisOps.hasKey(redisKey) && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }

        Map<String, Long> amountMap = arcIndexService.statisticArcAmount(permissionList, userId);

        if (MapUtil.isNotEmpty(amountMap)) {
            Date date = new Date();
            long nextHourStart = DateUtils.getNextHourStart(date);
            redisOps.set(redisKey, amountMap, (nextHourStart - date.getTime()) / 1000);
        }

        return ReturnModel.getInstance().ok(amountMap);
    }

    //1.2档案分类统计
    @GetMapping("/count_arc_info_by_type.json")
    @OperateLog
    @ArcPermissionCheck
    @ApiOperation(value = "arc count by type", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> countArcInfoByType(@RequestParam("staticType") String staticType, HttpServletRequest request) {
        //获取用户权限
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        String userId = CurrentUserUtil.getUserId(request);

        //设置缓存key
        String redisKey = arcCommonService.getRedisKeyByIndexCount(true, "ArcIndexController", "countArcInfoByType", staticType, permissionList);

        if (redisOps.hasKey(redisKey) && ArcCommonServiceImpl.isOpen) {
            return ReturnModel.getInstance().ok(redisOps.get(redisKey));
        }

        //按类型查询所有档案数
        Map<String, Object> totalArcAmount = arcIndexService.statisticArcAmountByType(staticType, permissionList,userId);
        Date date = new Date();
        long nextHourStart = DateUtils.getNextHourStart(date);

        if (MapUtil.isNotEmpty(totalArcAmount)) {
            redisOps.set(redisKey, totalArcAmount, (nextHourStart - date.getTime()) / 1000);
        }
        return ReturnModel.getInstance().ok(totalArcAmount);
    }

    /**
     * 1.3用户档案统计 -- 最近使用
     *
     * @param lang   语言
     * @param arcType 档案类型
     * @param request 请求
     * @return 最近使用档案TOP10
     */
    @GetMapping("/get_user_use_latest_arc_info.json")
    @OperateLog
    @ArcPermissionCheck
    @ApiOperation(value = "用户档案统计 -- 最近使用", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getUserUseLatestArcInfo(@RequestParam(value = "lang", required = false, defaultValue = "zh_CN") String lang,
                                               @RequestParam(value = "arcType", required = false, defaultValue = "0") Integer arcType, HttpServletRequest request) {
        //用户档案权限判断
        String userId = CurrentUserUtil.getUserId(request);
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        if (!CommonConstent.ARC_TYPE_ALL.equals(arcType) && !permissionList.contains(arcType)) {
            return ReturnModel.getInstance().error(I18nUtils.getMessage("arc.permission.error"));
        }

        Map<String, Object> data = new HashMap<>(4);
        List<ArcInfoModel> arcInfoModelList = Lists.newArrayList();

        //查询最近使用档案TOP10,根据修改时间大小来排序
        List<String> latestArcIds = arcUseService.latestUseArcTop10(Long.parseLong(userId), arcType, permissionList).stream().map(ArcUseEntity::getArcId).collect(Collectors.toList());
        if (latestArcIds.isEmpty()){
            data.put("latest", arcInfoModelList);
            return ReturnModel.getInstance().ok(data);
        }

        arcInfoModelList = ArcCommonUtils.sortResult(archivesInfoService.getArcInfoByIds(latestArcIds, arcType), latestArcIds);

        data.put("latest", arcInfoModelList);
        return ReturnModel.getInstance().ok(data);
    }


    /**
     * 1.3用户档案统计 -- 最常使用
     *
     * @param lang 预言
     * @param arcType 档案类型
     * @param request 请求
     * @return 最常使用档案TOP10
     */
    @GetMapping("/get_user_use_frequent_arc_info.json")
    @OperateLog
    @ArcPermissionCheck
    @ApiOperation(value = "用户档案统计 -- 最常使用", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getUserUseFrequentArcInfo(@RequestParam(value = "lang", required = false, defaultValue = "zh_CN") String lang,
                                                 @RequestParam(value = "arcType", required = false, defaultValue = "0") Integer arcType, HttpServletRequest request) {
        //用户档案权限判断
        String userId = CurrentUserUtil.getUserId(request);
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        if (!CommonConstent.ARC_TYPE_ALL.equals(arcType) && !permissionList.contains(arcType)) {
            return ReturnModel.getInstance().error(I18nUtils.getMessage("arc.permission.error"));
        }

        Map<String, Object> data = new HashMap<>(4);
        List<ArcInfoModel> arcInfoModelList = Lists.newArrayList();

        //查询经常使用档案TOP10    根据使用次数大小来排序
        List<String> frequentArcIds = arcUseService.frequencyUseArcTop10(Long.parseLong(userId), arcType,permissionList).stream().map(ArcUseEntity::getArcId).collect(Collectors.toList());
        if (frequentArcIds.isEmpty()){
            data.put("frequent", arcInfoModelList);
            return ReturnModel.getInstance().ok(data);
        }

        arcInfoModelList = ArcCommonUtils.sortResult(archivesInfoService.getArcInfoByIds(frequentArcIds, arcType), frequentArcIds);

        data.put("frequent", arcInfoModelList);
        return ReturnModel.getInstance().ok(data);
    }

    //1.5活跃档案top10
    @GetMapping("/get_active_arc_top.json")
    @OperateLog
    @ArcPermissionCheck
    @ApiOperation(value = "active arc top", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getActiveArcTop(@RequestParam(value = "lang", required = false, defaultValue = "zh_CN") String lang,
                                          @RequestParam(value = "isCare", required = false, defaultValue = "0") Integer isCare,
                                          @RequestParam(value = "arcType", required = false, defaultValue = "0") Integer arcType,
                                          HttpServletRequest request) {
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        if (!CommonConstent.ARC_TYPE_ALL.equals(arcType) && !permissionList.contains(arcType)) {
            return ReturnModel.getInstance().error(I18nUtils.getMessage("arc.permission.error"));
        }

        List<ArcInfoModel> list = Lists.newArrayList();
        //过滤网站、应用、协议档案 这三种档案统计活跃/失活无意义
        permissionList = permissionList.stream().filter(key -> !CommonConstent.DOMAIN_APP_LIST.contains(key)).collect(Collectors.toList());
        if (permissionList.isEmpty()) {
            return ReturnModel.getInstance().ok(list);
        }

        if (isCare == 0) {
            // 查询全部档案
            String redisKey = arcCommonService.getRedisKeys("getActiveArcTop", isCare, arcType, lang, permissionList);
            if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
                return ReturnModel.getInstance().ok(redisOps.get(redisKey));
            }

            list = arcIndexService.allActiveArcTop10(arcType, permissionList, lang);
        }
        return ReturnModel.getInstance().ok(list);
    }

    //1.6失活档案
    @GetMapping("/get_lose_active_arc_top.json")
    @OperateLog
    @ArcPermissionCheck
    @ApiOperation(value = "active arc top", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getLostActiveArcTop(@RequestParam(value = "lang", required = false, defaultValue = "zh_CN") String lang,
                                           @RequestParam(value = "isCare", required = false, defaultValue = "0") Integer isCare,
                                           @RequestParam(value = "arcType", required = false, defaultValue = "0") Integer arcType,
                                           HttpServletRequest request) {
        List<Integer> permissionList = PermissionUtil.getArcPermission(request);
        if (!CommonConstent.ARC_TYPE_ALL.equals(arcType) && !permissionList.contains(arcType)) {
            return ReturnModel.getInstance().error(I18nUtils.getMessage("arc.permission.error"));
        }

        List<ArcInfoModel> list = Lists.newArrayList();
        ////过滤网站、应用、协议档案 这三种档案统计活跃/失活无意义
        permissionList = permissionList.stream().filter(key -> !CommonConstent.DOMAIN_APP_LIST.contains(key)).collect(Collectors.toList());
        if(permissionList.isEmpty()){
            return ReturnModel.getInstance().ok(list);
        }

        if (isCare == 0) {
            // 查询全部档案
            String redisKey = arcCommonService.getRedisKeys("getLostActiveArcTop", isCare, arcType, lang,permissionList);
            if (redisOps.hasKey(redisKey) && redisOps.get(redisKey) != null && ArcCommonServiceImpl.isOpen) {
                return ReturnModel.getInstance().ok(redisOps.get(redisKey));
            }

            list = arcIndexService.allLostActiveArcTop10(arcType, permissionList, lang);
        }
        return ReturnModel.getInstance().ok(list);
    }

    @GetMapping("/get_arc_permission.json")
    @ApiOperation(value = "获取用户档案权限", httpMethod = "GET", response = ReturnModel.class)
    public ReturnModel<?> getArcPermission() {
        String userId = CurrentUserUtil.getUserId(request);
        String appId = request.getHeader("appId");
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(appId)) {
            return ReturnModel.getInstance().error("param is blank");
        }
        List<Integer> userArchivePermission = arcIndexService.getUserArchivePermission(appId, userId);
        if (CollUtil.isEmpty(userArchivePermission)) {
            return ReturnModel.getInstance().error();
        }
        return ReturnModel.getInstance().ok(userArchivePermission);
    }
}
