package com.semptian.archives.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 常用应用模型
 * <AUTHOR>
 * @Date 2021/06/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonUseAppModel implements Comparable{

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用类型
     */
    private String appType;

    /**
     * 统计次数
     */
    private long num;

    /**
     * 最早时间
     */
    private long earliestTime;

    /**
     * 最晚时间
     */
    private long latestTime;

    @Override
    public int compareTo(Object o) {
        CommonUseAppModel virtualAccountCountModel = (CommonUseAppModel) o;
        int times = (int) ((int) virtualAccountCountModel.num - this.num);
        return times;
    }
}
