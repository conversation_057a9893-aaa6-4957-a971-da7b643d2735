==============维度服务数据迁移==============

select * from tb_arc_dict where dict_key = 'child_type';

-- 清理 child_type 数据
delete from tb_arc_dict where dict_key = 'child_type';

-- 迁移数据
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) select 'child_type', protocol_id,protocol_name, protocol_name,protocol_name from `dw_dim_v6.4`.dim_protocol_type where parent_id != '-1';

==============全息档案业务sql 改变自动比对==============

-- 修改或者新增
select d.*,t.* from dev_tb_arc_service_sql d
left join test_tb_arc_service_sql t
on d.code =t.code
where (t.ck_sql != d.ck_sql) or (t.code is null)

-- 删除
select d.*,t.* from test_tb_arc_service_sql t
left join dev_tb_arc_service_sql d
on d.code =t.code
where  (d.code is null)

-- 查询
select * from dev_tb_arc_service_sql where code in (select d.code from dev_tb_arc_service_sql d
left join test_tb_arc_service_sql t
on d.code =t.code
where (t.ck_sql != d.ck_sql) or (t.code is null))

delete from tb_arc_service_sql where code in ('');

-- 核查sql
select d.*,t.* from dev_tb_arc_service_sql d
left join tb_arc_service_sql t
on d.code =t.code
where (t.ck_sql != d.ck_sql) or (t.code is null)
