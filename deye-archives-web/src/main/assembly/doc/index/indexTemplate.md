# 1 打开 kibana, 进行索引删除

```

DELETE /deye_v64_quanxidangan_website
DELETE /deye_v64_quanxidangan_app
DELETE /deye_v64_quanxidangan_fix_ip
DELETE /deye_v64_quanxidangan_phone
DELETE /deye_v64_quanxidangan_radius
DELETE /deye_v64_quanxidangan_email
DELETE /deye_v64_quanxidangan_im

```

# 2 索引模板预置

```
在 kinaba 中执行

../templates/*.templates


```


# 3 索引创建


```
PUT deye_v64_quanxidangan_website
PUT deye_v64_quanxidangan_app
PUT deye_v64_quanxidangan_fix_ip
PUT deye_v64_quanxidangan_phone
PUT deye_v64_quanxidangan_radius
PUT deye_v64_quanxidangan_email
PUT deye_v64_quanxidangan_im
```

# 4 索引创建检查

```
GET deye_v64_quanxidangan_website
GET deye_v64_quanxidangan_app
GET deye_v64_quanxidangan_fix_ip
GET deye_v64_quanxidangan_phone
GET deye_v64_quanxidangan_radius
GET deye_v64_quanxidangan_email
GET deye_v64_quanxidangan_im
```

# 5 关于后续索引重建（非必要执行）

# 前提
要确保索引模板是最新的（步骤2）

```
# 创建备份索引
PUT deye_v64_quanxidangan_app_temp

# 数据备份
POST _reindex?wait_for_completion=false
{
  "source": {
    "index": "deye_v64_quanxidangan_app"
  },
  "dest": {
    "index": "deye_v64_quanxidangan_app_temp"
  }
}

# 查看任务状态（要确保任务执行完成）
GET _tasks/1nALVlZDQdKBcQfZ0otNYg:228801640

# 重建原索引
DELETE /deye_v64_quanxidangan_app
PUT deye_v64_quanxidangan_app

# 数据重索引
POST _reindex?wait_for_completion=false
{
  "source": {
    "index": "deye_v64_quanxidangan_app_temp"
  },
  "dest": {
    "index": "deye_v64_quanxidangan_app"
  }
}

# 查看任务状态（要确保任务执行完成）
GET _tasks/1nALVlZDQdKBcQfZ0otNYg:228801640

# 删除备份索引
DELETE deye_v64_quanxidangan_app_temp
```
