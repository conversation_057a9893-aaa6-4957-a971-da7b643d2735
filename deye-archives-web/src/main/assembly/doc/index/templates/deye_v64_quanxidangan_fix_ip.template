PUT _template/deye_v64_quanxidangan_fix_ip
{
    "order": 10,
    "index_patterns": [
        "deye_v64_quanxidangan_fix_ip*"
    ],
    "settings": {
        "index": {
            "number_of_shards": "1",
            "number_of_replicas": "2",
			"refresh_interval" : "60s",
			"unassigned" : {
			  "node_left" : {
				"delayed_timeout" : "15m"
			  }
			},
			"analysis" : {
				"normalizer" : {
					"lowercase_normalizer" : {
					  "filter" : [
						"cjk_width","lowercase"
					  ],
					  "type" : "custom",
					  "char_filter" : [ ]
					}
				},
				"analyzer" : {
					"stanford-core-nlp-r5" : {
					  "filter" : [
						"lowercase"
					  ],
					  "type" : "custom",
					  "tokenizer" : "stanford-core-nlp-r5"
					}
				}
			}
        }
    },
    "mappings": {
        "properties": {
            "id": {
                "index": true,
                "store": true,
                "type": "keyword"
            },
            "archive_type": {
                "index": true,
                "store": true,
                "type": "integer"
            },
            "archive_name": {
                "analyzer": "stanford-core-nlp-r5",
                "index": true,
                "store": true,
                "type": "text",
                "fields": {
                    "keyword": {
                        "normalizer": "lowercase_normalizer",
                        "ignore_above": 256,
                        "store": false,
                        "type": "keyword"
                    }
                }
            },
            "latest_relation_country": {
                   "index": true,
                   "store": true,
                   "type": "text",
                   "fields": {
                       "keyword": {
                           "normalizer": "lowercase_normalizer",
                           "ignore_above": 256,
                           "store": false,
                           "type": "keyword"
                       }
                   }
               },
               "latest_relation_city": {
                   "index": true,
                   "store": true,
                   "type": "text",
                   "fields": {
                       "keyword": {
                           "normalizer": "lowercase_normalizer",
                           "ignore_above": 256,
                           "store": false,
                           "type": "keyword"
                       }
                   }
            },
            "archive_alias": {
                "analyzer": "stanford-core-nlp-r5",
                "index": true,
                "store": true,
                "type": "text",
                "fields": {
                    "keyword": {
                        "normalizer": "lowercase_normalizer",
                        "ignore_above": 256,
                        "store": false,
                        "type": "keyword"
                    }
                }
            },
            "imsi": {
                "index": true,
                "store": true,
                "type": "keyword"
            },
            "imei": {
                "index": true,
                "store": true,
                "type": "keyword"
            },
            "app_type": {
                "index": true,
                "store": true,
                "type": "keyword"
            },
            "app_type_alias": {
                "index": true,
                "store": true,
                "type": "text",
                "fields": {
                    "keyword": {
                        "normalizer": "lowercase_normalizer",
                        "ignore_above": 256,
                        "store": false,
                        "type": "keyword"
                    }
                }
            },
            "file_flag": {
                "index": true,
                "store": true,
                "type": "integer"
            },
            "remark": {
                "analyzer": "stanford-core-nlp-r5",
                "index": true,
                "store": true,
                "type": "text",
                "fields": {
                    "keyword": {
                        "normalizer": "lowercase_normalizer",
                        "ignore_above": 256,
                        "store": false,
                        "type": "keyword"
                    }
                }
            },
            "auth_type": {
                "index": true,
                "store": true,
                "type": "keyword"
            },
            "account_type": {
                "index": true,
                "store": true,
                "type": "integer"
            },
            "fax_num": {
                "index": true,
                "store": true,
                "type": "long"
            },
            "internet_behavior_num": {
                "index": true,
                "store": true,
                "type": "long"
            },
            "latest_relation_ip": {
                "index": true,
                "store": true,
                "type": "ip",
                "ignore_malformed": true
            },
            "sort_score": {
                "index": true,
                "store": true,
                "type": "long"
            },
            "latest_relation_time": {
                "index": true,
                "store": true,
                "type": "long"
            },
            "behavior_num": {
                "index": true,
                "store": true,
                "type": "long"
            },
            "create_time": {
                "index": true,
                "store": true,
                "type": "long"
            },
            "update_time": {
                "index": true,
                "store": true,
                "type": "long"
            }
        }
    },
    "aliases": {}
}