server {
    #访问端口
    listen       8109;
    #服务地址
    server_name  localhost;
    #前端dist包需要放置的路径，可根据需要修改
    location ~ \.(js|css|png|jpg|swf|gif|jpeg|woff|woff2|ttf|docx|mp3|ico)$ {
        root /semptian/web/archives/dist/;
    }
    #前端dist包需要放置的路径，可根据需要修改
    location ~ \.(html)$ {
        add_header Cache-Control 'no-cache,must-revalidate,proxy-revalidate,max-age=0';
        root /semptian/web/archives/dist/;
    }
    #首页配置
	location / {
        root /semptian/web/archives/dist/;
        try_files $uri $uri/ @router;
        index index.html;
    }
    #异常配置
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
    #门户后端接口地址
	location ^~ /portal/ {
        proxy_pass http://deye.com/portal/;
    }
	#消息服务后端接口地址
	location ^~ /message/ {
        proxy_pass http://deye.com/message/;
    }
    #全息档案后端接口地址
	location ^~ /archives_web/ {
        proxy_pass http://deye.com/archives_web/;
    }	
	#线索发现后端接口地址
	location ^~ /clue_scout/ {
        proxy_pass http://deye.com/clue_scout/;
    }
	#标签后端接口地址
	location ^~ /tag/ {
        proxy_pass http://deye.com/tag/;
    }
	#搜索后端接口地址
	location ^~ /search/ {
        proxy_pass http://deye.com/search/;
    }
    #地图
    location ^~ /googlemaps/ {
        proxy_pass http://192.168.80.193:8889/googlemaps/;
    }

}