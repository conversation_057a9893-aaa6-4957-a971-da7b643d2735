<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2
		  http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <id>assembly</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <componentDescriptors>
        <componentDescriptor>src/main/assembly/component.xml</componentDescriptor>
    </componentDescriptors>
    <fileSets>
        <fileSet>
            <directory>src/main/assembly/apollo</directory>
            <outputDirectory>apollo</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>src/main/assembly/sql</directory>
            <outputDirectory>sql</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>src/main/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>src/main/assembly/geoip2</directory>
            <outputDirectory>geoip2</outputDirectory>
            <fileMode>0644</fileMode>
            <includes>
                <include>ip.mmdb</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/assembly/doc</directory>
            <outputDirectory>doc</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>src/main/assembly/nginx</directory>
            <outputDirectory>nginx</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>src/main/assembly/sql</directory>
            <outputDirectory>sql</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>conf</outputDirectory>
            <fileMode>0644</fileMode>
            <includes>
                <include>application.yml</include>
                <include>bootstrap.yml</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>target</directory>
            <outputDirectory>lib</outputDirectory>
            <fileMode>0644</fileMode>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory></outputDirectory>
            <fileMode>0644</fileMode>
            <includes>
                <include>README.md</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>