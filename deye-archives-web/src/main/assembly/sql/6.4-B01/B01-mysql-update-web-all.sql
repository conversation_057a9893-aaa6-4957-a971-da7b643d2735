INSERT INTO `tb_arc_service_sql` (`id`, `code`, `my_sql`, `ck_sql`) VALUES ('100416', 'CONNECTION_PHONE_CK_BEHAVIOR_NUM_NOT_TODAY', NULL, 'SELECT\r\n	src_number virtual_account,\r\n	dst_number target_account,\r\n	call_tag,\r\n	sum(behavior_num) AS relationNum\r\nFROM\r\n	ads_phone_archive_action_detail_by_day_all\r\nWHERE\r\n	((src_number IN ({$fromAccounts}) AND dst_number IN ({$toAccounts}))\r\n		OR (src_number IN ({$toAccounts}) AND dst_number IN ({$fromAccounts})))\r\n	AND capture_day >={startDay}\r\n	AND capture_day <= {endDay}\r\n	AND call_tag IN ({$callTag})\r\n	AND src_number != dst_number\r\nGROUP BY\r\n	virtual_account,\r\n	target_account,\r\n	call_tag;');
INSERT INTO `tb_arc_service_sql` (`id`, `code`, `my_sql`, `ck_sql`) VALUES ('100417', 'CONNECTION_PHONE_CK_BEHAVIOR_NUM_WITH_TODAY', NULL, 'SELECT\r\n	t1.virtual_account virtual_account,\r\n	t1.target_account target_account,\r\n	t1.call_tag call_tag,\r\n	sum(t1.behavior_num) relationNum\r\nFROM\r\n	(\r\n		SELECT\r\n			src_number virtual_account,\r\n			dst_number target_account,\r\n			call_tag,\r\n			sum(behavior_num) AS behavior_num\r\n		FROM\r\n			ads_phone_archive_action_detail_by_hour_all\r\n		WHERE\r\n	((src_number IN ({$fromAccounts}) AND dst_number IN ({$toAccounts}))\r\n		OR (src_number IN ({$toAccounts}) AND dst_number IN ({$fromAccounts})))\r\n		AND capture_day = {today}\r\n		AND call_tag IN ({$callTag})\r\n		AND src_number != dst_number\r\n		GROUP BY\r\n			virtual_account,\r\n			target_account,\r\n			call_tag\r\n		UNION ALL\r\n			SELECT\r\n				src_number virtual_account,\r\n				dst_number target_account,\r\n				call_tag,\r\n				sum(behavior_num) AS behavior_num\r\n			FROM\r\n				ads_phone_archive_action_detail_by_day_all\r\n			WHERE\r\n	((src_number IN ({$fromAccounts}) AND dst_number IN ({$toAccounts}))\r\n		OR (src_number IN ({$toAccounts}) AND dst_number IN ({$fromAccounts})))\r\n			AND capture_day >={startDay}\r\n			AND capture_day <= {endDay}\r\n			AND call_tag IN ({$callTag})\r\n			AND src_number != dst_number\r\n			GROUP BY\r\n				virtual_account,\r\n				target_account,\r\n				call_tag\r\n	) t1\r\nGROUP BY\r\n	virtual_account,\r\n	target_account,\r\n	call_tag;');
