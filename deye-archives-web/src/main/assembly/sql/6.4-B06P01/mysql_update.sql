-- 变更 code
delete from tb_arc_service_sql where `code` in ('PHONE_COMMUNICATION_RANK','EMAIL_TOP_EMAIL_ACCOUNT','IM_TOP_COMMUNICATE_ACCOUNT','EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER','IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER','PHONE_ONE_WEEK_FAX_NUMBER','PHONE_ONE_WEEK_CALL_NUMBER','PHONE_ONE_WEEK_SMS_NUMBER');

INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_COMMUNICATION_RANK', NULL, 'select connect_number number, sum(behaviorNum) total,\r\n`sum`(`if`(`callTag` = 2, `behaviorNum`, 0)) `call`,\r\n`sum`(`if`(`callTag` = 1, `behaviorNum`, 0)) `fax`,\r\n`sum`(`if`(`callTag` = 3, `behaviorNum`, 0)) `sms`\r\nfrom (\r\nselect \r\ncase \r\nwhen src_number = \'${arcAccount}\' then dst_number  \r\nwhen dst_number = \'${arcAccount}\' then src_number  \r\nend connect_number,\r\ncall_tag callTag,\r\nsum(behavior_num) behaviorNum\r\nfrom ${dws}.dws_element_behavior_call_day \r\nwhere (src_number = \'${arcAccount}\' or dst_number = \'${arcAccount}\') and src_number <> \'\' and dst_number <> \'\' and capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\n${call_tag_condition}\r\ngroup by connect_number,call_tag\r\n) as t group by number');
INSERT INTO `tb_arc_service_sql` (`code`, `my_sql`, `ck_sql`) VALUES ('EMAIL_TOP_EMAIL_ACCOUNT', NULL, 'SELECT\r\n	email,\r\n	sum(num) as num\r\nFROM\r\n	(\r\n	SELECT\r\n		1 as flag,\r\n		target_account as email,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account = {email}\r\n		and target_account <> {email}\r\n		and target_account <> \'\' ${dataType}\r\n	GROUP BY\r\n		target_account\r\nUNION\r\n	SELECT\r\n		2 as flag,\r\n		virtual_account as email,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account = {email}\r\n		and virtual_account <> {email}\r\n		and virtual_account <> \'\' ${dataType}\r\n	GROUP BY\r\n		virtual_account\r\n) t\r\nGROUP BY\r\n	email\r\nORDER BY\r\n	num DESC');
INSERT INTO `tb_arc_service_sql` (`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOP_COMMUNICATE_ACCOUNT', NULL, 'SELECT\r\n	account,\r\n	sum(num) num\r\nFROM\r\n	(\r\n	SELECT\r\n		1 as flag,\r\n		target_account as account,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account = {imAccount} ${app_type} ${dataType}\r\n		and target_account <> {imAccount}\r\n		and target_account <> \'\'\r\n	GROUP BY\r\n		target_account\r\nUNION\r\n	SELECT\r\n		2 as flag,\r\n		virtual_account as account,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account = {imAccount} ${app_type} ${dataType}\r\n		and virtual_account <> {imAccount}\r\n		and virtual_account <> \'\'\r\n	GROUP BY\r\n		virtual_account\r\n) t\r\nGROUP BY\r\n	account\r\nORDER BY\r\n	num DESC');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_ONE_WEEK_SMS_NUMBER', NULL, 'select\r\n		${phone} as arcName,\r\n		sum(behavior_num) as nums\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day} and call_tag = 3\r\n		and (dst_number in (${phone}) or src_number in (${phone}) )\r\n	group by\r\n		arcName;');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_ONE_WEEK_CALL_NUMBER', NULL, 'select\r\n		${phone} as arcName,\r\n		sum(behavior_num) as nums\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day} and call_tag = 2\r\n		and (dst_number in (${phone}) or src_number in (${phone}) )\r\n	group by\r\n		arcName;');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_ONE_WEEK_FAX_NUMBER', NULL, 'select\r\n		${phone} as arcName,\r\n		sum(behavior_num) as nums\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day} and call_tag = 1\r\n		and (dst_number in (${phone}) or src_number in (${phone}) )\r\n	group by\r\n		arcName;');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER', NULL, 'SELECT \r\n	arcName,\r\n	count(distinct account) as nums\r\nfrom\r\n	(\r\n	select\r\n		1 as flag,\r\n		target_account as arcName,\r\n		virtual_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account in (${email}) ${dataType}\r\n		and virtual_account <> ${email}\r\n   	and virtual_account <> \'\'\r\nUNION\r\n	select\r\n		2 as flag,\r\n		virtual_account as arcName,\r\n		target_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${email}) ${dataType}\r\n		and target_account <> ${email}\r\n   	and target_account <> \'\'\r\n) t\r\nwhere account <> \'\'\r\ngroup by\r\n	arcName;');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER', NULL, 'SELECT\r\n	arcName,\r\n	appType,\r\n	count(distinct account) as nums from\r\n(\r\n	SELECT\r\n		1 as flag,\r\n		virtual_account as arcName,\r\n		virtual_app_type as appType,\r\n		target_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${imAccount}) ${app_type} ${dataType}\r\n		and target_account <> \'\'\r\n		and target_account <> ${imAccount}\r\nUNION\r\n	SELECT\r\n		2 as flag,\r\n		target_account as arcName,\r\n		virtual_app_type as appType,\r\n		virtual_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account in (${imAccount}) ${app_type} ${dataType}\r\n		and virtual_account <> \'\'\r\n		and virtual_account <> ${imAccount}\r\n) t\r\ngroup by\r\n	arcName,\r\n	appType;');
