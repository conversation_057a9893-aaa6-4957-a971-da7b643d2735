CREATE TABLE IF NOT EXISTS `tb_arc_drill_down_field_config` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `data_type` VARCHAR(16) NOT NULL COMMENT '协议类型',
    `field` VARCHAR(64) NOT NULL COMMENT '字段名称',
    `desc_zh` VARCHAR(64) NOT NULL COMMENT '中文描述',
    `desc_fr` VARCHAR(64) NOT NULL COMMENT '法文描述',
    `sort` INT NOT NULL COMMENT '排序',
    `is_core` INT NOT NULL DEFAULT '0' COMMENT '是否核心业务字段：0=否，1=是，默认为0',
    `is_hide` TINYINT NOT NULL DEFAULT '0' COMMENT '是否隐藏字段：0=否，1=是，默认为0',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `modify_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储详细字段配置的表';




