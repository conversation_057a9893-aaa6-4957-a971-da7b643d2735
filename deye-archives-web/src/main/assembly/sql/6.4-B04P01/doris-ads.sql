
DROP TABLE IF EXISTS ads_archive_im_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_im_statistics (
  `virtual_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `virtual_app_type` VARCHAR(128) NOT NULL COMMENT '虚拟账号应用类型',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为总数',
  `connect_account_total` BITMAP BITMAP_UNION COMMENT '通联账号总数',
  `active_area_total` BITMAP BITMAP_UNION COMMENT '活跃区域总数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `important_target_total` BITMAP BITMAP_UNION COMMENT '重要目标总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`virtual_account`, `virtual_app_type`)
COMMENT 'IM档案统计表'
DISTRIBUTED BY HASH(`virtual_account`) BUCKETS 2
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_email_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_email_statistics (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `behavior_num` BIGINT SUM NOT NULL COMMENT '行为总数',
  `connect_account_total` BITMAP BITMAP_UNION COMMENT '通联邮箱总数',
  `active_area_total` BITMAP BITMAP_UNION COMMENT '活跃区域总数',
  `nickname_total` BITMAP BITMAP_UNION COMMENT '昵称总数',
  `password_total` BITMAP BITMAP_UNION COMMENT '密码总数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `important_target_total` BITMAP BITMAP_UNION COMMENT '重要目标总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`virtual_account`)
COMMENT 'Email档案统计表'
DISTRIBUTED BY HASH(`virtual_account`) BUCKETS 100
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_phone_base_station_stay_detail;  

CREATE TABLE IF NOT EXISTS ads_archive_phone_base_station_stay_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `phone_stay_id` VARCHAR(64) NOT NULL COMMENT '号码基站驻留id',
  `base_station_no` VARCHAR(64) NOT NULL COMMENT '基站编号',
  `station_longitude` DECIMAL(10,7) COMMENT '基站经度',
  `station_latitude` DECIMAL(10,7) COMMENT '基站纬度',
  `address` VARCHAR(256) COMMENT '基站地址',
  `network_type` VARCHAR(16) COMMENT '网络类型',
  `device_id` VARCHAR(64) COMMENT '设备id',
  `capture_time` BIGINT COMMENT '捕获时间',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `in_time` BIGINT COMMENT '进入基站时间',
  `out_time` BIGINT COMMENT '离开时间',
  `stay_time` BIGINT COMMENT '驻留时长（秒）',
  `stay_num` BIGINT COMMENT '驻留次数'
)
DUPLICATE KEY (`phone_number`)
COMMENT '号码档案基站信息天表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`phone_number`, `phone_stay_id`, `base_station_no`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

DROP TABLE IF EXISTS ads_archive_phone_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_phone_statistics (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '号码',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `internet_behavior_num` BIGINT SUM COMMENT '上网行为总数',
  `connected_area` BITMAP BITMAP_UNION COMMENT '通联区域总数',
  `call_num` BIGINT SUM COMMENT '通话次数',
  `message_num` BIGINT SUM COMMENT '短信次数',
  `fax_num` BIGINT SUM COMMENT '传真次数',
  `apps` BITMAP BITMAP_UNION COMMENT '应用个数',
  `virtual_account` BITMAP BITMAP_UNION COMMENT '虚拟账号个数',
  `block_apps` BITMAP BITMAP_UNION COMMENT '阻断应用个数',
  `block_behavior_num` BIGINT SUM COMMENT '阻断行为次数',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最近活跃国家（基于基站数据）',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最近活跃城市（基于基站数据）',
  `imei` VARCHAR(64) REPLACE COMMENT '国际移动设备身份码',
  `imsi` VARCHAR(64) REPLACE COMMENT '国际移动用户识别码',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`)
COMMENT '号码档案统计表'
DISTRIBUTED BY HASH(`phone_number`) BUCKETS 20
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);
