
DROP TABLE IF EXISTS dws_element_behavior_phone_app_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_phone_app_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`phone_number`, `app_name`, `app_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '号码档案应用明细小时表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`phone_number`, `app_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);



DROP TABLE IF EXISTS dws_element_behavior_phone_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_phone_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `behavior_type` TINYINT NOT NULL COMMENT '行为类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`, `behavior_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '号码档案行为明细小时表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`phone_number`, `behavior_type`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

DROP TABLE IF EXISTS dws_element_relation_phone_virtual_account_detail;  

CREATE TABLE IF NOT EXISTS dws_element_relation_phone_virtual_account_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `virtual_account_type` VARCHAR(16) NOT NULL COMMENT '虚拟账号类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `app_type` VARCHAR(128) NOT NULL COMMENT '虚拟账号应用类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`phone_number`, `virtual_account`, `virtual_account_type`, `data_type`, `app_type`, `capture_day`, `capture_month`)
COMMENT '号码档案关联虚拟账号'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`phone_number`, `virtual_account`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

DROP TABLE IF EXISTS dws_element_phone_entity;  

CREATE TABLE IF NOT EXISTS dws_element_phone_entity (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `insert_time` BIGINT min COMMENT '创建时间',
  `latest_relation_time` BIGINT max COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`)
COMMENT '号码档案建档实体表'
DISTRIBUTED BY HASH(`phone_number`) BUCKETS 20
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);
