UPDATE`tb_arc_drill_down_field_config` SET `desc_fr` = 'Annexe MD5' WHERE data_type='email' and field = 'attach_md5s';


delete from tb_arc_dict where dict_key = 'network_type';


INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('network_type', '1', '2G', '2G', '2G');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('network_type', '2', '3G', '3G', '3G');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('network_type', '3', '4G', '4G', '4G');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('network_type', '4', '5G', '5G', '5G');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('network_type', '5', 'MIX', 'MIX', 'MIX');


delete from tb_arc_service_sql where code in ('RADIUS_ONE_WEEK_ACTIVE_NUMBER','QUERY_ATTACHMENT_INFO_FILE_PATH','PHONE_RELATION_EXTENSION','QUERY_ATTACHMENT_INFO','COMMON_PHONE_DAY_HOUR_ANALYZE','VIRTUAL_RELATE_AUTH_ACCOUNT','PHONE_AREA_HOT_TIME_STATISTICS','PHONE_IMEI_LIST','EMAIL_LAST_RELATION_AREA_STATISTICS','IM_LAST_RELATION_AREA_STATISTICS');

INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ('QUERY_ATTACHMENT_INFO', NULL, 'SELECT\r\n	max(attach_path) attachPath,\r\n	data_type dataType,\r\n	attach_md5 attachMd5,\r\n	max(attach_suffix) AS attachType,\r\n	max(attach_name) AS attachName,\r\n	max(attach_size) AS attachSize,\r\n	array_join(collect_set(CASE \r\n                WHEN virtual_account = \'\' THEN NULL \r\n                ELSE virtual_account \r\n            END), \',\') AS virtualAccount,\r\n	array_join(collect_set(CASE \r\n                WHEN auth_account = \'\' THEN NULL \r\n                ELSE auth_account \r\n            END), \',\') AS authAccount,\r\n	MIN(earliest_relation_time ) AS firstAppearTime,\r\n	MAX(latest_relation_time )AS lastAppearTime,\r\n	SUM(behavior_num) AS appearTimes\r\nFROM\r\n	${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n	${account_type_condition}  in (${arc_account})\r\n	and attach_path <> \'\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}  ${dataType}\r\n	${fileType} \r\n	${keyword}\r\nGROUP BY\r\n	attach_md5,\r\n	data_type');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ('RADIUS_ONE_WEEK_ACTIVE_NUMBER', NULL, 'SELECT\r\n	auth_account as arcName,\r\n	SUM(behavior_num) AS nums\r\nFROM\r\n	${dws}.dws_element_behavior_auth_account_detail\r\nWHERE\r\n	capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and auth_account in (${authAccount}) AND auth_type = {authAccountType} AND behavior_type = 3\r\nGROUP BY\r\n	auth_account;');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ('COMMON_PHONE_DAY_HOUR_ANALYZE', NULL, 'SELECT SUM(behavior_num) as num, capture_hour as `period` FROM ${dws}.dws_element_behavior_phone_detail WHERE capture_day >= {start_day} and capture_day <= {end_day} and phone_number = {arc_account} and behavior_type = {behavior_type} GROUP BY capture_hour ORDER BY capture_hour');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'QUERY_ATTACHMENT_INFO_FILE_PATH', NULL, 'SELECT\r\n	file_path AS filePath, ${account_field} AS account\r\nfrom\r\n	${dws}.dws_element_behavior_attachment_day\r\nwhere\r\n	${account_type_condition}  in ({arc_account})\r\n	and attach_md5 = {attach_md5}\r\n	and data_type = {data_type}\r\n	and attach_path <> \'\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_RELATION_EXTENSION', NULL, 'select `number`,`total`,`call`,fax,sms from (\r\nselect connect_number `number`, sum(behaviorNum) `total`,\r\nsum(if(callTag = 2, behaviorNum, 0)) `call`,\r\nsum(if(callTag = 1, behaviorNum, 0)) fax,\r\nsum(if(callTag = 3, behaviorNum, 0)) sms\r\nfrom (\r\nselect \r\ncase \r\nwhen src_number = \'${arcAccount}\' then dst_number  \r\nwhen dst_number = \'${arcAccount}\' then src_number  \r\nend connect_number,\r\ncall_tag callTag,\r\nsum(behavior_num) behaviorNum\r\nfrom ${dws}.dws_element_behavior_call_day \r\nwhere (src_number = \'${arcAccount}\' or dst_number = \'${arcAccount}\') and capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\n${call_tag_condition}\r\nand spam_flag=0\r\ngroup by connect_number,call_tag\r\n) as t group by number\r\n) as t1 ${minLinkCountCondition}');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'VIRTUAL_RELATE_AUTH_ACCOUNT', NULL, 'select auth_account account,auth_type accountType,earliest_relation_time earliestTime,latest_relation_time latestTime,num from (\r\n(select phone_number auth_account, \'1020004\' auth_type, min(earliest_relation_time) earliest_relation_time, max(latest_relation_time) latest_relation_time, sum(behavior_num) num\r\nfrom ${dws}.dws_element_relation_phone_virtual_account_detail\r\nwhere virtual_account = {virtual_account}\r\nand data_type = {data_type}\r\n${app_type_condition}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by phone_number)\r\nunion ALL \r\n(select auth_account, auth_type, min(earliest_relation_time) earliest_relation_time, max(latest_relation_time) latest_relation_time, sum(behavior_num) num\r\nfrom ${dws}.dws_element_relation_auth_account_virtual_account_detail\r\nwhere virtual_account = {virtual_account}\r\nand data_type = {data_type}\r\n${app_type_condition}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by auth_account, auth_type)\r\n) as  t\r\nwhere 1=1\r\n${auth_account_condition}\r\n${auth_type_condition}');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_TIME_STATISTICS', NULL, 'select CONCAT(FLOOR(capture_hour / ${interval}) * ${interval}, \'-\', (FLOOR(capture_hour / ${interval}) * ${interval} + ${interval})) timeRange,\r\nsum(stay_num) stayCount, count(DISTINCT base_station_no) areaCount, truncate(sum(stay_time)/3600, 1) stayHour\r\nfrom ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n${keyword_condition}\r\ngroup by timeRange\r\n${sort} ${top}');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_IMEI_LIST', NULL, 'select device_id imei, min(capture_time) earliestTime, max(capture_time) latestTime from \r\nads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {arcAccount}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n${keyword_condition}\r\ngroup by device_id ');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_LAST_RELATION_AREA_STATISTICS', NULL, 'SELECT\r\n	latest_relation_country AS latestRelationCountry,\r\n	latest_relation_city AS latestRelationCity\r\nfrom\r\n	ads_archive_email_statistics\r\nwhere\r\n	virtual_account = {arc_account}');
INSERT INTO `tb_arc_service_sql`( `code`, `my_sql`, `ck_sql`) VALUES ( 'IM_LAST_RELATION_AREA_STATISTICS', NULL, 'SELECT\r\n	latest_relation_country AS latestRelationCountry,\r\n	latest_relation_city AS latestRelationCity\r\nfrom\r\n	ads_archive_im_statistics\r\nwhere\r\n	virtual_account = {arc_account}\r\n	${app_type} ');
