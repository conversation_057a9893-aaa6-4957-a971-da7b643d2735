
DROP TABLE IF EXISTS ads_archive_im_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_im_statistics (
  `virtual_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `virtual_app_type` VARCHAR(128) NOT NULL COMMENT '虚拟账号应用类型',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为总数',
  `connect_account_total` BITMAP BITMAP_UNION COMMENT '通联账号总数',
  `active_area_total` BITMAP BITMAP_UNION COMMENT '活跃区域总数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `important_target_total` BITMAP BITMAP_UNION COMMENT '重要目标总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`virtual_account`, `virtual_app_type`)
COMMENT 'IM档案统计表'
DISTRIBUTED BY HASH(`virtual_account`) BUCKETS 2
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_email_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_email_statistics (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `behavior_num` BIGINT SUM NOT NULL COMMENT '行为总数',
  `connect_account_total` BITMAP BITMAP_UNION COMMENT '通联邮箱总数',
  `active_area_total` BITMAP BITMAP_UNION COMMENT '活跃区域总数',
  `nickname_total` BITMAP BITMAP_UNION COMMENT '昵称总数',
  `password_total` BITMAP BITMAP_UNION COMMENT '密码总数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `important_target_total` BITMAP BITMAP_UNION COMMENT '重要目标总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`virtual_account`)
COMMENT 'Email档案统计表'
DISTRIBUTED BY HASH(`virtual_account`) BUCKETS 100
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_phone_base_station_path_detail;  

CREATE TABLE IF NOT EXISTS ads_archive_phone_base_station_path_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `src_station_no` VARCHAR(64) NOT NULL COMMENT '源基站编号',
  `src_network_type` VARCHAR(16) COMMENT '网络类型',
  `src_station_longitude` DECIMAL(10,7) COMMENT '源基站经度',
  `src_station_latitude` DECIMAL(10,7) COMMENT '源基站纬度',
  `src_address` VARCHAR(256) COMMENT '源基站地址',
  `dst_station_no` VARCHAR(64) NOT NULL COMMENT '目的基站编号',
  `dst_network_type` VARCHAR(16) COMMENT '网络类型',
  `dst_station_longitude` DECIMAL(10,7) COMMENT '目的基站经度',
  `dst_station_latitude` DECIMAL(10,7) COMMENT '目的基站纬度',
  `dst_address` VARCHAR(256) COMMENT '目地源基站地址',
  `capture_time` BIGINT NOT NULL COMMENT '基站变更时间',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份'
)
DUPLICATE KEY (`phone_number`)
COMMENT '号码档案基站信息天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`, `src_station_no`, `dst_station_no`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_phone_base_station_stay_detail;  

CREATE TABLE IF NOT EXISTS ads_archive_phone_base_station_stay_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `phone_stay_id` VARCHAR(64) NOT NULL COMMENT '号码基站驻留id',
  `base_station_no` VARCHAR(64) NOT NULL COMMENT '基站编号',
  `station_longitude` DECIMAL(10,7) COMMENT '基站经度',
  `station_latitude` DECIMAL(10,7) COMMENT '基站纬度',
  `address` VARCHAR(256) COMMENT '基站地址',
  `network_type` VARCHAR(16) COMMENT '网络类型',
  `device_id` VARCHAR(64) COMMENT '设备id',
  `capture_time` BIGINT COMMENT '捕获时间',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `in_time` BIGINT COMMENT '进入基站时间',
  `out_time` BIGINT COMMENT '离开时间',
  `stay_time` BIGINT COMMENT '驻留时长（秒）',
  `stay_num` BIGINT COMMENT '驻留次数'
)
DUPLICATE KEY (`phone_number`)
COMMENT '号码档案基站信息天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`, `phone_stay_id`, `base_station_no`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_phone_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_phone_statistics (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '号码',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `internet_behavior_num` BIGINT SUM COMMENT '上网行为总数',
  `connected_area` BITMAP BITMAP_UNION COMMENT '通联区域总数',
  `call_num` BIGINT SUM COMMENT '通话次数',
  `message_num` BIGINT SUM COMMENT '短信次数',
  `fax_num` BIGINT SUM COMMENT '传真次数',
  `apps` BITMAP BITMAP_UNION COMMENT '应用个数',
  `virtual_account` BITMAP BITMAP_UNION COMMENT '虚拟账号个数',
  `block_apps` BITMAP BITMAP_UNION COMMENT '阻断应用个数',
  `block_behavior_num` BIGINT SUM COMMENT '阻断行为次数',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最近活跃国家（基于基站数据）',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最近活跃城市（基于基站数据）',
  `imei` VARCHAR(64) REPLACE COMMENT '国际移动设备身份码',
  `imsi` VARCHAR(64) REPLACE COMMENT '国际移动用户识别码',
  `archive_flag` INT MAX COMMENT '建档标识，0-不建档；1-建档',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`)
COMMENT '号码档案统计表'
DISTRIBUTED BY HASH(`phone_number`) BUCKETS 20
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_auth_account_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_auth_account_statistics (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `behavior_num` BIGINT SUM COMMENT '行为总次数',
  `apps` BITMAP BITMAP_UNION COMMENT '应用总数',
  `connected_area` BITMAP BITMAP_UNION COMMENT '通联区域总数',
  `virtual_account` BITMAP BITMAP_UNION COMMENT '虚拟账号总数',
  `block_num` BIGINT SUM COMMENT '阻断行为总次数',
  `block_apps` BITMAP BITMAP_UNION COMMENT '阻断应用个数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_ip` VARCHAR(64) REPLACE COMMENT '最后关联ip',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`auth_account`, `auth_type`)
COMMENT 'radius档案统计表'
DISTRIBUTED BY HASH(`auth_account`) BUCKETS 2
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_app_ip_visitor_detail_day;  

CREATE TABLE IF NOT EXISTS ads_archive_app_ip_visitor_detail_day (
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `ip` VARCHAR(64) NOT NULL COMMENT '访问者ip',
  `visitor_country` VARCHAR(128) COMMENT '访问者国家',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `nf_behavior_num` BIGINT SUM COMMENT 'nf访问总数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`app_name`, `app_type`, `net_action`, `data_type`, `ip`, `visitor_country`, `capture_day`, `capture_month`)
COMMENT '应用档案IP访问明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`app_name`, `ip`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "20G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_app_important_target_visitor_detail_day;  

CREATE TABLE IF NOT EXISTS ads_archive_app_important_target_visitor_detail_day (
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `important_target` VARCHAR(128) NOT NULL COMMENT '重要目标',
  `important_target_type` VARCHAR(64) NOT NULL COMMENT '重点目标类型',
  `visitor_country` VARCHAR(128) COMMENT '访问者国家',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `nf_behavior_num` BIGINT SUM COMMENT 'nf访问总数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`app_name`, `app_type`, `net_action`, `data_type`, `important_target`, `important_target_type`, `visitor_country`, `capture_day`, `capture_month`)
COMMENT '应用档案重要目标访问明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`app_name`, `important_target`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_app_arc_visitor_detail_day;  

CREATE TABLE IF NOT EXISTS ads_archive_app_arc_visitor_detail_day (
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `visitor_archive_name` VARCHAR(1024) NOT NULL COMMENT '档案访问者名称',
  `visitor_archive_type` TINYINT NOT NULL COMMENT '档案类型',
  `visitor_country` VARCHAR(128) COMMENT '访问者国家',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `nf_behavior_num` BIGINT SUM COMMENT 'nf访问总数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`app_name`, `app_type`, `net_action`, `data_type`, `visitor_archive_name`, `visitor_archive_type`, `visitor_country`, `capture_day`, `capture_month`)
COMMENT '应用档案访问明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`app_name`, `visitor_archive_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "15G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_app_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_app_statistics (
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `arc_visitor_area` BITMAP BITMAP_UNION COMMENT '访问档案区域总数',
  `arc_visitor_num` BITMAP BITMAP_UNION COMMENT '访问档案行为总次数',
  `block_num` BIGINT SUM COMMENT '阻断行为总次数',
  `important_target_num` BITMAP BITMAP_UNION COMMENT '重点访问目标个数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_ip` VARCHAR(64) REPLACE COMMENT '最后关联ip',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`app_name`, `app_type`)
COMMENT '应用档案统计表'
DISTRIBUTED BY HASH(`app_name`) BUCKETS 1
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_website_ip_visitor_detail_day;  

CREATE TABLE IF NOT EXISTS ads_archive_website_ip_visitor_detail_day (
  `domain` VARCHAR(1024) NOT NULL COMMENT '域名',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `ip` VARCHAR(64) NOT NULL COMMENT '访问者ip',
  `visitor_country` VARCHAR(128) COMMENT '访问者国家',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `nf_behavior_num` BIGINT sum COMMENT 'nf访问总数',
  `earliest_relation_time` BIGINT  min COMMENT '最早关联时间',
  `latest_relation_time` BIGINT  max COMMENT '最后关联时间'
)
AGGREGATE KEY (`domain`, `net_action`, `data_type`, `ip`,`visitor_country`, `capture_day`, `capture_month`)
COMMENT '网站档案IP访问明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`domain`, `ip`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "20G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_website_important_target_visitor_detail_day;  

CREATE TABLE IF NOT EXISTS ads_archive_website_important_target_visitor_detail_day (
  `domain` VARCHAR(1024) NOT NULL COMMENT '域名',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `important_target` VARCHAR(64) NOT NULL COMMENT '重点目标',
  `important_target_type` VARCHAR(64) NOT NULL COMMENT '重点目标类型',
  `visitor_country` VARCHAR(128) COMMENT '访问者国家',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `nf_behavior_num` BIGINT SUM COMMENT 'nf非阻断访问总数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT  MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`domain`, `net_action`, `data_type`, `important_target`, `important_target_type`,`visitor_country`, `capture_day`, `capture_month`)
COMMENT '网站档案重点目标访问明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`domain`, `important_target`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_website_arc_visitor_detail_day;  

CREATE TABLE IF NOT EXISTS ads_archive_website_arc_visitor_detail_day (
  `domain` VARCHAR(1024) NOT NULL COMMENT '域名',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `visitor_archive_name` VARCHAR(1024) NOT NULL COMMENT '档案访问者名称',
  `visitor_archive_type` TINYINT NOT NULL COMMENT '档案类型',
  `visitor_country` VARCHAR(128) COMMENT '访问者国家',
  `capture_day` DATEV2 NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `nf_behavior_num` BIGINT SUM COMMENT 'nf访问总数',
  `earliest_relation_time` BIGINT  MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT   MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`domain`, `net_action`, `data_type`, `visitor_archive_name`, `visitor_archive_type`,`visitor_country`, `capture_day`, `capture_month`)
COMMENT '网站档案访问明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`domain`, `visitor_archive_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "15G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive_website_statistics;  

CREATE TABLE IF NOT EXISTS ads_archive_website_statistics (
  `domain` VARCHAR(1024) NOT NULL COMMENT '域名',
  `behavior_num` BIGINT SUM COMMENT '行为总次数',
  `arc_visitor_area` BITMAP BITMAP_UNION COMMENT '访问档案区域个数',
  `arc_visitor_num` BITMAP BITMAP_UNION COMMENT '访问档案的档案个数',
  `block_num` BIGINT SUM COMMENT '阻断行为总次数',
  `important_target_num` BITMAP BITMAP_UNION COMMENT '重点访问目标个数',
  `file_num` BITMAP BITMAP_UNION COMMENT '文件总个数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `latest_relation_ip` VARCHAR(64) REPLACE COMMENT '最后关联ip',
  `latest_relation_country` VARCHAR(256) REPLACE COMMENT '最后关联国家',
  `latest_relation_city` VARCHAR(256) REPLACE COMMENT '最后关联城市'
)
AGGREGATE KEY (`domain`)
COMMENT '网站档案统计表'
DISTRIBUTED BY HASH(`domain`) BUCKETS 200
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);
