
DROP TABLE IF EXISTS dwd_nf_other_log_day;  

CREATE TABLE IF NOT EXISTS dwd_nf_other_log_day (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '动作类型',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT  COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE NOT NULL  COMMENT '捕获日期',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
  `mac` VARCHAR(64)  REPLACE COMMENT 'MAC',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
  `protocol` INT REPLACE  COMMENT '协议号',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`)
COMMENT 'other_log数据NF明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_nf_url_day;  

CREATE TABLE IF NOT EXISTS dwd_nf_url_day (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024) COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`)
COMMENT 'URL数据NF明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_nf_others;  

CREATE TABLE IF NOT EXISTS dwd_nf_others (
  `auth_account` VARCHAR(64) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(128) NOT NULL COMMENT '认证类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `mail_from` VARCHAR(1024) COMMENT '发件人',
  `mail_to` STRING COMMENT '收件人',
  `cc` STRING COMMENT '抄送',
  `bcc` STRING COMMENT '密送',
  `mail_title` STRING COMMENT '标题',
  `mail_body` STRING COMMENT '正文',
  `app_type` VARCHAR(128) COMMENT '应用类型',
  `app_name` VARCHAR(128) COMMENT '应用名称',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `user_name` STRING COMMENT '用户名',
  `user_group` STRING COMMENT '用户组',
  `imsi` VARCHAR(15) COMMENT 'IMSI',
  `imei` VARCHAR(15) COMMENT 'IMEI',
  `hardware_type` VARCHAR(64) COMMENT '终端类型',
  `protocol` INT COMMENT '协议号',
  `strsrc_ip` VARCHAR(64) NOT NULL COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `strdst_ip` VARCHAR(64) NOT NULL COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `domain` VARCHAR(1024) COMMENT '域名',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_time` BIGINT COMMENT '捕获时间',
  `chat_from` STRING COMMENT '发送人',
  `chat_to` STRING COMMENT '接收人',
  `msg` STRING COMMENT '聊天内容',
  `chat_dir` VARCHAR(4) COMMENT '消息方向',
  `mac` VARCHAR(64) COMMENT 'MAC',
  `url` VARCHAR(65533) COMMENT 'URL',
  `text` STRING COMMENT '内容',
  `chat_action` VARCHAR(64) COMMENT '动作类型',
  `terminal_type` VARCHAR(64) COMMENT '终端类型'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'NF其他协议汇总表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `data_type`, `strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_others;  

CREATE TABLE `dwd_lis_others` (
  `auth_account` VARCHAR(128) NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NULL COMMENT '认证类型',
  `data_type` CHAR(7) NULL COMMENT '协议类型',
  `child_type` CHAR(7) NULL COMMENT '协议类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_time` BIGINT NULL COMMENT '采集时间',
  `account` VARCHAR(64) NULL COMMENT '网购人',
  `action` VARCHAR(16) NULL COMMENT '动作类型',
  `ap_base_mac` VARCHAR(64) NULL COMMENT 'AP MAC',
  `attach_download_path` TEXT NULL COMMENT '附件下载地址',
  `attach_md5s` TEXT NULL COMMENT '附件列表中对应附件md5值',
  `attach_names` TEXT NULL COMMENT '附件列表名称',
  `attach_num` INT NULL COMMENT '附件个数',
  `attach_sizes` VARCHAR(256) NULL COMMENT '附件列表中对应附件大小',
  `attach_text` TEXT NULL COMMENT '附件列表中对应附件的内容信息',
  `author` TEXT NULL COMMENT '作者',
  `base_station_id` VARCHAR(64) NULL COMMENT '基站编号',
  `blue_mac` VARCHAR(64) NULL COMMENT '蓝牙MAC',
  `board` VARCHAR(128) NULL COMMENT '搜索版块',
  `board_name` VARCHAR(128) NULL COMMENT '版块名称',
  `browse_type` VARCHAR(32) NULL COMMENT '浏览器',
  `buy_count` BIGINT NULL COMMENT '物品数量',
  `buy_time` BIGINT NULL COMMENT '下单时间',
  `channel_url` VARCHAR(128) NULL COMMENT '频道',
  `comment_counter` VARCHAR(16) NULL COMMENT '评论数',
  `content` TEXT NULL COMMENT '内容',
  `coordinate_type` VARCHAR(16) NULL COMMENT '坐标系',
  `copy_from` TEXT NULL COMMENT '来源',
  `delivery_company` TEXT NULL COMMENT '快递公司',
  `delivery_num` VARCHAR(16) NULL COMMENT '快递单号',
  `depart` TEXT NULL COMMENT '出发地',
  `depart_address` TEXT NULL COMMENT '出发地详细地址',
  `destination` TEXT NULL COMMENT '目的地',
  `destination_address` TEXT NULL COMMENT '目的地详细地址',
  `dispatched_name` VARCHAR(64) NULL COMMENT '派件人',
  `dispatched_phone` VARCHAR(16) NULL COMMENT '派件人电话',
  `domain` VARCHAR(1024) NULL COMMENT '域名',
  `dst_ip_area` VARCHAR(128) NULL COMMENT '目的IP归属区域',
  `dst_latitude` DOUBLE NULL COMMENT '目的地纬度',
  `dst_longitude` DOUBLE NULL COMMENT '目的地经度',
  `dst_port` INT NULL COMMENT '目的端口',
  `favorite_tags` VARCHAR(64) NULL COMMENT '数据标签',
  `file_path` TEXT NULL COMMENT '报文下载',
  `file_size` INT NULL COMMENT '报文大小',
  `from_account` TEXT NULL COMMENT '发布人',
  `from_nickname` TEXT NULL COMMENT '发送方昵称',
  `from_role` TEXT NULL COMMENT '发送者',
  `goods_attr` TEXT NULL COMMENT '商品描述',
  `goods_name` TEXT NULL COMMENT '物品名称',
  `goods_pic` TEXT NULL COMMENT '物品图片',
  `goods_score` VARCHAR(16) NULL COMMENT '商品评分',
  `group_name` TEXT NULL COMMENT '聊天室',
  `hardware_sign` VARCHAR(64) NULL COMMENT '特征值',
  `hardware_type` VARCHAR(64) NULL COMMENT '特征类型',
  `head_url` TEXT NULL COMMENT '用户头像',
  `host` VARCHAR(64) NULL COMMENT '主机名',
  `icp_provider` VARCHAR(64) NULL COMMENT 'ICP',
  `imei` VARCHAR(15) NULL COMMENT 'IMEI',
  `imsi` TEXT NULL,
  `internet_land` TEXT NULL COMMENT '认证账号上网地',
  `in_time` BIGINT NULL COMMENT '进入时间',
  `isp_id` VARCHAR(64) NULL COMMENT 'ISP',
  `issue_status` VARCHAR(16) NULL COMMENT '状态',
  `lang_type` VARCHAR(32) NULL COMMENT '语种',
  `latitude` DOUBLE NULL COMMENT '纬度',
  `leave_word` TEXT NULL COMMENT '买家留言',
  `login_email` TEXT NULL COMMENT '用户账号',
  `login_time` BIGINT NULL COMMENT '动作时间',
  `longitude` DOUBLE NULL COMMENT '经度',
  `mac` VARCHAR(64) NULL COMMENT 'MAC',
  `message` TEXT NULL COMMENT '聊天内容',
  `mime_desc` TEXT NULL COMMENT '描述',
  `mime_name` TEXT NULL COMMENT '标题',
  `money` VARCHAR(15) NULL COMMENT '交易金额',
  `news_content` TEXT NULL COMMENT '新闻',
  `oper_time` BIGINT NULL COMMENT '操作时间',
  `order_status` VARCHAR(16) NULL COMMENT '订单状态',
  `order_text` TEXT NULL COMMENT '订单内容',
  `org_owner_nick` TEXT NULL COMMENT '创建者',
  `os_name` VARCHAR(32) NULL COMMENT '操作系统',
  `other_imei` VARCHAR(15) NULL COMMENT '其他IMEI',
  `out_time` BIGINT NULL COMMENT '离开时间',
  `ownership_land` VARCHAR(128) NULL COMMENT '认证账号归属地',
  `password` VARCHAR(16) NULL COMMENT '登录密码',
  `pay_account` TEXT NULL COMMENT '支付账号',
  `pay_name` TEXT NULL COMMENT '对方名称',
  `position` TEXT NULL COMMENT '位置信息',
  `post_time` BIGINT NULL COMMENT '发布时间',
  `proxy_account` TEXT NULL COMMENT '代理账号',
  `proxy_address` TEXT NULL COMMENT '代理地址',
  `proxy_provider` TEXT NULL COMMENT '代理提供商',
  `proxy_type` VARCHAR(64) NULL COMMENT '代理类型',
  `publish_time` BIGINT NULL COMMENT '发表时间',
  `rat_type` VARCHAR(16) NULL COMMENT '网络类型',
  `receiver` TEXT NULL COMMENT '收件人',
  `receiver_account` TEXT NULL COMMENT '评论人',
  `receiver_address` TEXT NULL COMMENT '收件人地址',
  `receiver_phone` VARCHAR(16) NULL COMMENT '收件人手机号',
  `regis_time` BIGINT NULL COMMENT '注册时间',
  `reg_time` BIGINT NULL COMMENT '注册时间',
  `reply_content` TEXT NULL COMMENT '评论内容',
  `sender` TEXT NULL COMMENT '发件人',
  `sender_phone` VARCHAR(16) NULL COMMENT '发件人手机号',
  `send_address` TEXT NULL COMMENT '发件人地址',
  `sign_name` TEXT NULL COMMENT '个性签名',
  `source_site` TEXT NULL COMMENT '来源',
  `src_ip_area` VARCHAR(128) NULL COMMENT '源IP归属区域',
  `src_port` INT NULL COMMENT '源端口',
  `start_latitude` DOUBLE NULL COMMENT '出发地纬度',
  `start_longitude` DOUBLE NULL COMMENT '出发点经度',
  `strdst_ip` VARCHAR(64) NULL COMMENT '目的IP',
  `strsrc_ip` VARCHAR(64) NULL COMMENT '源IP',
  `subject` TEXT NULL COMMENT '主题',
  `sub_domain` VARCHAR(1024) NULL COMMENT '子域名',
  `terminal_imei` VARCHAR(15) NULL COMMENT '内容提取的IMEI',
  `terminal_imsi` VARCHAR(15) NULL COMMENT '内容提取的IMSI',
  `terminal_mac` VARCHAR(64) NULL COMMENT '内容提取的MAC',
  `text` TEXT NULL COMMENT '报文正文',
  `tool_name` VARCHAR(128) NULL COMMENT '工具名称',
  `tool_type` VARCHAR(64) NULL COMMENT '工具类型',
  `video_author` VARCHAR(64) NULL COMMENT '视频作者',
  `total_fee` DOUBLE NULL COMMENT '支付金额',
  `to_account` TEXT NULL COMMENT '收款账号',
  `to_nickname` TEXT NULL COMMENT '接收方昵称',
  `to_role` TEXT NULL COMMENT '接收者',
  `trace_id` VARCHAR(64) NULL COMMENT '还原设备ID',
  `trading_account` VARCHAR(32) NULL COMMENT '支付卡号',
  `uparea_id` INT NULL COMMENT '采集地点',
  `url` TEXT NULL COMMENT 'URL',
  `username` TEXT NULL COMMENT '登录账号',
  `user_nick` TEXT NULL COMMENT '用户昵称',
  `video_category` VARCHAR(64) NULL COMMENT '分类',
  `video_count` BIGINT NULL COMMENT '点击次数',
  `video_pic` VARCHAR(128) NULL COMMENT '图片',
  `video_topic` VARCHAR(64) NULL COMMENT '专题',
  `video_desc` TEXT NULL COMMENT '描述',
  `wallet_id` TEXT NULL COMMENT '钱包地址',
  `wallet_pwd` VARCHAR(16) NULL COMMENT '钱包密码',
  `website` TEXT NULL COMMENT 'URL',
  `web_subject` TEXT NULL COMMENT '标题',
  `transaction_amount` VARCHAR(15) NULL COMMENT '交易金额'
) ENGINE=OLAP
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'LIS其他协议汇总表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `data_type`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_call;  

CREATE TABLE IF NOT EXISTS dwd_call (
  `calling_number` VARCHAR(20) COMMENT '主叫号码',
  `called_number` VARCHAR(20) COMMENT '被叫号码',
  `call_type` CHAR(2) COMMENT '通话状态',
  `call_id` BIGINT COMMENT 'call id',
  `calling_atrribution` VARCHAR(128) COMMENT '主叫归属地',
  `called_atrribution` VARCHAR(128) COMMENT '被叫归属地',
  `start_time` BIGINT COMMENT '通话起始时间',
  `end_time` BIGINT COMMENT '通话结束时间',
  `duration` INT COMMENT '通话持续时间',
  `file_path` STRING COMMENT '通话语音文件路径',
  `bs_attribution` VARCHAR(128) COMMENT '主叫位置',
  `called_bs_attribution` VARCHAR(128) COMMENT '被叫位置',
  `calling_imsi` VARCHAR(15) COMMENT '主叫IMSI',
  `called_imsi` VARCHAR(15) COMMENT '被叫IMSI',
  `data_type` INT COMMENT '通信协议',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目标IP',
  `dst_ip_area` VARCHAR(128) COMMENT '目标IP归属区域',
  `action` VARCHAR(16) COMMENT '动作编码 20 主叫，21 被叫',
  `capture_time` BIGINT NOT NULL COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
   spam_flag TINYINT COMMENT '骚扰号码标志位',
  `capture_day` DATE NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`calling_number`, `called_number`, `call_type`)
COMMENT '通话数据明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`calling_number`, `called_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_sms;  

CREATE TABLE IF NOT EXISTS dwd_sms (
  `calling_number` VARCHAR(20) COMMENT '主叫号码',
  `called_number` VARCHAR(20) COMMENT '被叫号码',
  `call_type` CHAR(2) COMMENT '发送状态',
  `call_id` BIGINT COMMENT 'call id',
  `calling_atrribution` VARCHAR(128) COMMENT '主叫归属地',
  `called_atrribution` VARCHAR(128) COMMENT '被叫归属地',
  `start_time` BIGINT COMMENT '发送时间',
  `sms_text` STRING COMMENT '短信内容',
  `sms_lang` VARCHAR(16) COMMENT '短信语言',
  `bs_attribution` VARCHAR(128) COMMENT '主叫位置',
  `called_bs_attribution` VARCHAR(128) COMMENT '被叫位置',
  `calling_imsi` VARCHAR(15) COMMENT '主叫IMSI',
  `called_imsi` VARCHAR(15) COMMENT '被叫IMSI',
  `data_type` INT COMMENT '通信协议',
  `action` VARCHAR(16) COMMENT '动作编码 20 主叫，21 被叫',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  spam_flag TINYINT COMMENT '骚扰号码标志位',
  `capture_day` DATE NOT NULL COMMENT '捕获日期'  
)
DUPLICATE KEY (`calling_number`, `called_number`, `call_type`)
COMMENT '短信数据明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`calling_number`, `called_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_fax;  

CREATE TABLE IF NOT EXISTS dwd_fax (
  `calling_number` VARCHAR(20) COMMENT '发送人',
  `called_number` VARCHAR(20) COMMENT '接收人',
  `call_id` BIGINT COMMENT 'call id',
  `calling_atrribution` VARCHAR(128) COMMENT '主叫归属地',
  `called_atrribution` VARCHAR(128) COMMENT '被叫归属地',
  `start_time` BIGINT COMMENT '开始时间',
  `end_time` BIGINT COMMENT '结束时间',
  `duration` INT COMMENT '持续时长',
  `file_path` STRING COMMENT '路径',
  `media_type` VARCHAR(32) COMMENT '类型',
  `media_protocol` VARCHAR(32) COMMENT '协议',
  `media_format` VARCHAR(32) COMMENT '格式',
  `sender_nickname` STRING COMMENT '发送人昵称',
  `receiver_nickname` STRING COMMENT '接收人昵称',
  `data_type` INT COMMENT '通信协议',
  `action` VARCHAR(16) COMMENT '动作编码 20 主叫，21 被叫',
  `strsrc_ip` VARCHAR(64) COMMENT '发送人IP',
  `src_ip_area` VARCHAR(128) COMMENT '发送人IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '接收人IP',
  `dst_ip_area` VARCHAR(128) COMMENT '接收人IP归属区域',
  `sender_from_url` STRING COMMENT '发送人网络地址',
  `receiver_to_url` STRING COMMENT '接收人网络地址',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  spam_flag TINYINT COMMENT '骚扰号码标志位',
  `capture_day` DATE NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`calling_number`, `called_number`)
COMMENT '传真数据明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`calling_number`, `called_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_voip;  

CREATE TABLE IF NOT EXISTS dwd_lis_voip (
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `from_id` VARCHAR(64) COMMENT '主叫账号',
  `to_id` VARCHAR(64) COMMENT '被叫账号',
  `action` VARCHAR(4) COMMENT '动作类型',
  `file_size` INT COMMENT '通话时长',
  `file_path` STRING COMMENT '通话语音附件地址',
  `src_ip_area` VARCHAR(128) COMMENT '主叫通话地',
  `dst_ip_area` VARCHAR(128) COMMENT '被叫通话地',
  `username` STRING COMMENT '登录账号',
  `password` STRING COMMENT '登录密码',
  `user_nick` STRING COMMENT '主叫昵称',
  `calling_imsi` VARCHAR(15) COMMENT '主叫IMSI',
  `called_imsi` VARCHAR(15) COMMENT '被叫IMSI',
  `child_type` VARCHAR(16) COMMENT '应用名称',
  `calling_imei` VARCHAR(15) COMMENT '主叫IMEI',
  `called_imei` VARCHAR(15) COMMENT '被叫IMEI',
  `mac` VARCHAR(64) COMMENT 'MAC',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `src_attribution` VARCHAR(128) COMMENT '源号码归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `dst_attribution` VARCHAR(128) COMMENT '目的号码归属区域',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `isp_id` VARCHAR(64) COMMENT 'ISP',
  `icp_provider` VARCHAR(64) COMMENT 'ICP',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(64) COMMENT '代理地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理服务商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期',
  `spam_flag` TINYINT COMMENT '垃圾邮件，骚扰短信标识 1 垃圾邮件 0 不是'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT '网络电话LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`,`strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_email;  




CREATE TABLE IF NOT EXISTS dwd_lis_email(
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证类型',
  `important_target_flag` TINYINT  COMMENT '重要目标标志位 ',
  `file_id` VARCHAR(32) COMMENT '文件id信息',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期',
  `mail_from` VARCHAR(1024) COMMENT '发件人',
  `mail_to` STRING COMMENT '收件人',
  `mail_cc` STRING COMMENT '抄送',
  `mail_bcc` STRING COMMENT '密抄',
  `mail_date` BIGINT COMMENT '发送时间',
  `subject` STRING COMMENT '主题',
  `text` STRING COMMENT '正文',
  `attach_num` INT COMMENT '附件个数',
  `attach_download_path` STRING COMMENT '附件下载地址',
  `attach_names` STRING COMMENT '附件名称',
  `attach_text` STRING COMMENT '附件列表中对应附件的内容信息',
  `attach_sizes` VARCHAR(256) COMMENT '附件列表中对应附件大小',
   `attach_md5s` STRING COMMENT '附件列表中对应附件md5值',
  `action` VARCHAR(4) COMMENT '动作类型',
  `spam_flag` TINYINT COMMENT '垃圾邮件',
  `forgery_flag` TINYINT COMMENT '伪造邮件',
  `username` STRING COMMENT '用户账号',
  `password` STRING COMMENT '登录密码',
  `from_nickname` STRING COMMENT '发送方昵称',
  `to_nickname` STRING COMMENT '接收方昵称',
  `imsi` VARCHAR(15) COMMENT 'IMSI',
  `child_type` CHAR(16) COMMENT '协议类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `imei` VARCHAR(15) COMMENT 'IMEI',
  `mac` VARCHAR(64) COMMENT 'MAC',
  `os_name` VARCHAR(16) COMMENT '操作系统',
  `browse_type` VARCHAR(32) COMMENT '浏览器',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP归属区域',
  `isp_id` VARCHAR(64) COMMENT 'ISP',
  `icp_provider` VARCHAR(64) COMMENT 'ICP',
  `base_station_id` VARCHAR(64) COMMENT '基站编号',
  `host` VARCHAR(64) COMMENT '主机名',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理地址',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `file_path` STRING COMMENT '报文下载',
  `file_size` INT COMMENT '报文大小',
  `charset` VARCHAR(16) COMMENT '字符集',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'EMAIL数据LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `file_id`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);





DROP TABLE IF EXISTS dwd_lis_vpn;  

CREATE TABLE IF NOT EXISTS dwd_lis_vpn (
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `child_type` CHAR(16) COMMENT '名称',
  `username` STRING COMMENT 'VPN账号',
  `password` STRING COMMENT '登录密码',
  `action` VARCHAR(16) COMMENT '动作类型',
  `attach_num` INT COMMENT '附件个数',
  `attach_download_path` STRING COMMENT '附件下载地址',
  `attach_text` STRING COMMENT '附件列表中对应附件的内容信息',
  `attach_names` STRING COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256) COMMENT '附件列表中对应附件大小',
  `attach_md5s` STRING COMMENT '附件列表中对应附件md5值',
  `imsi` VARCHAR(15) COMMENT 'IMSI',
  `imei` VARCHAR(15) COMMENT 'IMEI',
  `mac` VARCHAR(64) COMMENT 'MAC',
  `os_name` VARCHAR(32) COMMENT '操作系统',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP归属区域',
  `base_station_id` VARCHAR(64) COMMENT '基站编号',
  `isp_id` VARCHAR(64) COMMENT 'ISP',
  `icp_provider` VARCHAR(64) COMMENT 'ICP',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `vpn_src_ip` VARCHAR(64) COMMENT 'VPN客户端IP',
  `vpn_src_port` INT COMMENT 'VPN客户端端口',
  `vpn_dst_ip` VARCHAR(64) COMMENT 'VPN服务器端IP',
  `vpn_dst_port` INT COMMENT 'VPN服务器端端口',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `text` STRING COMMENT '报文正文',
  `file_path` STRING COMMENT '报文下载',
  `file_size` INT COMMENT '报文大小',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'VPN数据LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`,`strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_im;  

CREATE TABLE IF NOT EXISTS dwd_lis_im (
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证类型',
  `important_target_flag` TINYINT  COMMENT '重要目标标志位 ',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `from_id` VARCHAR(1024) COMMENT 'from_id',
  `to_id` VARCHAR(1024) COMMENT 'to_id',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `child_type` CHAR(16) COMMENT '协议代码',
  `from_nickname` STRING COMMENT '发送人昵称',
  `to_nickname` STRING COMMENT '接收人昵称',
  `action` VARCHAR(4) COMMENT '动作类型',
  `content` STRING COMMENT '消息内容',
  `money` VARCHAR(13) COMMENT '转账金额',
  `postal_time` BIGINT COMMENT '发送时间',
  `username` STRING COMMENT '登录账号',
  `password` STRING  COMMENT '登录密码',
  `head_url` STRING COMMENT '用户头像',
  `sign_name` STRING COMMENT '个性签名',
  `friend_remark` STRING COMMENT '好友备注',
  `friend_head_url` STRING COMMENT '好友头像',
  `group_num` VARCHAR(20) COMMENT '群号',
  `group_name` STRING COMMENT '群名称',
  `group_introduce` STRING COMMENT '群简介',
  `group_member_count` VARCHAR(8) COMMENT '群人数',
  `org_creater` VARCHAR(64) COMMENT '群主',
  `group_create_time` BIGINT COMMENT '群创建时间',
  `attach_num` INT COMMENT '附件个数',
  `attach_download_path` STRING COMMENT '附件列表下载地址',
  `attach_names` STRING COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256) COMMENT '附件列表中对应附件大小',
  `attach_md5s` STRING COMMENT '附件列表中对应附件md5值',
  `attach_text` STRING COMMENT '附件列表中对应附件的内容信息',
  `imsi` VARCHAR(15) COMMENT 'IMSI',
  `terminal_imei` VARCHAR(15) COMMENT 'IMEI',
  `terminal_mac` VARCHAR(64) COMMENT 'MAC',
  `os_name` VARCHAR(16) COMMENT '操作系统',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP归属区域',
  `base_station_id` VARCHAR(64) COMMENT '基站编号',
  `isp_id` VARCHAR(64) COMMENT 'ISP',
  `icp_provider` VARCHAR(64) COMMENT 'ICP',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `url` STRING COMMENT 'URL',
  `host` VARCHAR(64) COMMENT '主机名',
  `text` STRING COMMENT '报文正文',
  `file_path` STRING COMMENT '报文下载',
  `file_size` INT COMMENT '报文大小',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'IM数据LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_tool;  

CREATE TABLE IF NOT EXISTS dwd_lis_tool (
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `action` VARCHAR(16) COMMENT '动作类型',
  `file_name` STRING COMMENT '文件名称',
  `username` STRING COMMENT '用户账号',
  `password` STRING COMMENT '登录密码',
  `peer_user` STRING COMMENT '设备账号',
  `peer_pwd` VARCHAR(16) COMMENT '设备密码',
  `dev_name` STRING COMMENT '设备名称',
  `wlw_get_flag` VARCHAR(16) COMMENT '数据获取方式',
  `app_list` STRING COMMENT '软件安装列表',
  `imsi` VARCHAR(15) COMMENT 'IMSI',
  `imei` VARCHAR(15) COMMENT 'IMEI',
  `mac` VARCHAR(64) COMMENT 'MAC',
  `os_name` VARCHAR(32) COMMENT '操作系统',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP归属区域',
  `base_station_id` VARCHAR(64) COMMENT '基站编号',
  `isp_id` VARCHAR(64) COMMENT 'ISP',
  `icp_provider` VARCHAR(64) COMMENT 'ICP',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `text` STRING COMMENT '报文正文',
  `file_path` STRING COMMENT '报文下载',
  `file_size` INT COMMENT '报文大小',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT '工具数据LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_remote;  

CREATE TABLE IF NOT EXISTS dwd_lis_remote (
  `auth_account` VARCHAR(64) COMMENT '认证账号',
  `auth_type` VARCHAR(128) COMMENT '认证类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `child_type` CHAR(16) COMMENT '协议代码',
  `login_email` VARCHAR(1024) COMMENT '邮箱',
  `to_ip` VARCHAR(64) COMMENT '被控制ip',
  `to_port` VARCHAR(5) COMMENT '控制通信端口',
  `password` STRING COMMENT '远程控制密码',
  `action` VARCHAR(4) COMMENT '操作类型',
  `op_desc` STRING COMMENT '操作描述',
  `duration` BIGINT COMMENT '记录持续时间（单位 ： s秒）',
  `attach_num` INT COMMENT '附件个数',
  `attach_download_path` STRING COMMENT '附件下载地址',
  `attach_names` STRING COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256) COMMENT '附件列表中对应附件大小',
  `attach_md5s` STRING COMMENT '附件列表中对应附件md5值',
  `username` STRING COMMENT '账号',
  `password_hash_string` STRING COMMENT '密码串',
  `user_nick` STRING COMMENT '昵称',
  `imsi` VARCHAR(15) COMMENT '国际移动用户标识号',
  `imei` VARCHAR(15) COMMENT '设备号',
  `mac` VARCHAR(64) COMMENT '客户端MAC地址',
  `os_name` VARCHAR(16) COMMENT '操作系统名称',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP地址（ipv4或者ipv6）',
  `src_port` INT COMMENT '源端口（ipv4或者ipv6）',
  `src_ip_area` VARCHAR(128) COMMENT '源IP区域标识',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP地址（ipv4或者ipv6）',
  `dst_port` INT COMMENT '目的端口（ipv4或者ipv6）',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP区域标识',
  `base_station_id` VARCHAR(64) COMMENT '基站号_AP编号',
  `isp_id` VARCHAR(64) COMMENT '运营商和基础网络',
  `icp_provider` VARCHAR(64) COMMENT 'ICP服务提供商',
  `hostname` VARCHAR(64) COMMENT '主机名',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理工具地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `text` STRING COMMENT '报文正文',
  `file_path` STRING COMMENT '原始报文路径',
  `file_size` INT COMMENT '原始报文大小',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'RemoteCTRL数据LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_lis_http;  

CREATE TABLE IF NOT EXISTS dwd_lis_http (
 `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `dst_port` INT  COMMENT '目的端口（ipv4或者ipv6）',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE NOT NULL  COMMENT '捕获日期',
  `attach_names` VARCHAR(1024)  COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256)  COMMENT '附件列表中对应附件大小',
  `attach_text` VARCHAR(65533)  COMMENT '附件列表中对应附件的内容信息',
  `attach_num` INT  COMMENT '附件个数',
  `text` VARCHAR(65533)  COMMENT '报文正文',
  `keywords` VARCHAR(256)  COMMENT '关键词',
  `subject` VARCHAR(1024)  COMMENT '标题',
  `abstract` VARCHAR(1024)  COMMENT '摘要',
  `lang_type` VARCHAR(32)  COMMENT '语种',
  `username` VARCHAR(512)  COMMENT '用户名',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `capture_hour` TINYINT  COMMENT '捕获时段',
  `ownership_land` VARCHAR(128) REPLACE  COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) REPLACE COMMENT '认证账号上网地',
  `url` STRING REPLACE COMMENT  'URL',
  `child_type` CHAR(16) REPLACE COMMENT  '协议代码',
  `attach_download_path` VARCHAR(1024)  REPLACE COMMENT '附件下载地址',
  `attach_md5s` STRING REPLACE COMMENT '附件列表中对应附件md5值',
  `password` VARCHAR(512) REPLACE COMMENT '密码',
  `imsi` VARCHAR(15) REPLACE COMMENT '国际移动用户标识号',
  `imei` VARCHAR(15) REPLACE COMMENT '设备号',
  `mac` VARCHAR(64) REPLACE COMMENT '客户端MAC地址',
  `os_name` VARCHAR(16) REPLACE COMMENT '操作系统名称',
  `browse_type` VARCHAR(64) REPLACE COMMENT '浏览器名称',
  `cookie` VARCHAR(4096) REPLACE COMMENT 'Cookie',
  `answer_code` VARCHAR(512) REPLACE COMMENT '操作返回结果',
  `strsrc_ip` VARCHAR(64) REPLACE COMMENT '源IP地址（ipv4或者ipv6）',
  `src_port` INT REPLACE COMMENT '源端口（ipv4或者ipv6）',
  `src_ip_area` VARCHAR(128) REPLACE COMMENT '源IP区域标识',
  `strdst_ip` VARCHAR(64) REPLACE COMMENT '目的IP地址（ipv4或者ipv6）',
  `dst_ip_area` VARCHAR(128) REPLACE COMMENT '目的IP区域标识',
  `base_station_id` VARCHAR(64) REPLACE COMMENT '基站号_AP编号',
  `isp_id` VARCHAR(64) REPLACE COMMENT '运营商和基础网络',
  `host` VARCHAR(64) REPLACE COMMENT '主机名',
  `tool_type` VARCHAR(64) REPLACE COMMENT '工具类型',
  `tool_name` VARCHAR(128) REPLACE COMMENT '工具名称',
  `proxy_type` VARCHAR(64) REPLACE COMMENT '代理类型',
  `proxy_address` STRING REPLACE COMMENT '代理工具地址',
  `proxy_provider` VARCHAR(512) REPLACE COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) REPLACE COMMENT '代理账号',
  `file_path` STRING REPLACE COMMENT '原始报文路径',
  `file_size` INT REPLACE COMMENT '原始报文大小',
  `earliest_relation_time` BIGINT REPLACE COMMENT '最早关联时间',
  `latest_relation_time` BIGINT REPLACE COMMENT '最后关联时间',
  `favorite_tags` VARCHAR(64) REPLACE COMMENT '数据标签',
  `behavior_num` BIGINT sum  COMMENT '行为总数'
)
AGGREGATE KEY (auth_account, auth_type, dst_port, uparea_id, capture_day, attach_names, attach_sizes, attach_text, attach_num, text, keywords, subject, abstract, lang_type, username, domain,capture_hour)
COMMENT 'HTTP 数据LIS 明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `dst_port`, `uparea_id`, capture_day, attach_names) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "30G",
    "replication_allocation" = "tag.location.default: 3"
);	

DROP TABLE IF EXISTS dwd_lis_ftp;  

CREATE TABLE IF NOT EXISTS dwd_lis_ftp (
  `auth_account` VARCHAR(64) COMMENT '认证账号',
  `auth_type` VARCHAR(128) COMMENT '认证类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `child_type` CHAR(16) COMMENT '协议类型',
  `url` STRING COMMENT 'URL',
  `username` STRING COMMENT '登录账号',
  `password` STRING  COMMENT '登录密码',
  `action` VARCHAR(16) COMMENT '动作类型',
  `content` STRING COMMENT '内容',
  `command` STRING COMMENT '执行命令',
  `attach_num` INT COMMENT '附件个数',
  `attach_download_path` STRING COMMENT '附件下载地址',
  `attach_names` STRING COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256) COMMENT '附件列表中对应附件大小',
  `attach_md5s` STRING COMMENT '附件列表中对应附件md5值',
  `attach_text` STRING COMMENT '附件列表中对应附件的内容信息',
  `imsi` VARCHAR(15) COMMENT 'IMSI',
  `mac` VARCHAR(64) COMMENT 'MAC',
  `imei` VARCHAR(15) COMMENT 'IMEI',
  `os_name` VARCHAR(16) COMMENT '操作系统',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP',
  `dst_port` INT COMMENT '目的端口',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP归属区域',
  `base_station_id` VARCHAR(64) COMMENT '基站编号',
  `isp_id` VARCHAR(64) COMMENT 'ISP',
  `icp_provider` VARCHAR(64) COMMENT 'ICP',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `text` STRING COMMENT '报文正文',
  `file_path` STRING COMMENT '报文下载',
  `file_size` INT COMMENT '报文大小',
  `capture_time` BIGINT COMMENT '捕获时间',
  `uparea_id` INT COMMENT '上报地',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'FTP数据LIS明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_nf_url;  

CREATE TABLE IF NOT EXISTS dwd_nf_url (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024) COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, capture_hour)
COMMENT 'URL数据NF明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "100G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_vpn_url;  

CREATE TABLE IF NOT EXISTS dwd_vpn_url (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024) COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_hour` TINYINT COMMENT '捕获小时',
  `capture_minute` TINYINT COMMENT '捕获分钟',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, `capture_hour`, `capture_minute`)
COMMENT 'URL数据NF明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_nf_other_log;  

CREATE TABLE IF NOT EXISTS dwd_nf_other_log (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '动作类型',
  `url` VARCHAR(512)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT  COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
  `mac` VARCHAR(64)  REPLACE COMMENT 'MAC',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
  `protocol` INT REPLACE  COMMENT '协议号',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, capture_hour)
COMMENT 'other_log数据NF明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dwd_vpn_other_log;  

CREATE TABLE IF NOT EXISTS dwd_vpn_other_log (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '动作类型',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT  COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE  NOT NULL COMMENT '捕获日期',
  `capture_hour` TINYINT COMMENT '捕获小时',
  `capture_minute` TINYINT COMMENT '捕获分钟',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `mac` VARCHAR(64)  REPLACE COMMENT 'MAC',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
  `protocol` INT REPLACE  COMMENT '协议号',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, `capture_hour`, `capture_minute`)
COMMENT 'other_log数据NF明细表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);
