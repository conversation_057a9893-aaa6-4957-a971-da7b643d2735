
DROP TABLE IF EXISTS dws_element_relation_virtual_account_target_detail;  

CREATE TABLE `dws_element_relation_virtual_account_target_detail` (
  `virtual_account` varchar(512) NOT NULL COMMENT '虚拟账号',
  `virtual_app_type` varchar(128) COMMENT '虚拟账号应用类型',
 `data_type` INT NOT NULL COMMENT '协议类型',
  `important_target` varchar(64) NOT NULL COMMENT '重点目标',
  `important_target_type` varchar(64) NOT NULL COMMENT '重点目标类型',
  `capture_hour` tinyint NOT NULL COMMENT '捕获时段',
  `capture_day` date NOT NULL COMMENT '捕获日期',
  `capture_month` int NOT NULL COMMENT '捕获月份'
)
AGGREGATE KEY (`virtual_account`,virtual_app_type,data_type,`important_target`,`important_target_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '虚拟账号重要目标关联表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`,virtual_app_type,data_type, important_target) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);


DROP TABLE IF EXISTS dws_element_behavior_im_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_im_detail (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `virtual_app_type` VARCHAR(128) NOT NULL COMMENT '虚拟账号应用类型',
  `active_country` VARCHAR(128) COMMENT '活跃国家',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为总数'
)
AGGREGATE KEY (`virtual_account`,virtual_app_type,`active_country`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT 'Email档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`,virtual_app_type) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_im_entity;  

CREATE TABLE IF NOT EXISTS dws_element_im_entity (
  `virtual_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `virtual_app_type` VARCHAR(128) NOT NULL COMMENT '虚拟账号应用类型',
  `insert_time` BIGINT min COMMENT '创建时间',
  `latest_relation_time` BIGINT max NOT NULL COMMENT '最后关联时间'
)
AGGREGATE KEY (`virtual_account`, virtual_app_type)
COMMENT 'IM档案建档案实体表'
DISTRIBUTED BY HASH(`virtual_account`, virtual_app_type) BUCKETS 2
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_email_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_email_detail (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `active_country` VARCHAR(128) NOT NULL COMMENT '活跃国家',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为总数'
)
AGGREGATE KEY (`virtual_account`,`active_country`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT 'Email档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_email_entity;  

CREATE TABLE IF NOT EXISTS dws_element_email_entity (
  `virtual_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `insert_time` BIGINT min COMMENT '创建时间',
  `latest_relation_time` BIGINT max NOT NULL COMMENT '最后关联时间'
)
AGGREGATE KEY (`virtual_account`)
COMMENT 'Email档案建档实体表'
DISTRIBUTED BY HASH(`virtual_account`) BUCKETS 100
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_nickname_detail;  

CREATE TABLE IF NOT EXISTS dws_element_nickname_detail (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `alias` VARCHAR(1024) COMMENT '别名',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT min NOT NULL COMMENT '最早关联时间',
  `latest_relation_time` BIGINT max NOT NULL COMMENT '最后关联时间',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为次数'
)
AGGREGATE KEY (`virtual_account`,`alias`, `capture_day`, `capture_month`)
COMMENT 'Email档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`,`alias`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_password_detail;  

CREATE TABLE IF NOT EXISTS dws_element_password_detail (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `password` VARCHAR(512) COMMENT '密码',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT min NOT NULL COMMENT '最早关联时间',
  `latest_relation_time` BIGINT max NOT NULL COMMENT '最后关联时间',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为次数'
)
AGGREGATE KEY (`virtual_account`,`password`, `capture_day`, `capture_month`)
COMMENT '密码记录表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`,`password`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_email_phone_extract_detail;  

CREATE TABLE IF NOT EXISTS dws_element_email_phone_extract_detail (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `auth_account` VARCHAR(128)  COMMENT '认证账号',
  `auth_type` VARCHAR(64)  COMMENT '认证账号类型',
  `phone_number` BIGINT NOT NULL COMMENT '电话号码',
  `file_path` VARCHAR(256) COMMENT '报文路径',
  `attach_path` VARCHAR(256) COMMENT '附件文件路径',
  `attach_name` VARCHAR(256) COMMENT '附件名称',
  `attach_md5` VARCHAR(64) COMMENT '附件md5值',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `capture_time` BIGINT NOT NULL COMMENT '数据捕获时间'
)
Duplicate KEY (`virtual_account`)
COMMENT '号码提取表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`,auth_account,auth_type, `phone_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);


DROP TABLE IF EXISTS dws_element_radius_auth_record;  

CREATE TABLE IF NOT EXISTS dws_element_radius_auth_record (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `ip` VARCHAR(64) NOT NULL COMMENT '认证ip',
  `action` VARCHAR(16) NOT NULL COMMENT '动作',
  `mac` VARCHAR(64) NOT NULL COMMENT 'MAC',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `duration` BIGINT COMMENT '时长（秒）',
  `capture_time` BIGINT NOT NULL COMMENT '最后关联时间'
)
Duplicate KEY (`auth_account`, auth_type)
COMMENT 'RADIUS认证记录表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_call_day;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_call_day (
  `src_number` VARCHAR(20) NOT NULL COMMENT '主叫号码',
  `dst_number` VARCHAR(20) NOT NULL COMMENT '被叫号码',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `call_tag` VARCHAR(16) COMMENT '通话标签',
  `calling_attribution` VARCHAR(64) COMMENT '主叫归属地',
  `called_attribution` VARCHAR(64) COMMENT '被叫归属地',
  `action` VARCHAR(4) COMMENT '动作编码  20 主叫，21 被叫',
  `spam_flag` TINYINT COMMENT '垃圾邮件，骚扰短信标识 1 垃圾邮件 0 不是',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`src_number`, `dst_number`, `data_type`, `call_tag`, `calling_attribution`, `called_attribution`,action, `spam_flag`, `capture_day`, `capture_month`)
COMMENT '通话明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`src_number`, `dst_number`, `data_type`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_nf_protocol_info;  

CREATE TABLE IF NOT EXISTS dws_element_nf_protocol_info (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `net_action` INT NOT NULL COMMENT '上网动作类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `net_action`, `data_type`, `capture_day`, `capture_month`)
COMMENT 'NF协议数据信息表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_lis_protocol_info;  

CREATE TABLE IF NOT EXISTS dws_element_lis_protocol_info (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `data_type`, `capture_day`, `capture_month`)
COMMENT 'LIS协议数据信息表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_vpn_info_hour;  

CREATE TABLE IF NOT EXISTS dws_element_vpn_info_hour (
  `auth_account` VARCHAR(64) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(16) NOT NULL COMMENT '认证账号类型',
  `capture_time` BIGINT COMMENT '捕获时间',
  `capture_minute` TINYINT COMMENT '捕获分钟',
  `block_flag` INT NOT NULL COMMENT '阻断标志位 1 阻断流量 0 非阻断流量',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT COMMENT '行为总数'
)
Duplicate KEY (`auth_account`, `auth_type`,  `capture_time`, `capture_minute`)
COMMENT 'VPN信息小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `app_name`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_flight_info;  

CREATE TABLE IF NOT EXISTS dws_element_flight_info (
  `phone_number` BIGINT NOT NULL COMMENT '电话号码',
  `src_airport` VARCHAR(128) COMMENT '出发机场',
  `dst_airport` VARCHAR(128) COMMENT '到达机场',
  `src_country` VARCHAR(128) COMMENT '出发国家',
  `dst_country` VARCHAR(128) COMMENT '到达国家',
  `flight_info` VARCHAR(128) COMMENT '航班信息',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `capture_time` BIGINT NOT NULL COMMENT '数据捕获时间'
)
Duplicate KEY (`phone_number`)
COMMENT '航班信息表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_bank_card_detail;  

CREATE TABLE IF NOT EXISTS dws_element_bank_card_detail (
  `phone_number` BIGINT NOT NULL COMMENT '电话号码',
  `bank_account` VARCHAR(128) NOT NULL COMMENT '银行卡账户',
  `text` VARCHAR(1024) NOT NULL COMMENT '短信内容',
  `transaction_type` TINYINT NOT NULL COMMENT '1-转入 2 转出',
  `transaction_amount` VARCHAR(128) NOT NULL COMMENT '交易金额',
  `transaction_time` VARCHAR(32) NOT NULL COMMENT '交易时间',
  `bank_balance` VARCHAR(128) NOT NULL COMMENT '银行卡余额',
  `currency_type` VARCHAR(32) NOT NULL COMMENT '货币类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `capture_time` BIGINT NOT NULL COMMENT '数据捕获时间'
)
Duplicate KEY (`phone_number`, `bank_account`)
COMMENT '银行卡信息表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`, `bank_account`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_high_frequency_words;  

CREATE TABLE IF NOT EXISTS dws_element_high_frequency_words (
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证账号类型',
  `virtual_account_type` VARCHAR(16) COMMENT '虚拟账号类型',
  `virtual_account` VARCHAR(512) COMMENT '虚拟账号',
  `high_frequency_word` VARCHAR(64) NOT NULL COMMENT '高频词',
  `word_type` VARCHAR(32) NOT NULL COMMENT '高频词类型',
  `data_source` TINYINT NOT NULL COMMENT '数据来源 1 email 2 sms',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT SUM COMMENT '词出现次数'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `virtual_account_type`, `virtual_account`, `high_frequency_word`, `word_type`, `data_source`, `capture_day`, `capture_month`)
COMMENT '高频词表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `high_frequency_word`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_phone_app_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_phone_app_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`phone_number`, `app_name`, `app_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '号码档案应用明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`, `app_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3"
);



DROP TABLE IF EXISTS dws_element_behavior_phone_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_phone_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `behavior_type` TINYINT NOT NULL COMMENT '行为类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`, `behavior_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '号码档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`, `behavior_type`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_relation_phone_virtual_account_detail;  

CREATE TABLE IF NOT EXISTS dws_element_relation_phone_virtual_account_detail (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `virtual_account_type` VARCHAR(16) NOT NULL COMMENT '虚拟账号类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `app_type` VARCHAR(128) NOT NULL COMMENT '虚拟账号应用类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`phone_number`, `virtual_account`, `virtual_account_type`, `data_type`, `app_type`, `capture_day`, `capture_month`)
COMMENT '号码档案关联虚拟账号'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`phone_number`, `virtual_account`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_phone_entity;  

CREATE TABLE IF NOT EXISTS dws_element_phone_entity (
  `phone_number` VARCHAR(20) NOT NULL COMMENT '电话号码',
  `insert_time` BIGINT min COMMENT '创建时间',
  `latest_relation_time` BIGINT max COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`)
COMMENT '号码档案建档实体表'
DISTRIBUTED BY HASH(`phone_number`) BUCKETS 20
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_theme_app_block_detail;  

CREATE TABLE dws_theme_app_block_detail (
  auth_account VARCHAR(64) COMMENT '认证账号',
  auth_type VARCHAR(16) COMMENT '认证账号类型',
  data_type INT COMMENT '协议类型',
  app_name VARCHAR(128) COMMENT '应用名称',
  app_type VARCHAR(128) COMMENT '应用类型',
  `capture_hour` int COMMENT '捕获小时',
  capture_day DATE NOT NULL COMMENT '捕获日期',
  `capture_month` int COMMENT '捕获月份',
  behavior_num BIGINT sum COMMENT '行为总数',
  earliest_relation_time BIGINT min COMMENT '最早关联时间',
  latest_relation_time BIGINT max COMMENT '最后关联时间'
)
AGGREGATE KEY(auth_account, auth_type, data_type,app_name,app_type,capture_hour,capture_day,capture_month)
COMMENT '阻断明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(auth_account, app_name) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_auth_account_info;  

CREATE TABLE IF NOT EXISTS dws_element_auth_account_info (
  `auth_account` varchar(64) COMMENT '认证账号',
  `auth_type` varchar(16) COMMENT '认证账号类型',
  `insert_time` bigint min COMMENT '建档时间',
  `latest_relation_time` bigint max COMMENT '最后关联时间'
)
AGGREGATE KEY(auth_account, auth_type)
COMMENT 'radius建档实体表'
DISTRIBUTED BY HASH(`auth_account`, `auth_type`) BUCKETS 2
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_theme_app_entity;  

CREATE TABLE IF NOT EXISTS dws_theme_app_entity (
  `app_name` varchar(128) COMMENT '应用名称',
  `app_type` varchar(128) COMMENT '应用类型',
  `app_name_alias` varchar(128) COMMENT '应用名称',
  `app_type_alias` varchar(128) COMMENT '应用类型',
  `insert_time` bigint min COMMENT '建档时间',
  `latest_relation_time` bigint max COMMENT '最后关联时间'
)
AGGREGATE KEY(app_name,app_type,app_name_alias,app_type_alias)
COMMENT '应用档案建档实体表'
DISTRIBUTED BY HASH(`app_name`) BUCKETS 1
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_theme_website_entity;  

CREATE TABLE dws_theme_website_entity (
   `domain` VARCHAR(1024),
   `insert_time` bigint min COMMENT '建档时间',
   `latest_relation_time` bigint max COMMENT '最后关联时间'
) ENGINE=OLAP
AGGREGATE KEY(domain)
COMMENT '网站档案建档实体表'
DISTRIBUTED BY HASH(`domain`) BUCKETS 200
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_relation_auth_account_virtual_account_detail;  

CREATE TABLE IF NOT EXISTS dws_element_relation_auth_account_virtual_account_detail (
  `auth_account` varchar(64) COMMENT '认证账号',
  `auth_type` varchar(16) COMMENT '认证账号类型',
  `virtual_account` varchar(512) COMMENT '虚拟账号',
  `virtual_account_type` varchar(16) COMMENT '虚拟账号类型',
  `target_account` varchar(512) COMMENT '通联虚拟账号',
  `app_type` varchar(128) COMMENT '虚拟账号应用类型',
  `data_type` int COMMENT '协议类型',
  `capture_day` date NOT NULL COMMENT '捕获日期',
  `capture_month` int COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` bigint sum COMMENT '行为总数'
)
AGGREGATE KEY(auth_account, auth_type, virtual_account, virtual_account_type,target_account, app_type, data_type,capture_day,capture_month)
COMMENT 'radius 档案虚拟账号明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `target_account`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_auth_account_app_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_auth_account_app_detail (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT  SUM COMMENT '行为总数'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `app_name`, `app_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT 'radius档案应用明细小时'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `app_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_attachment_day;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_attachment_day (
  `auth_account` VARCHAR(128) COMMENT '认证账号',
  `auth_type` VARCHAR(64) COMMENT '认证账号类型',
  `file_id` VARCHAR(32) NOT NULL COMMENT '文件id信息',
  `file_path` VARCHAR(256) COMMENT '报文路径',
  `attach_path` VARCHAR(256) COMMENT '附件文件路径',
  `attach_type` VARCHAR(16) NOT NULL COMMENT '附件类型',
  `attach_suffix` VARCHAR(32) COMMENT '附件后缀',
  `attach_name` VARCHAR(256) COMMENT '附件名称',
  `attach_size` INT COMMENT '附件大小',
  `attach_md5` VARCHAR(64) COMMENT '附件md5值',
  `domain` VARCHAR(1024) COMMENT '域名信息',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `virtual_account` VARCHAR(512) COMMENT '行为主体账号',
  `virtual_account_type` VARCHAR(64) COMMENT '行为主体账号类型',
  `virtual_app_type` VARCHAR(128) COMMENT '虚拟账号应用类型',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT  COMMENT '行为总数',
  `earliest_relation_time` BIGINT COMMENT '最早关联时间',
  `latest_relation_time` BIGINT COMMENT '最后关联时间'
)
Duplicate KEY (`auth_account`, auth_type)
COMMENT '附件明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, file_id, attach_md5) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_theme_block_detail_day;  

CREATE TABLE dws_theme_block_detail_day (
  auth_account VARCHAR(64) COMMENT '认证账号',
  auth_type VARCHAR(16) COMMENT '认证账号类型',
  virtual_account VARCHAR(512) COMMENT '虚拟账号',
  virtual_account_type VARCHAR(16) COMMENT '虚拟账号类型',
  data_type INT COMMENT '协议类型',
  src_country VARCHAR(128) COMMENT '源地址所在国家',
  dst_country VARCHAR(128) COMMENT '目的地址所在国家',
  source_city VARCHAR(128) COMMENT '源地址所在城市',
  dst_city VARCHAR(128) COMMENT '目的地址所在城市',
  strsrc_ip VARCHAR(64) COMMENT '字符串类型源地址ip',
  strdst_ip VARCHAR(64) COMMENT '字符串类型目的地址ip',
  domain VARCHAR(1024) COMMENT '域名信息',
  app_name VARCHAR(128) COMMENT '应用名称',
  app_type VARCHAR(128) COMMENT '应用类型',
  capture_day DATE NOT NULL COMMENT '捕获日期',
  `capture_month` int COMMENT '捕获月份',
  behavior_num BIGINT sum COMMENT '行为总数',
  earliest_relation_time BIGINT min COMMENT '最早关联时间',
  latest_relation_time BIGINT max COMMENT '最后关联时间'
)
AGGREGATE KEY(auth_account, auth_type, virtual_account,virtual_account_type,data_type,src_country,dst_country,source_city,dst_city,strsrc_ip,strdst_ip,domain,app_name,app_type,capture_day,capture_month)
COMMENT '阻断明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(auth_account,  virtual_account,`domain`,app_name) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_connection_day;  

CREATE TABLE dws_element_behavior_connection_day (
  auth_account VARCHAR(64) COMMENT '认证账号',
  auth_type VARCHAR(16) COMMENT '认证账号类型',
  `data_type` INT NOT NULL COMMENT '协议类型',
  virtual_account VARCHAR(512) COMMENT '虚拟账号',
  virtual_app_type VARCHAR(16) COMMENT '虚拟账号应用类型',
  important_target_flag TINYINT COMMENT '重要目标标志位 ',
  target_account VARCHAR(64) COMMENT '目标账号',
  main_account VARCHAR(512) COMMENT '行为主体账号',
  `action` VARCHAR(32) COMMENT '动作类型',
  spam_flag TINYINT COMMENT '垃圾邮件，骚扰短信标识  1 垃圾邮件 0 不是',
  capture_day DATE NOT NULL COMMENT '捕获日期',
  `capture_month` int COMMENT '捕获月份',
  behavior_num BIGINT sum COMMENT '行为总数',
  earliest_relation_time BIGINT min COMMENT '最早关联时间',
  latest_relation_time BIGINT max COMMENT '最后关联时间'
)
AGGREGATE KEY(auth_account, auth_type,data_type, virtual_account, virtual_app_type,important_target_flag, target_account, main_account, action, spam_flag, capture_day,capture_month)
COMMENT '通联明细天表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(auth_account, auth_type,data_type, virtual_account, virtual_app_type, target_account) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_element_behavior_auth_account_detail;  

CREATE TABLE IF NOT EXISTS dws_element_behavior_auth_account_detail (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `behavior_type` TINYINT NOT NULL COMMENT '行为类型 2 PR上网行为 3 NF 上网行为',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `behavior_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT 'radius档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_theme_app_behavior_detail;  

CREATE TABLE IF NOT EXISTS dws_theme_app_behavior_detail (
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `server_ip` VARCHAR(64) COMMENT '服务端ip',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `behavior_num` BIGINT SUM COMMENT '行为总数',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间'
)
AGGREGATE KEY (`app_name`, `app_type`, `server_ip`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '应用档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`app_name`, `server_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS dws_theme_website_behavior_detail;  

CREATE TABLE IF NOT EXISTS dws_theme_website_behavior_detail (
  `domain` varchar(1024) not null comment '域名',
  `server_ip` varchar(64) comment '服务端ip',
  `capture_hour` tinyint not null comment '捕获时段',
  `capture_day` date not null comment '捕获日期',
  `capture_month` int not null comment '捕获月份',
  `behavior_num` bigint  sum comment '行为总数',
  `earliest_relation_time` bigint  min comment '最早关联时间',
  `latest_relation_time` bigint  max comment '最后关联时间'
)
AGGREGATE KEY (`domain`, `server_ip`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '网站档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`domain`, `server_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);
