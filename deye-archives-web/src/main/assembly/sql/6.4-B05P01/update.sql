
delete from tb_arc_service_sql where code in ('PHONE_REAL_TIME_TRAJECTORY','PHONE_AREA_HOT_TIME_STATISTICS','PHONE_AREA_HOT_STAY_RECORD','PHONE_HIS_DAY_TRAJECTORY','PHONE_HIS_WEEK_TRAJECTORY','PHONE_HIS_MONTH_TRAJECTORY','PHONE_AREA_HOT_TRAJECTORY', 'PHONE_REGULAR_TRAJECTORY');


INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_REAL_TIME_TRAJECTORY', NULL, 'select \r\nstation_no baseStationNo\r\n,network_type netWorkType\r\n,station_longitude lng\r\n,station_latitude lat\r\n,address stationAddess\r\n,login_time earliestRelationTime\r\n,logout_time latestRelationTime\r\n,truncate((case when logout_time > login_time then logout_time - login_time else 0 end)/(3600000), 3) onlineHour\r\nfrom (\r\nSELECT phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,last_station\r\n,capture_time as login_time\r\n,lead(session_id, 1, \'\') over(partition by phone_number order by capture_time asc) as next_session_id\r\n,case when action = \'stop\' and station_no = last_station then 0 \r\n      when action = \'stop\' and station_no <> last_station then capture_time\r\n      when lead(session_id, 1, \'\') over(partition by phone_number order by capture_time asc) <> \'\' and \r\n           lead(session_id, 1, \'\') over(partition by phone_number order by capture_time asc) <> session_id then capture_time\r\n else lead(capture_time, 1, 0) over(partition by phone_number, session_id order by capture_time asc) end as logout_time\r\nfrom (\r\nSELECT phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,lag(station_no, 1, \'\') over(partition by phone_number, session_id order by capture_time asc) as last_station\r\n,case when action = \'stop\' then 1\r\n      when lag(station_no, 1, \'\') over(partition by phone_number, session_id order by capture_time asc) = station_no then 0\r\n      else 1 end as is_change\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n ${station_condition}\r\n ${keyword_condition}\r\n) as change_tmp\r\nwhere change_tmp.is_change = 1\r\n) as tmp_data\r\nwhere (tmp_data.action = \'stop\' and tmp_data.logout_time > 0 ) or tmp_data.action in (\'start\', \'update\')\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_AREA_HOT_TIME_STATISTICS', NULL, 'select CONCAT(FLOOR(capture_hour / ${interval}) * ${interval}, \'-\', (FLOOR(capture_hour / ${interval}) * ${interval} + ${interval})) timeRange,\r\nsum(stay_num) stayCount, count(DISTINCT station_no) areaCount, truncate(sum(stay_time)/3600, 3) stayHour\r\nfrom ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n${keyword_condition}\r\ngroup by timeRange\r\n${sort} ${top}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_AREA_HOT_STAY_RECORD', NULL, 'select station_no stationNo, in_time inTime, out_time outTime, truncate(stay_time/3600, 3) stayTime\r\nfrom ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= ${start_time}\r\nand capture_time <= ${end_time}\r\nand station_no = {stationNo}\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_HIS_DAY_TRAJECTORY', NULL, 'SELECT address stationAddess, station_no baseStationNo, network_type netWorkType, truncate(stay_time/3600, 3) onlineHour,\r\nin_time earliestRelationTime, out_time latestRelationTime, station_longitude lng, station_latitude lat\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n ${station_condition}\r\n ${keyword_condition}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_HIS_WEEK_TRAJECTORY', NULL, 'SELECT `any`(address) stationAddess, station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 3) onlineHour,\r\nmin(in_time) earliestRelationTime, max(out_time) latestRelationTime, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n ${station_condition}\r\n ${keyword_condition}\r\n group by station_no\r\n HAVING stayNum >= ${stayNumLimit}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_HIS_MONTH_TRAJECTORY', NULL, 'SELECT `any`(address) stationAddess, station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 3) onlineHour,\r\nmin(in_time) earliestRelationTime, max(out_time) latestRelationTime, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_month >= {start_month}\r\nand capture_month <= {end_month}\r\n ${station_condition}\r\n ${keyword_condition}\r\n group by station_no\r\n HAVING stayNum >= ${stayNumLimit}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_AREA_HOT_TRAJECTORY', NULL, 'SELECT `any`(address) stationAddess, station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 3) onlineHour,\r\nmin(in_time) earliestRelationTime, max(out_time) latestRelationTime, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n ${hour_condition}\r\n ${station_condition}\r\n ${keyword_condition}\r\n group by station_no\r\n');
