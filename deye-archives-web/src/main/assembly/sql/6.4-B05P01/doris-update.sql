CREATE TABLE IF NOT EXISTS ads_archive_phone_location_his (
    phone_number VARCHAR(20) NOT NULL COMMENT '电话号码',
    device_id VARCHAR(64) COMMENT '设备id',
    session_id VARCHAR(64) COMMENT '会话id',
    station_no VARCHAR(64) NOT NULL COMMENT '基站编号',
    network_type VARCHAR(16) COMMENT '网络类型',
    station_longitude DECIMAL(10,7) COMMENT '基站经度',
    station_latitude DECIMAL(10,7) COMMENT '基站纬度',
    address VARCHAR(256) COMMENT '基站地址',
    in_time BIGINT COMMENT '进入基站时间',
    out_time BIGINT COMMENT '离开时间',
    stay_time BIGINT COMMENT '驻留时长（秒）',
    stay_num BIGINT COMMENT '驻留次数',
    capture_time BIGINT NOT NULL COMMENT '数据捕获时间',
    capture_hour TINYINT NOT NULL COMMENT '捕获时段',
    capture_day DATE NOT NULL COMMENT '捕获日期',
    capture_month INT NOT NULL COMMENT '捕获月份',
    insert_time BIGINT NOT NULL COMMENT '插入时间'
    ) ENGINE=OLAP
    DUPLICATE KEY (`phone_number`)
    COMMENT '号码档案基站历史信息表'
    AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
    DISTRIBUTED BY HASH(`phone_number`) BUCKETS AUTO
    PROPERTIES (
        "replication_allocation" = "tag.location.default: 3"
    );

CREATE MATERIALIZED VIEW ads_archive_phone_location_his_month_view
AS
SELECT station_no,capture_month,any(address), any(network_type), sum(stay_time) stay_time,
    min(in_time), max(out_time), any(station_longitude), any(station_latitude), sum(stay_num)
FROM ads_archive.ads_archive_phone_location_his
group by station_no, capture_month;