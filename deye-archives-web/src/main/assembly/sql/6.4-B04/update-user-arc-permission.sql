DELETE from tb_service_role_permission where  service_type=1;

DELETE from tb_service_user_permission where  service_type=1;

REPLACE INTO tb_service_role_permission ( role_id, permission_id, permission_option_codes, service_type, create_time, modify_time, create_user_id )
VALUES
        ( 580694, 100, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 ),
        ( 580694, 101, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 ),
        ( 580694, 102, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 ),
        ( 580694, 103, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 ),
        ( 580694, 104, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 ),
        ( 580694, 105, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 ),
        ( 580694, 106, 1, 1, UNIX_TIMESTAMP(), <PERSON>IX_TIMESTAMP(),- 1 ),
        ( 580694, 107, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),- 1 );