DROP TABLE IF EXISTS dws_element_radius_auth_record;
DROP TABLE IF EXISTS dws_theme_website_entity;
DROP TABLE IF EXISTS dws_theme_app_entity;
DROP TABLE IF EXISTS dws_element_auth_account_info;
DROP TABLE IF EXISTS dws_element_phone_entity;
DROP TABLE IF EXISTS dws_element_behavior_auth_account_app_detail;
DROP TABLE IF EXISTS dws_element_behavior_phone_app_detail;



CREATE TABLE IF NOT EXISTS dws_element_behavior_phone_app_detail (
  `phone_number` BIGINT NOT NULL COMMENT '电话号码',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT SUM COMMENT '行为总数'
)
AGGREGATE KEY (`phone_number`, `app_name`, `app_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT '号码档案应用明细小时表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`phone_number`, `app_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);



CREATE TABLE IF NOT EXISTS dws_element_phone_entity (
  `phone_number` BIGINT NOT NULL COMMENT '电话号码',
  `insert_time` BIGINT min COMMENT '创建时间',
  `latest_relation_time` BIGINT max COMMENT '最后关联时间'
)
AGGREGATE KEY (`phone_number`)
COMMENT '号码档案建档实体表'
DISTRIBUTED BY HASH(`phone_number`) BUCKETS 20
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

CREATE TABLE IF NOT EXISTS dws_element_auth_account_info (
  `auth_account` varchar(64) COMMENT '认证账号',
  `auth_type` varchar(16) COMMENT '认证账号类型',
  `insert_time` bigint min COMMENT '建档时间',
  `latest_relation_time` bigint max COMMENT '最后关联时间'
)
AGGREGATE KEY(auth_account, auth_type)
COMMENT 'radius建档实体表'
DISTRIBUTED BY HASH(`auth_account`, `auth_type`) BUCKETS 2
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

CREATE TABLE IF NOT EXISTS dws_theme_app_entity (
  `app_name` varchar(128) COMMENT '应用名称',
  `app_type` varchar(128) COMMENT '应用类型',
  `app_name_alias` varchar(128) COMMENT '应用名称',
  `app_type_alias` varchar(128) COMMENT '应用类型',
  `insert_time` bigint min COMMENT '建档时间',
  `latest_relation_time` bigint max COMMENT '最后关联时间'
)
AGGREGATE KEY(app_name,app_type,app_name_alias,app_type_alias)
COMMENT '应用档案建档实体表'
DISTRIBUTED BY HASH(`app_name`) BUCKETS 1
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

CREATE TABLE dws_theme_website_entity (
   `domain` VARCHAR(1024),
   `insert_time` bigint min COMMENT '建档时间',
   `latest_relation_time` bigint max COMMENT '最后关联时间'
) ENGINE=OLAP
AGGREGATE KEY(domain)
COMMENT '网站档案建档实体表'
DISTRIBUTED BY HASH(`domain`) BUCKETS 200
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

CREATE TABLE IF NOT EXISTS dws_element_behavior_auth_account_app_detail (
  `auth_account` VARCHAR(128) NOT NULL COMMENT '认证账号',
  `auth_type` VARCHAR(64) NOT NULL COMMENT '认证账号类型',
  `app_name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `app_type` VARCHAR(128) NOT NULL COMMENT '应用类型',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间',
  `behavior_num` BIGINT  SUM COMMENT '行为总数'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `app_name`, `app_type`, `capture_hour`, `capture_day`, `capture_month`)
COMMENT 'radius档案应用明细小时'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`, `app_name`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);
