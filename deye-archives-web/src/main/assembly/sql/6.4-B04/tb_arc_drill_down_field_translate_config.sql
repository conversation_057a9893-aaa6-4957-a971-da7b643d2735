/*
 Navicat Premium Data Transfer

 Source Server         : *************-开发
 Source Server Type    : MySQL
 Source Server Version : 50633
 Source Host           : *************:3306
 Source Schema         : deye_archive_6.4-B04

 Target Server Type    : MySQL
 Target Server Version : 50633
 File Encoding         : 65001

 Date: 07/06/2024 16:33:04
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_arc_drill_down_field_translate_config
-- ----------------------------
DROP TABLE IF EXISTS `tb_arc_drill_down_field_translate_config`;
CREATE TABLE `tb_arc_drill_down_field_translate_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `field` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名称',
  `translate_type` tinyint(4) NOT NULL COMMENT '字段翻译类型：1=字典编码翻译；2=时间戳格式化;3=日期格式化;4=国家码翻译',
  `protocol_id` int(11) NOT NULL COMMENT '协议id,默认为-1即通用协议字段其它情况表示对应协议特殊字段,字段翻译时优先取协议对应字段再取通用字段',
  `dict_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典key',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '明细下钻字段翻译配置表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of tb_arc_drill_down_field_translate_config
-- ----------------------------
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (1, 'auth_type', 1, -1, 'auth_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (2, 'mail_date', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (3, 'action', 1, -1, 'action');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (4, 'capture_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (5, 'os_name', 1, -1, 'os_name');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (6, 'uparea_id', 1, -1, 'uparea_id');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (7, 'isp_id', 1, -1, 'isp_id');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (8, 'browse_type', 1, -1, 'browse_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (9, 'spam_flag', 1, -1, 'spam_flag');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (10, 'forgery_flag', 1, -1, 'forgery_flag');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (11, 'proxy_type', 1, -1, 'proxy_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (12, 'child_type', 1, -1, 'child_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (13, 'earliest_relation_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (14, 'latest_relation_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (15, 'capture_day', 3, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (16, 'net_action', 1, -1, 'net_action');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (17, 'hardware_type', 1, -1, 'hardware_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (18, 'start_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (19, 'end_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (20, 'call_type', 1, -1, 'call_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (21, 'postal_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (22, 'group_create_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (23, 'post_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (24, 'reg_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (25, 'publish_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (26, 'in_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (27, 'out_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (28, 'regis_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (29, 'buy_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (30, 'oper_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (31, 'login_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (32, 'insert_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (33, 'logout_time', 2, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (34, 'country_code', 1, -1, 'country_code');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (35, 'data_type', 1, -1, 'data_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (36, 'network_type', 1, -1, 'network_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (37, 'calling_atrribution', 4, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (38, 'called_atrribution', 4, -1, NULL);
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (39, 'coordinate_type', 1, -1, 'coordinate_type');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (40, 'action', 1, 109, 'call_action');
INSERT INTO `tb_arc_drill_down_field_translate_config` VALUES (41, 'action', 1, 200, 'call_action');

SET FOREIGN_KEY_CHECKS = 1;
