delete from tb_arc_service_sql where code in 
('EMAIL_RELATION_ACCOUNT','EMAIL_NICKNAME_RECORD','EMAIL_PASSWORD_RECORD','EMAIL_TOTAL_BEHAVIOR_NUMBER','EMAIL_ONE_WEEK_BEHAVIOR_NUMBER','EMAIL_TOTAL_COMMUNICATE_MAIL_NUMBER','EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER','EMAIL_TOTAL_ACTIVE_AREA_NUMBER','EMAIL_ONE_WEEK_ACTIVE_AREA_NUMBER','EMAIL_TOTAL_NICKNAME_NUMBER','EMAIL_ONE_WEEK_NICKNAME_NUMBER','EMAIL_TOTAL_PASSWORD_NUMBER','EMAIL_ONE_WEEK_PASSWORD_NUMBER','EMAIL_TOTAL_FILE_NUMBER','EMAIL_ONE_WEEK_FILE_NUMBER','EMAIL_TOTAL_IMPORTANT_TARGET_NUMBER','EMAIL_ONE_WEEK_IMPORTANT_TARGET_NUMBER','COMMON_EMAIL_DAY_TREND_ANALYZE','COMMON_EMAIL_DAY_HOUR_ANALYZE','RADIUS_HIGH_FREQUENCY_WORDS_RANK','EMAIL_HIGH_FREQUENCY_WORDS_RANK','RADIUS_HIGH_FREQUENCY_WORDS_TREND','EMAIL_HIGH_FREQUENCY_WORDS_TREND','EMAIL_TOP_EMAIL_ACCOUNT','EMAIL_PHONE_EXTRACT','EMAIL_PHONE_EXTRACT_SOURCE','EMAIL_PHONE_EXTRACT_FILE_LIST','EMAIL_LIS_DETAIL_LIST','IM_TOTAL_BEHAVIOR_NUMBER','IM_ONE_WEEK_BEHAVIOR_NUMBER','IM_TOTAL_ACTIVE_AREA_NUMBER','IM_ONE_WEEK_ACTIVE_AREA_NUMBER','IM_TOTAL_COMMUNICATE_ACCOUNT_NUMBER','IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER','IM_TOTAL_FILE_NUMBER','IM_ONE_WEEK_FILE_NUMBER','IM_TOTAL_IMPORTANT_TARGET_NUMBER','IM_ONE_WEEK_IMPORTANT_TARGET_NUMBER','COMMON_IM_DAY_TREND_ANALYZE','COMMON_IM_DAY_HOUR_ANALYZE','VIRTUAL_RELATE_AUTH_ACCOUNT','IM_TOP_COMMUNICATE_ACCOUNT','COMMON_IM_LIS_DAY_TREND_ANALYZE','PHONE_AREA_HOT_TIME_STATISTICS','PHONE_AREA_HOT_STAY_RECORD','PHONE_IMEI_LIST','DOMAIN_ARC_TARGET_DAY_LIST','DOMAIN_IP_TARGET_DAY_LIST','APP_ARC_TARGET_DAY_LIST','APP_IP_TARGET_DAY_LIST','QUERY_ATTACHMENT_INFO','COMMON_PHONE_DAY_HOUR_ANALYZE','APP_IMPORTANT_TARGET_DAY_LIST','DOMAIN_IMPORTANT_TARGET_DAY_LIST','PHONE_FREQUENT_ACTIVE_AREA','PHONE_REAL_TIME_TRAJECTORY','QUERY_ATTACHMENT_INFO_OVERVIEW','RADIUS_AUTH_RECORD','RADIUS_AUTH_RECORD_DURATION_STATISTICS','EMAIL_RELATION_EXTENSION','PHONE_NETWORK_TYPE_DISTRIBUTION','PHONE_USE_DEVICE_RANK','HIGH_FREQUENCY_WORDS_RANK','HIGH_FREQUENCY_WORDS_TREND','HIGH_FREQUENCY_WORDS_RANK_TOTAL');


INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('DOMAIN_ARC_TARGET_DAY_LIST', NULL, 'select\r\n		visitor_archive_type as arcType,\r\n		visitor_archive_name as arcAccount,\r\n		visitor_country  as srcCountry,\r\n		sum(nf_behavior_num) as nfNum,\r\n		min(earliest_relation_time) as earliestTime,\r\n		max(latest_relation_time) as latestTime\r\nfrom\r\n		ads_archive_website_arc_visitor_detail_day\r\nwhere\r\n		${timeCondition}\r\n	and domain = {domain} ${net_action} ${keyWord}\r\ngroup by\r\n		visitor_archive_name ,\r\n		visitor_archive_type ,\r\n		visitor_country \r\norder by\r\n	${orderField} ${orderType} ');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('DOMAIN_IP_TARGET_DAY_LIST', NULL, 'select\r\n	0 as arcType,\r\n	ip as arcAccount,\r\n	visitor_country as srcCountry,\r\n	sum(nf_behavior_num) as nfNum,\r\n	min(earliest_relation_time) as earliestTime,\r\n	max(latest_relation_time) as latestTime\r\nfrom\r\n	ads_archive_website_ip_visitor_detail_day\r\nwhere\r\n	${timeCondition}	\r\n	and domain = {domain} and ip <> \'\' ${net_action}	${keyWord}\r\ngroup by\r\n	ip,\r\n	visitor_country\r\nORDER BY\r\n    ${orderField} ${orderType} \r\n     ');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('APP_ARC_TARGET_DAY_LIST', NULL, 'select\r\n		visitor_archive_type as arcType,\r\n		visitor_archive_name as arcAccount,\r\n		visitor_country  as srcCountry,\r\n		sum(nf_behavior_num) as nfNum,\r\n		min(earliest_relation_time) as earliestTime,\r\n		max(latest_relation_time) as latestTime\r\n	from\r\n		ads_archive_app_arc_visitor_detail_day\r\n	where\r\n		${timeCondition}	\r\n		and app_name = {app_name} ${app_type} ${net_action}	${keyWord}\r\n	group by\r\n		visitor_archive_name ,\r\n		visitor_archive_type ,\r\n		visitor_country \r\nORDER BY\r\n    ${orderField} ${orderType} 	');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('APP_IP_TARGET_DAY_LIST', NULL, 'select\r\n	0 as arcType,\r\n	ip as arcAccount,\r\n	visitor_country as srcCountry,\r\n	sum(nf_behavior_num) as nfNum,\r\n	min(earliest_relation_time) as earliestTime,\r\n	max(latest_relation_time) as latestTime\r\nfrom\r\n	ads_archive_app_ip_visitor_detail_day\r\nwhere\r\n	${timeCondition}\r\n	and app_name = {app_name} ${app_type}  \r\n	and ip <> \'\' ${net_action} ${keyWord}\r\ngroup by\r\n	ip,\r\n	visitor_country\r\nORDER BY\r\n	${orderField} ${orderType}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('QUERY_ATTACHMENT_INFO', NULL, 'SELECT\r\n	max(attach_path) attachPath,\r\n	data_type dataType,\r\n	attach_md5 attachMd5,\r\n	max(attach_suffix) AS attachType,\r\n	max(attach_name) AS attachName,\r\n	max(attach_size) AS attachSize,\r\n	array_join(collect_set(virtual_account), \',\') AS virtualAccount,\r\n	array_join(collect_set(auth_account), \',\') AS authAccount,\r\n	MIN(earliest_relation_time ) AS firstAppearTime,\r\n	MAX(latest_relation_time )AS lastAppearTime,\r\n	SUM(behavior_num) AS appearTimes\r\nFROM\r\n	${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n	${account_type_condition}  in (${arc_account})\r\n	and attach_path <> \'\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}  ${dataType}\r\n	${fileType} \r\n	${keyword}\r\nGROUP BY\r\n	attach_md5,\r\n	data_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('COMMON_PHONE_DAY_HOUR_ANALYZE', NULL, 'SELECT SUM(behavior_num) as num, capture_hour as `period` FROM ${dws}.dws_element_behavior_phone_detail WHERE capture_day >= {start_day} and capture_day <= {end_day} and phone_number = ${arc_account} and behavior_type = {behavior_type} GROUP BY capture_hour ORDER BY capture_hour');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'APP_IMPORTANT_TARGET_DAY_LIST', NULL, 'select\r\n	important_target_type as arcType,\r\n	important_target as arcAccount,\r\n	visitor_country as srcCountry,\r\n	sum(nf_behavior_num) as nfNum,\r\n	min(earliest_relation_time) as earliestTime,\r\n	max(latest_relation_time) as latestTime\r\nfrom\r\n	ads_archive_app_important_target_visitor_detail_day\r\nwhere\r\n	${timeCondition}\r\n	and app_name = {app_name} ${app_type} ${net_action} ${keyWord}\r\ngroup by\r\n	arcAccount,\r\n	arcType,\r\n	visitor_country\r\nORDER BY\r\n	${orderField} ${orderType}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'DOMAIN_IMPORTANT_TARGET_DAY_LIST', NULL, 'select\r\n	important_target_type as arcType,\r\n	important_target as arcAccount,\r\n	visitor_country as srcCountry,\r\n	sum(nf_behavior_num) as nfNum,\r\n	min(earliest_relation_time) as earliestTime,\r\n	max(latest_relation_time) as latestTime\r\nfrom\r\n	ads_archive_website_important_target_visitor_detail_day\r\nwhere\r\n	${timeCondition}\r\n	and domain = {domain} ${net_action} ${keyWord}\r\ngroup by\r\n	arcAccount,\r\n	arcType,\r\n	visitor_country\r\nORDER BY\r\n	${orderField} ${orderType}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_FREQUENT_ACTIVE_AREA', NULL, 'SELECT base_station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 1) onlineHour,\r\n`any`(address) stationAddess, `any`(station_longitude) lng, `any`(station_latitude) lat\r\nFROM ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= \'${start_day}\'\r\nand capture_day <= \'${end_day}\'\r\ngroup by baseStationNo order by onlineHour desc');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_REAL_TIME_TRAJECTORY', NULL, 'SELECT base_station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 1) onlineHour,\r\n`any`(address) stationAddess, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum, min(in_time) earliestRelationTime, max(out_time) latestRelationTime\r\nFROM ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n${hour_condition} ${station_condition}\r\n${keyword_condition}\r\ngroup by baseStationNo');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'QUERY_ATTACHMENT_INFO_OVERVIEW', NULL, 'SELECT\r\n	max(attach_path) attachPath,\r\n	max(attach_name) AS attachName,\r\n	max(attach_size) AS attachSize,\r\n	max(attach_type) AS attachType,\r\n	max(latest_relation_time) AS latestRelationTime,\r\n	attach_md5 AS attachMd5,\r\n	data_type AS dataType\r\nFROM\r\n	${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n	${account_type_condition}  in (${arc_account})\r\n	and attach_path <> \'\'\r\n	and attach_type <> \'OTHER\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day} ${dataType}\r\nGROUP BY\r\n	attach_md5,data_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'RADIUS_AUTH_RECORD', NULL, 'SELECT\r\n	capture_time AS captureTime,\r\n	CASE `action` when \'0\' then \'login\' when \'1\' then \'logout\' end as `action`,\r\n	CASE\r\n		`action` when \'0\' then 0\r\n		else ROUND(duration / 3600, 2)\r\n	END AS onlineHour,\r\n	ip,\r\n	mac\r\nfrom\r\n	${dws}.dws_element_radius_auth_record\r\nwhere\r\n	auth_account = {radius} ${ip}\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'RADIUS_AUTH_RECORD_DURATION_STATISTICS', NULL, 'SELECT\r\n	ROUND(SUM(duration) / 3600, 2) AS total\r\nfrom\r\n	${dws}.dws_element_radius_auth_record\r\nwhere\r\n	auth_account = {radius}\r\n	${ip}\r\n	and action = \'1\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_RELATION_EXTENSION', NULL, 'select src_account,dst_account,total from (\r\nSELECT\r\ncase \r\nwhen virtual_account in (${relateVirtualAccounts}) then virtual_account  \r\nwhen target_account in (${relateVirtualAccounts}) then target_account  \r\nend src_account,\r\ncase \r\nwhen virtual_account in (${relateVirtualAccounts}) then target_account \r\nwhen target_account in (${relateVirtualAccounts}) then virtual_account  \r\nend dst_account,\r\nsum(behavior_num) total\r\nFROM ${dws}.dws_element_behavior_connection_day \r\nwhere \r\n(virtual_account in (${relateVirtualAccounts}) or target_account in (${relateVirtualAccounts}) )\r\n${virtual_app_type_condition}\r\nand data_type = \'${data_type}\'\r\nand capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\nand spam_flag=0\r\ngroup by src_account,dst_account\r\n) as t ${minLinkCountCondition}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_RELATION_ACCOUNT', NULL, 'select virtual_account ,max(latest_relation_time) latest_relation_time from \r\n${dws}.dws_element_relation_phone_virtual_account_detail where virtual_account <> {virtual_account}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand data_type = 101\r\nand phone_number in (\r\nselect phone_number  from \r\n${dws}.dws_element_relation_phone_virtual_account_detail \r\nwhere virtual_account = {virtual_account}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand data_type = 101\r\ngroup by phone_number\r\n) group by virtual_account\r\n\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_NICKNAME_RECORD', NULL, 'select alias nickname, min(earliest_relation_time) earliestTime, max(latest_relation_time) latestTime, sum(behavior_num) num\r\nfrom ${dws}.dws_element_nickname_detail\r\nwhere virtual_account = {virtual_account}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by alias');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_PASSWORD_RECORD', NULL, 'select password, min(earliest_relation_time) earliestTime, max(latest_relation_time) latestTime, sum(behavior_num) num\r\nfrom ${dws}.dws_element_password_detail\r\nwhere virtual_account = {virtual_account}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by password');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_BEHAVIOR_NUMBER', NULL, 'SELECT virtual_account as arcName, sum(behavior_num) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_BEHAVIOR_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	sum(behavior_num) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_email_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${email})\r\ngroup by\r\n		virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_COMMUNICATE_MAIL_NUMBER', NULL, 'SELECT virtual_account as arcName, bitmap_union_count(connect_account_total) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER', NULL, 'SELECT \r\n	arcName,\r\n	count(distinct account) as nums\r\nfrom\r\n	(\r\n	select\r\n		1 as flag,\r\n		target_account as arcName,\r\n		virtual_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account in (${email}) ${dataType}\r\nUNION\r\n	select\r\n		2 as flag,\r\n		virtual_account as arcName,\r\n		target_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${email}) ${dataType}\r\n) t\r\ngroup by\r\n	arcName;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_ACTIVE_AREA_NUMBER', NULL, 'SELECT virtual_account as arcName, bitmap_union_count(active_area_total) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_ACTIVE_AREA_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	count(distinct active_country) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_email_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${email})\r\ngroup by\r\n		virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_NICKNAME_NUMBER', NULL, 'SELECT virtual_account as arcName, bitmap_union_count(nickname_total) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_NICKNAME_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	count(distinct alias) as nums\r\nfrom\r\n		${dws}.dws_element_nickname_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${email})\r\ngroup by\r\n		virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_PASSWORD_NUMBER', NULL, 'SELECT virtual_account as arcName, bitmap_union_count(password_total) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_PASSWORD_NUMBER', NULL, 'SELECT\r\n	virtual_account as arcName,\r\n	count(distinct password) as nums\r\nfrom\r\n		${dws}.dws_element_password_detail\r\nWHERE\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${email})\r\ngroup by\r\n	virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_FILE_NUMBER', NULL, 'SELECT virtual_account as arcName, bitmap_union_count(file_num) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_FILE_NUMBER', NULL, 'SELECT\r\n	virtual_account as arcName,\r\n	count(distinct attach_md5) as nums\r\nFROM\r\n		${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and attach_path <> \'\'\r\n	and virtual_account in (${email}) ${dataType}\r\ngroup by\r\n	virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOTAL_IMPORTANT_TARGET_NUMBER', NULL, 'SELECT virtual_account as arcName, bitmap_union_count(important_target_total) AS nums FROM ads_archive_email_statistics WHERE virtual_account in (${email}) GROUP BY virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_IMPORTANT_TARGET_NUMBER', NULL, 'SELECT\r\n	arcName,\r\n	count(1) as nums\r\nfrom\r\n	(\r\n	select\r\n		virtual_account as arcName,\r\n		important_target,\r\n		important_target_type\r\n	from\r\n		${dws}.dws_element_relation_virtual_account_target_detail\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${email}) ${dataType}\r\n	group by\r\n		virtual_account,\r\n		important_target,\r\n		important_target_type\r\n	) tmp\r\ngroup by\r\n	arcName;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_EMAIL_DAY_TREND_ANALYZE', NULL, 'SELECT DATE_FORMAT(capture_day, \'yyyyMMdd\') as `time`, sum(behavior_num) as num FROM ${dws}.dws_element_behavior_email_detail WHERE capture_day >= {start_day} and capture_day <= {end_day} and virtual_account = {arc_account} GROUP BY capture_day ORDER BY capture_day');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_EMAIL_DAY_HOUR_ANALYZE', NULL, 'SELECT SUM(behavior_num) as num, capture_hour as `period` FROM ${dws}.dws_element_behavior_email_detail WHERE capture_day >= {start_day} and capture_day <= {end_day} and virtual_account = {arc_account} GROUP BY capture_hour ORDER BY capture_hour');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'RADIUS_HIGH_FREQUENCY_WORDS_RANK', NULL, 'SELECT\r\n	high_frequency_word as highFrequencyWord ,\r\n	word_type as wordType,\r\n	SUM(behavior_num) AS behaviorNum\r\nFROM\r\n	${dws}.dws_element_high_frequency_words\r\nWHERE\r\n	capture_day BETWEEN {start_day} AND {end_day}\r\n	AND auth_account = {arcAccount}\r\n	AND auth_type = {arcAccountType}\r\n	AND data_source in (${dataSource})\r\nGROUP BY\r\n	high_frequency_word,\r\n	word_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_HIGH_FREQUENCY_WORDS_RANK', NULL, 'SELECT\r\n	high_frequency_word as highFrequencyWord ,\r\n	word_type as wordType,\r\n	SUM(behavior_num) AS behaviorNum\r\nFROM\r\n	${dws}.dws_element_high_frequency_words\r\nWHERE\r\n	capture_day BETWEEN {start_day} AND {end_day}\r\n	AND virtual_account = {arcAccount}\r\n	AND data_source in (${dataSource})\r\nGROUP BY\r\n	high_frequency_word,\r\n	word_type\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'RADIUS_HIGH_FREQUENCY_WORDS_TREND', NULL, 'SELECT\r\n	UNIX_TIMESTAMP(capture_day) * 1000 AS captureTime,\r\n	high_frequency_word AS highFrequencyWord,\r\n	behavior_num AS behaviorNum\r\nFROM\r\n	(\r\n	SELECT\r\n		capture_day,\r\n		high_frequency_word,\r\n		SUM(behavior_num) behavior_num,\r\n		ROW_NUMBER() OVER (PARTITION BY capture_day\r\n	ORDER BY\r\n		SUM(behavior_num) DESC) AS row_num\r\n	FROM\r\n		${dws}.dws_element_high_frequency_words\r\n	WHERE\r\n		auth_account = \'${arcAccount}\'\r\n		and capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and data_source in (${dataSource})\r\n		and auth_type = \'${arcAccountType}\'\r\n	group by\r\n		capture_day,\r\n		high_frequency_word ) tt\r\nWHERE\r\n	row_num = 1\r\norder by\r\n	capture_day');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_HIGH_FREQUENCY_WORDS_TREND', NULL, 'SELECT\r\n	UNIX_TIMESTAMP(capture_day) * 1000 AS captureTime,\r\n	high_frequency_word AS highFrequencyWord,\r\n	behavior_num AS behaviorNum\r\nFROM\r\n	(\r\n	SELECT\r\n		capture_day,\r\n		high_frequency_word,\r\n		SUM(behavior_num) behavior_num,\r\n		ROW_NUMBER() OVER (PARTITION BY capture_day\r\n	ORDER BY\r\n		SUM(behavior_num) DESC) AS row_num\r\n	FROM\r\n		${dws}.dws_element_high_frequency_words\r\n	WHERE\r\n		virtual_account = \'${arcAccount}\'\r\n		and capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and data_source in (${dataSource})\r\n	group by\r\n		capture_day,\r\n		high_frequency_word ) tt\r\nWHERE\r\n	row_num = 1\r\norder by\r\n	capture_day');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_TOP_EMAIL_ACCOUNT', NULL, 'SELECT\r\n	email,\r\n	sum(num) as num\r\nFROM\r\n	(\r\n	SELECT\r\n		1 as flag,\r\n		target_account as email,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account = {email}\r\n		and target_account <> \'\' ${dataType}\r\n	GROUP BY\r\n		target_account\r\nUNION\r\n	SELECT\r\n		2 as flag,\r\n		virtual_account as email,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account = {email}\r\n		and virtual_account <> \'\' ${dataType}\r\n	GROUP BY\r\n		virtual_account\r\n) t\r\nGROUP BY\r\n	email\r\nORDER BY\r\n	num DESC');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_PHONE_EXTRACT', NULL, 'select phone_number phone, count() behaviorNum, MAP_AGG(if(r <= 3, attach_name, null), if(r <= 3, attach_path, null)) fileInfo, max(capture_time) captureTime from (\r\nselect phone_number,file_path,attach_path, attach_name,attach_md5, capture_time,\r\nRANK() over(partition by phone_number order by	capture_time desc, attach_md5 desc) r \r\nfrom ${dws}.dws_element_email_phone_extract_detail \r\nwhere virtual_account = {virtual_account}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n) as t \r\ngroup by phone_number');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_PHONE_EXTRACT_SOURCE', NULL, 'select virtual_account virtualAccount, auth_account authAccount, auth_type authType,attach_name attachName, file_path filePath, capture_day captureDay\r\nfrom  ${dws}.dws_element_email_phone_extract_detail\r\nwhere virtual_account = {virtual_account}\r\nand phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_PHONE_EXTRACT_FILE_LIST', NULL, 'select attach_name fileName, attach_path filePath,attach_md5 fileMd5,capture_day captureDay\r\nfrom  ${dws}.dws_element_email_phone_extract_detail\r\nwhere virtual_account = {virtual_account}\r\nand phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_LIS_DETAIL_LIST', NULL, 'select\r\n	${fields}\r\nfrom\r\n		${dwd}.dwd_lis_email\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day} ${accountCondition} ${keyword} ${importantTarget} ORDER BY capture_time DESC ');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOTAL_BEHAVIOR_NUMBER', NULL, 'SELECT virtual_account as arcName, virtual_app_type as appType, sum(behavior_num) AS nums FROM ads_archive_im_statistics WHERE virtual_account in (${imAccount}) ${app_type} GROUP BY virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_BEHAVIOR_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	virtual_app_type as appType,\r\n	sum(behavior_num) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_im_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${imAccount}) ${app_type}\r\ngroup by\r\n		virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOTAL_ACTIVE_AREA_NUMBER', NULL, 'SELECT virtual_account as arcName, virtual_app_type as appType, bitmap_union_count(active_area_total) AS nums FROM ads_archive_im_statistics WHERE virtual_account in (${imAccount}) ${app_type} GROUP BY virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_ACTIVE_AREA_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	virtual_app_type as appType,\r\n	count(distinct active_country) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_im_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${imAccount}) ${app_type}\r\ngroup by\r\n		virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOTAL_COMMUNICATE_ACCOUNT_NUMBER', NULL, 'SELECT virtual_account as arcName, virtual_app_type as appType, bitmap_union_count(connect_account_total) AS nums FROM ads_archive_im_statistics WHERE virtual_account in (${imAccount}) ${app_type} GROUP BY virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	virtual_app_type as appType,\r\n	count(distinct target_account) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_connection_day\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${imAccount}) ${app_type} ${dataType}\r\ngroup by\r\n	virtual_account,\r\n	virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOTAL_FILE_NUMBER', NULL, 'SELECT virtual_account as arcName,virtual_app_type as appType, bitmap_union_count(file_num) AS nums FROM ads_archive_im_statistics WHERE virtual_account in (${imAccount}) ${app_type} GROUP BY virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_FILE_NUMBER', NULL, 'SELECT\r\n	virtual_account as arcName,\r\n	virtual_app_type as appType,\r\n	count(distinct attach_md5) as nums\r\nFROM\r\n		${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and attach_path <> \'\'\r\n	and virtual_account in (${imAccount}) ${app_type} ${dataType}\r\ngroup by\r\n	virtual_account,\r\n	virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOTAL_IMPORTANT_TARGET_NUMBER', NULL, 'SELECT virtual_account as arcName, virtual_app_type as appType, bitmap_union_count(important_target_total) AS nums FROM ads_archive_im_statistics WHERE virtual_account in (${imAccount}) ${app_type} GROUP BY virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_IMPORTANT_TARGET_NUMBER', NULL, 'SELECT\r\n	arcName,\r\n	appType,\r\n	count(1) as nums\r\nfrom\r\n	(\r\n	select\r\n		virtual_account as arcName,\r\n		virtual_app_type as appType,\r\n		important_target,\r\n		important_target_type\r\n	from\r\n		${dws}.dws_element_relation_virtual_account_target_detail\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${imAccount}) ${app_type} ${dataType}\r\n	group by\r\n		virtual_account,\r\n		virtual_app_type,\r\n		important_target,\r\n		important_target_type\r\n	) tmp\r\ngroup by\r\n	arcName,\r\n	appType;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_IM_DAY_TREND_ANALYZE', NULL, 'SELECT DATE_FORMAT(capture_day, \'yyyyMMdd\') as `time`, sum(behavior_num) as num FROM ${dws}.dws_element_behavior_im_detail WHERE capture_day >= {start_day} and capture_day <= {end_day} and virtual_account = {arc_account} ${app_type} GROUP BY capture_day ORDER BY capture_day');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_IM_DAY_HOUR_ANALYZE', NULL, 'SELECT SUM(behavior_num) as num, capture_hour as `period` FROM ${dws}.dws_element_behavior_im_detail WHERE capture_day >= {start_day} and capture_day <= {end_day} and virtual_account = {arc_account} ${app_type} GROUP BY capture_hour ORDER BY capture_hour');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'VIRTUAL_RELATE_AUTH_ACCOUNT', NULL, 'select auth_account account,auth_type accountType,earliest_relation_time earliestTime,latest_relation_time latestTime,num from (\r\n(select phone_number auth_account, \'1020004\' auth_type, min(earliest_relation_time) earliest_relation_time, max(latest_relation_time) latest_relation_time, count() num\r\nfrom ${dws}.dws_element_relation_phone_virtual_account_detail\r\nwhere virtual_account = {virtual_account}\r\nand data_type = {data_type}\r\n${app_type_condition}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by phone_number)\r\nunion ALL \r\n(select auth_account, auth_type, min(earliest_relation_time) earliest_relation_time, max(latest_relation_time) latest_relation_time, count() num\r\nfrom ${dws}.dws_element_relation_auth_account_virtual_account_detail\r\nwhere virtual_account = {virtual_account}\r\nand data_type = {data_type}\r\n${app_type_condition}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by auth_account, auth_type)\r\n) as  t\r\nwhere 1=1\r\n${auth_account_condition}\r\n${auth_type_condition}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_TOP_COMMUNICATE_ACCOUNT', NULL, 'SELECT\r\n	account,\r\n	sum(num) num\r\nFROM\r\n	(\r\n	SELECT\r\n		1 as flag,\r\n		target_account as account,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account = {imAccount} ${app_type} ${dataType}\r\n		and target_account <> \'\'\r\n	GROUP BY\r\n		target_account\r\nUNION\r\n	SELECT\r\n		2 as flag,\r\n		virtual_account as account,\r\n		sum(behavior_num) num\r\n	FROM\r\n		${dws}.dws_element_behavior_connection_day\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account = {imAccount} ${app_type} ${dataType}\r\n		and virtual_account <> \'\'\r\n	GROUP BY\r\n		virtual_account\r\n) t\r\nGROUP BY\r\n	account\r\nORDER BY\r\n	num DESC');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_IM_LIS_DAY_TREND_ANALYZE', NULL, 'SELECT\r\n	DATE_FORMAT(capture_day, \'yyyyMMdd\') as `time`,\r\n	sum(behavior_num) as num \r\nFROM\r\n	${dws}.dws_element_behavior_connection_day\r\nWHERE\r\n	capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	${app_type} ${lis_target_condition}\r\nGROUP BY\r\n	capture_day\r\nORDER BY\r\n	capture_day');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_TIME_STATISTICS', NULL, 'select CONCAT(FLOOR(capture_hour / ${interval}) * ${interval}, \'-\', (FLOOR(capture_hour / ${interval}) * ${interval} + ${interval})) timeRange,\r\nsum(stay_num) stayCount, count(base_station_no) areaCount, truncate(sum(stay_time)/3600, 1) stayHour\r\nfrom ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n${keyword_condition}\r\ngroup by timeRange\r\n${sort} ${top}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_STAY_RECORD', NULL, 'select phone_stay_id stayId, base_station_no stationNo, min(in_time) inTime, max(out_time) outTime, truncate(sum(stay_time)/3600, 1) stayTime\r\nfrom ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand base_station_no = {stationNo}\r\ngroup by phone_stay_id, base_station_no');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_IMEI_LIST', NULL, 'select device_id imei, min(capture_time) earliestTime, max(capture_time) latestTime from \r\ndev_ads.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = ${arcAccount}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n${keyword_condition}\r\ngroup by device_id ');
