DROP TABLE IF EXISTS dwd_call;
DROP TABLE IF EXISTS dwd_sms;
DROP TABLE IF EXISTS dwd_fax;
DROP TABLE IF EXISTS dwd_lis_remote;
DROP TABLE IF EXISTS dwd_nf_url_day;
DROP TABLE IF EXISTS dwd_nf_other_log_day;
DROP TABLE IF EXISTS dwd_vpn_url;
DROP TABLE IF EXISTS dwd_vpn_other_log;
DROP TABLE IF EXISTS dwd_lis_http;
DROP TABLE IF EXISTS dwd_nf_url;
DROP TABLE IF EXISTS dwd_nf_other_log;


CREATE TABLE IF NOT EXISTS dwd_nf_other_log_day (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '动作类型',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT  COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE  COMMENT '捕获日期',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
  `mac` VARCHAR(64)  REPLACE COMMENT 'MAC',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
  `protocol` INT REPLACE  COMMENT '协议号',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`)
COMMENT 'other_log数据NF明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-37",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_nf_url_day (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024) COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`)
COMMENT 'URL数据NF明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_call (
  `calling_number` VARCHAR(20) COMMENT '主叫号码',
  `called_number` VARCHAR(20) COMMENT '被叫号码',
  `call_type` CHAR(2) COMMENT '通话状态',
  `call_id` BIGINT COMMENT 'call id',
  `calling_atrribution` VARCHAR(128) COMMENT '主叫归属地',
  `called_atrribution` VARCHAR(128) COMMENT '被叫归属地',
  `start_time` BIGINT COMMENT '通话起始时间',
  `end_time` BIGINT COMMENT '通话结束时间',
  `duration` INT COMMENT '通话持续时间',
  `file_path` STRING COMMENT '通话语音文件路径',
  `bs_attribution` VARCHAR(128) COMMENT '主叫位置',
  `called_bs_attribution` VARCHAR(128) COMMENT '被叫位置',
  `calling_imsi` VARCHAR(15) COMMENT '主叫IMSI',
  `called_imsi` VARCHAR(15) COMMENT '被叫IMSI',
  `data_type` INT COMMENT '通信协议',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP',
  `src_ip_area` VARCHAR(128) COMMENT '源IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '目标IP',
  `dst_ip_area` VARCHAR(128) COMMENT '目标IP归属区域',
  `action` VARCHAR(16) COMMENT '动作编码 20 主叫，21 被叫',
  `capture_time` BIGINT NOT NULL COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE NOT NULL COMMENT '捕获日期'
)
DUPLICATE KEY (`calling_number`, `called_number`, `call_type`)
COMMENT '通话数据明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`calling_number`, `called_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_sms (
  `calling_number` VARCHAR(20) COMMENT '主叫号码',
  `called_number` VARCHAR(20) COMMENT '被叫号码',
  `call_type` CHAR(2) COMMENT '发送状态',
  `call_id` BIGINT COMMENT 'call id',
  `calling_atrribution` VARCHAR(128) COMMENT '主叫归属地',
  `called_atrribution` VARCHAR(128) COMMENT '被叫归属地',
  `start_time` BIGINT COMMENT '发送时间',
  `sms_text` STRING COMMENT '短信内容',
  `sms_lang` VARCHAR(16) COMMENT '短信语言',
  `bs_attribution` VARCHAR(128) COMMENT '主叫位置',
  `called_bs_attribution` VARCHAR(128) COMMENT '被叫位置',
  `calling_imsi` VARCHAR(15) COMMENT '主叫IMSI',
  `called_imsi` VARCHAR(15) COMMENT '被叫IMSI',
  `data_type` INT COMMENT '通信协议',
  `action` VARCHAR(16) COMMENT '动作编码 20 主叫，21 被叫',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE COMMENT '捕获日期'  
)
DUPLICATE KEY (`calling_number`, `called_number`, `call_type`)
COMMENT '短信数据明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`calling_number`, `called_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_fax (
  `calling_number` VARCHAR(20) COMMENT '发送人',
  `called_number` VARCHAR(20) COMMENT '接收人',
  `call_id` BIGINT COMMENT 'call id',
  `calling_atrribution` VARCHAR(128) COMMENT '主叫归属地',
  `called_atrribution` VARCHAR(128) COMMENT '被叫归属地',
  `start_time` BIGINT COMMENT '开始时间',
  `end_time` BIGINT COMMENT '结束时间',
  `duration` INT COMMENT '持续时长',
  `file_path` STRING COMMENT '路径',
  `media_type` VARCHAR(32) COMMENT '类型',
  `media_protocol` VARCHAR(32) COMMENT '协议',
  `media_format` VARCHAR(32) COMMENT '格式',
  `sender_nickname` STRING COMMENT '发送人昵称',
  `receiver_nickname` STRING COMMENT '接收人昵称',
  `data_type` INT COMMENT '通信协议',
  `action` VARCHAR(16) COMMENT '动作编码 20 主叫，21 被叫',
  `strsrc_ip` VARCHAR(64) COMMENT '发送人IP',
  `src_ip_area` VARCHAR(128) COMMENT '发送人IP归属区域',
  `strdst_ip` VARCHAR(64) COMMENT '接收人IP',
  `dst_ip_area` VARCHAR(128) COMMENT '接收人IP归属区域',
  `sender_from_url` STRING COMMENT '发送人网络地址',
  `receiver_to_url` STRING COMMENT '接收人网络地址',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE COMMENT '捕获日期'
)
DUPLICATE KEY (`calling_number`, `called_number`)
COMMENT '传真数据明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`calling_number`, `called_number`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_lis_remote (
  `auth_account` VARCHAR(64) COMMENT '认证账号',
  `auth_type` VARCHAR(128) COMMENT '认证类型',
  `ownership_land` VARCHAR(128) COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) COMMENT '认证账号上网地',
  `child_type` CHAR(16) COMMENT '协议代码',
  `login_email` VARCHAR(1024) COMMENT '邮箱',
  `to_ip` VARCHAR(64) COMMENT '被控制ip',
  `to_port` VARCHAR(5) COMMENT '控制通信端口',
  `password` STRING COMMENT '远程控制密码',
  `action` VARCHAR(4) COMMENT '操作类型',
  `op_desc` STRING COMMENT '操作描述',
  `duration` BIGINT COMMENT '记录持续时间（单位 ： s秒）',
  `attach_num` INT COMMENT '附件个数',
  `attach_download_path` STRING COMMENT '附件下载地址',
  `attach_names` STRING COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256) COMMENT '附件列表中对应附件大小',
  `attach_md5s` STRING COMMENT '附件列表中对应附件md5值',
  `username` STRING COMMENT '账号',
  `password_hash_string` STRING COMMENT '密码串',
  `user_nick` STRING COMMENT '昵称',
  `imsi` VARCHAR(15) COMMENT '国际移动用户标识号',
  `imei` VARCHAR(15) COMMENT '设备号',
  `mac` VARCHAR(64) COMMENT '客户端MAC地址',
  `os_name` VARCHAR(16) COMMENT '操作系统名称',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP地址（ipv4或者ipv6）',
  `src_port` INT COMMENT '源端口（ipv4或者ipv6）',
  `src_ip_area` VARCHAR(128) COMMENT '源IP区域标识',
  `strdst_ip` VARCHAR(64) COMMENT '目的IP地址（ipv4或者ipv6）',
  `dst_port` INT COMMENT '目的端口（ipv4或者ipv6）',
  `dst_ip_area` VARCHAR(128) COMMENT '目的IP区域标识',
  `base_station_id` VARCHAR(64) COMMENT '基站号_AP编号',
  `isp_id` VARCHAR(64) COMMENT '运营商和基础网络',
  `icp_provider` VARCHAR(64) COMMENT 'ICP服务提供商',
  `hostname` VARCHAR(64) COMMENT '主机名',
  `tool_type` VARCHAR(64) COMMENT '工具类型',
  `tool_name` VARCHAR(128) COMMENT '工具名称',
  `proxy_type` VARCHAR(64) COMMENT '代理类型',
  `proxy_address` VARCHAR(512) COMMENT '代理工具地址',
  `proxy_provider` VARCHAR(512) COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) COMMENT '代理账号',
  `text` STRING COMMENT '报文正文',
  `file_path` STRING COMMENT '原始报文路径',
  `file_size` INT COMMENT '原始报文大小',
  `capture_time` BIGINT COMMENT '采集时间',
  `uparea_id` INT COMMENT '采集地点',
  `lang_type` VARCHAR(32) COMMENT '语种',
  `favorite_tags` VARCHAR(64) COMMENT '数据标签',
  `capture_day` DATE COMMENT '捕获日期'
)
DUPLICATE KEY (`auth_account`, `auth_type`)
COMMENT 'RemoteCTRL数据LIS明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `strsrc_ip`, `strdst_ip`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_lis_http (
 `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `dst_port` INT  COMMENT '目的端口（ipv4或者ipv6）',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE  COMMENT '捕获日期',
  `attach_names` VARCHAR(1024)  COMMENT '附件列表名称',
  `attach_sizes` VARCHAR(256)  COMMENT '附件列表中对应附件大小',
  `attach_text` VARCHAR(4096)  COMMENT '附件列表中对应附件的内容信息',
  `attach_num` INT  COMMENT '附件个数',
  `text` VARCHAR(4096)  COMMENT '报文正文',
  `keywords` VARCHAR(256)  COMMENT '关键词',
  `subject` VARCHAR(1024)  COMMENT '标题',
  `abstract` VARCHAR(1024)  COMMENT '摘要',
  `lang_type` VARCHAR(32)  COMMENT '语种',
  `username` VARCHAR(512)  COMMENT '用户名',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `capture_hour` TINYINT  COMMENT '捕获时段',
  `ownership_land` VARCHAR(128) REPLACE  COMMENT '认证账号归属地',
  `internet_land` VARCHAR(128) REPLACE COMMENT '认证账号上网地',
  `url` STRING REPLACE COMMENT  'URL',
  `child_type` CHAR(16) REPLACE COMMENT  '协议代码',
  `attach_download_path` VARCHAR(1024)  REPLACE COMMENT '附件下载地址',
  `attach_md5s` VARCHAR(512) REPLACE COMMENT '附件列表中对应附件md5值',
  `password` VARCHAR(512) REPLACE COMMENT '密码',
  `imsi` VARCHAR(15) REPLACE COMMENT '国际移动用户标识号',
  `imei` VARCHAR(15) REPLACE COMMENT '设备号',
  `mac` VARCHAR(64) REPLACE COMMENT '客户端MAC地址',
  `os_name` VARCHAR(16) REPLACE COMMENT '操作系统名称',
  `browse_type` VARCHAR(64) REPLACE COMMENT '浏览器名称',
  `cookie` VARCHAR(4096) REPLACE COMMENT 'Cookie',
  `answer_code` VARCHAR(512) REPLACE COMMENT '操作返回结果',
  `strsrc_ip` VARCHAR(64) REPLACE COMMENT '源IP地址（ipv4或者ipv6）',
  `src_port` INT REPLACE COMMENT '源端口（ipv4或者ipv6）',
  `src_ip_area` VARCHAR(128) REPLACE COMMENT '源IP区域标识',
  `strdst_ip` VARCHAR(64) REPLACE COMMENT '目的IP地址（ipv4或者ipv6）',
  `dst_ip_area` VARCHAR(128) REPLACE COMMENT '目的IP区域标识',
  `base_station_id` VARCHAR(64) REPLACE COMMENT '基站号_AP编号',
  `isp_id` VARCHAR(64) REPLACE COMMENT '运营商和基础网络',
  `host` VARCHAR(64) REPLACE COMMENT '主机名',
  `tool_type` VARCHAR(64) REPLACE COMMENT '工具类型',
  `tool_name` VARCHAR(128) REPLACE COMMENT '工具名称',
  `proxy_type` VARCHAR(64) REPLACE COMMENT '代理类型',
  `proxy_address` STRING REPLACE COMMENT '代理工具地址',
  `proxy_provider` VARCHAR(512) REPLACE COMMENT '代理提供商',
  `proxy_account` VARCHAR(512) REPLACE COMMENT '代理账号',
  `file_path` STRING REPLACE COMMENT '原始报文路径',
  `file_size` INT REPLACE COMMENT '原始报文大小',
  `earliest_relation_time` BIGINT REPLACE COMMENT '最早关联时间',
  `latest_relation_time` BIGINT REPLACE COMMENT '最后关联时间',
  `favorite_tags` VARCHAR(64) REPLACE COMMENT '数据标签',
  `behavior_num` BIGINT sum  COMMENT '行为总数'
)
AGGREGATE KEY (auth_account, auth_type, dst_port, uparea_id, capture_day, attach_names, attach_sizes, attach_text, attach_num, text, keywords, subject, abstract, lang_type, username, domain,capture_hour)
COMMENT 'HTTP 数据LIS 明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`, `auth_type`, `dst_port`, `uparea_id`, capture_day, attach_names) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "30G",
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_nf_url (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024) COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, capture_hour)
COMMENT 'URL数据NF明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "estimate_partition_size" = "100G",
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_vpn_url (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024) COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '访问控制',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT COMMENT '采集地点',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_hour` TINYINT COMMENT '捕获小时',
  `capture_minute` TINYINT COMMENT '捕获分钟',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, `capture_hour`, `capture_minute`)
COMMENT 'URL数据NF明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_nf_other_log (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '动作类型',
  `url` VARCHAR(512)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT  COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE  COMMENT '捕获日期',
  `capture_hour` TINYINT NOT NULL COMMENT '捕获时段',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
  `mac` VARCHAR(64)  REPLACE COMMENT 'MAC',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
  `protocol` INT REPLACE  COMMENT '协议号',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, capture_hour)
COMMENT 'other_log数据NF明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
     "estimate_partition_size" = "80G",
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-37",
    "dynamic_partition.end" = "3"
);

CREATE TABLE IF NOT EXISTS dwd_vpn_other_log (
  `auth_account` VARCHAR(64)  COMMENT '认证账号',
  `auth_type` VARCHAR(128)  COMMENT '认证类型',
  `user_name` VARCHAR(128)  COMMENT '用户名',
  `user_group` VARCHAR(32) COMMENT '用户组',
  `app_type` VARCHAR(128)  COMMENT '应用类型',
  `app_name` VARCHAR(128)  COMMENT '应用名称',
  `domain` VARCHAR(1024)  COMMENT '域名',
  `data_type` INT COMMENT '协议类型',
  `net_action` VARCHAR(16) COMMENT '动作类型',
  `url` VARCHAR(65533)  COMMENT '访问URL',
  `strsrc_ip` VARCHAR(64)  COMMENT '源IP',
  `src_port` INT  COMMENT '源端口',
  `cell_id` VARCHAR(64) COMMENT '基站编号',
  `uparea_id` INT  COMMENT '采集地点',
  `capture_day` DATE  COMMENT '捕获日期',
  `capture_hour` TINYINT COMMENT '捕获小时',
  `capture_minute` TINYINT COMMENT '捕获分钟',
  `imsi` VARCHAR(16)  REPLACE COMMENT 'IMSI',
 `imei` VARCHAR(16)  REPLACE COMMENT 'IMEI',
 `mac` VARCHAR(64)  REPLACE COMMENT 'MAC',
 `terminal_type` VARCHAR(64)  REPLACE COMMENT '终端类型',
  `protocol` INT REPLACE  COMMENT '协议号',
 `strdst_ip` VARCHAR(64)  REPLACE  COMMENT '目的IP',
 `dst_port` INT REPLACE  COMMENT '目的端口',
  `behavior_num` BIGINT SUM COMMENT '行为次数',
  `earliest_relation_time` BIGINT MIN COMMENT '最早关联时间',
  `latest_relation_time` BIGINT MAX COMMENT '最后关联时间'
)
AGGREGATE KEY (`auth_account`, `auth_type`, `user_name`,`user_group`, `app_type`, `app_name`, `domain`, `data_type`, `net_action`, `url`, `strsrc_ip`, `src_port`, `cell_id`, `uparea_id`, `capture_day`, `capture_hour`, `capture_minute`)
COMMENT 'other_log数据NF明细表'
PARTITION BY RANGE (`capture_day`) ()
DISTRIBUTED BY HASH(`auth_account`,`auth_type`, `user_name`, `app_name`,`domain`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-372",
    "dynamic_partition.end" = "3"
);
