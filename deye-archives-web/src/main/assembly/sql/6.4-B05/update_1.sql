-- 删除 code
delete from tb_arc_service_sql where `code` in ('DOMAIN_ONE_WEEK_ATTACHMENT_NUMBER_PERFORM_BATCH','APP_ONE_WEEK_ATTACHMENT_NUMBER_PERFORM_BATCH','RADIUS_TOP_ATTACHMENT_SUFFIX','RADIUS_TOP_ATTACHMENT_TYPE','RADIUS_GET_CONTACT_INFO');

-- 变更 code
delete from tb_arc_service_sql where `code` in ('RADIUS_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER_BATCH','RADIUS_TOP_VIRTUAL_ACCOUNT','RADIUS_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE','RADIUS_GET_VIRTUAL_ACCOUNT_INFO','RADIUS_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER','PHONE_TOP_PHONE','PHONE_TOP_VIRTUAL_ACCOUNT','PHONE_VIRTUAL_ACCOUNT_TAB','COMMON_APP_TYPE_LIST','PHONE_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER','RADIUS_VIRTUAL_ACCOUNT_TAB','PHONE_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE','QUERY_ATTACHMENT_INFO_OVERVIEW','EMAIL_RELATION_EXTENSION','EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER','QUERY_ATTACHMENT_INFO','PHONE_RELATION_EXTENSION','PHONE_AREA_HOT_STAY_RECORD','QUERY_ATTACHMENT_INFO_FILE_PATH');


INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('RADIUS_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER_BATCH', NULL, 'SELECT\r\n	auth_account,\r\n	auth_account_type,\r\n	SUM(num) as nums\r\nFROM\r\n(	\r\nSELECT\r\n	auth_account,\r\n	auth_account_type,\r\n	norm_data_type,\r\n	virtual_account,\r\n	app_type,\r\n	1 num\r\nFROM\r\n	${dws}.dws_element_relation_auth_account_virtual_account_detail\r\nWHERE\r\n	capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and auth_account IN (${auth_account})\r\n	and auth_account_type = {auth_account_type}\r\n	and virtual_account <> \'\'\r\nGROUP BY\r\n	auth_account,\r\n	auth_account_type,\r\n	norm_data_type,\r\n	virtual_account,\r\n	app_type	\r\n) as t\r\nGROUP BY\r\n	auth_account,\r\n	auth_account_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('RADIUS_TOP_VIRTUAL_ACCOUNT', NULL, 'SELECT\r\n	virtual_account as virtualAccount,\r\n	norm_data_type as dataType,\r\n	app_type appType,\r\n	sum(behavior_num) num\r\nFROM\r\n	${dws}.dws_element_relation_auth_account_virtual_account_detail\r\nWHERE\r\n	capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and auth_account = {auth_account}\r\n	and auth_type = {auth_account_type}\r\n	and virtual_account <> \'\'\r\nGROUP BY\r\n	virtual_account,\r\n	norm_data_type,\r\n	app_type\r\nORDER BY\r\n	num DESC');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('RADIUS_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE', NULL, 'select\r\n	count(1) as `count`,\r\n	dataType\r\nfrom\r\n	(\r\n	select\r\n		virtual_account as virtualAccount,\r\n		app_type as appType,\r\n		norm_data_type as dataType\r\n	from\r\n		${dws}.dws_element_relation_auth_account_virtual_account_detail\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		AND auth_account = {auth_account}\r\n		and auth_type = {auth_account_type} ${key_word}\r\n	group by\r\n		virtual_account,\r\n		app_type,\r\n		norm_data_type ) as t\r\ngroup by\r\n	dataType\r\norder by `count` desc,dataType desc');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('RADIUS_GET_VIRTUAL_ACCOUNT_INFO', NULL, 'select\r\n	auth_account,\r\n	virtual_account virtualAccount,\r\n	norm_data_type dataType,\r\n	app_type appType,\r\n	Min(earliest_relation_time) earliest,\r\n	Max(latest_relation_time) latestTime,\r\n	count(DISTINCT(capture_day)) activeDays\r\nfrom\r\n	${dws}.dws_element_relation_auth_account_virtual_account_detail\r\nwhere\r\n	auth_account = {auth_account}\r\n	and auth_account_type = {auth_account_type}\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day} ${data_type} ${keyWord}\r\ngroup by\r\n	auth_account,\r\n	virtual_account,\r\n	norm_data_type,\r\n	app_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('QUERY_ATTACHMENT_INFO', NULL, 'SELECT\r\n	max(attach_path) attachPath,\r\n	norm_data_type dataType,\r\n	attach_md5 attachMd5,\r\n	max(attach_suffix) AS attachType,\r\n	max(attach_name) AS attachName,\r\n	max(attach_size) AS attachSize,\r\n	array_join(collect_set(CASE \r\n                WHEN virtual_account = \'\' THEN NULL \r\n                ELSE virtual_account \r\n            END), \',\') AS virtualAccount,\r\n	array_join(collect_set(CASE \r\n                WHEN auth_account = \'\' THEN NULL \r\n                ELSE auth_account \r\n            END), \',\') AS authAccount,\r\n	MIN(earliest_relation_time ) AS firstAppearTime,\r\n	MAX(latest_relation_time )AS lastAppearTime,\r\n	SUM(behavior_num) AS appearTimes\r\nFROM\r\n	${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n	${account_type_condition}  in (${arc_account})\r\n	and attach_path <> \'\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}  ${dataType}\r\n	${fileType} \r\n	${keyword}\r\nGROUP BY\r\n	attach_md5,\r\n	norm_data_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('RADIUS_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER', NULL, 'SELECT\r\n	arcName,\r\n	count(1) as nums\r\nfrom\r\n	(\r\n	select\r\n		auth_account as arcName,\r\n		virtual_account,\r\n		norm_data_type,\r\n		app_type\r\n	from\r\n		${dws}.dws_element_relation_auth_account_virtual_account_detail\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account <> \'\'\r\n		and auth_account in (${authAccount}) AND auth_type = {authAccountType} group by auth_account, virtual_account, norm_data_type, app_type\r\n	) tmp group by arcName;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_TOP_PHONE', NULL, 'SELECT \r\n	number,\r\n	sum(num) as behaviorNum\r\nfrom\r\n	(\r\n	select\r\n		1 as flag,\r\n		src_number as number,\r\n		sum(behavior_num) as num\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and dst_number = {auth_account}\r\n	group by\r\n		src_number\r\nUNION\r\n	select\r\n		2 as flag,\r\n		dst_number as number,\r\n		sum(behavior_num) as num\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and src_number = {auth_account}\r\n	group by\r\n		dst_number \r\n) t\r\nwhere number <> \'\'\r\ngroup by number order by behaviorNum desc\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_TOP_VIRTUAL_ACCOUNT', NULL, 'SELECT\r\n	virtual_account as virtualAccount,\r\n	norm_data_type as dataType,\r\n	app_type appType,\r\n	sum(behavior_num) num\r\nFROM\r\n	${dws}.dws_element_relation_phone_virtual_account_detail\r\nWHERE\r\n	capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and phone_number = {auth_account}\r\n	and virtual_account <> \'\'\r\nGROUP BY\r\n	virtual_account,\r\n	norm_data_type,\r\n	app_type\r\nORDER BY\r\n	num DESC');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'QUERY_ATTACHMENT_INFO_OVERVIEW', NULL, 'SELECT\r\n	max(attach_path) attachPath,\r\n	max(attach_name) AS attachName,\r\n	max(attach_size) AS attachSize,\r\n	max(attach_type) AS attachType,\r\n	max(latest_relation_time) AS latestRelationTime,\r\n	attach_md5 AS attachMd5,\r\n	norm_data_type AS dataType\r\nFROM\r\n	${dws}.dws_element_behavior_attachment_day\r\nWHERE\r\n	${account_type_condition}  in (${arc_account})\r\n	and attach_path <> \'\'\r\n	and attach_type <> \'OTHER\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day} ${dataType}\r\nGROUP BY\r\n	attach_md5,norm_data_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_VIRTUAL_ACCOUNT_TAB', NULL, 'select virtual_account virtualAccount, `any`(phone_number) authAccount, norm_data_type dataType, app_type appType, `max`(latest_relation_time) latestTime,`min`(earliest_relation_time) earliest, count(DISTINCT capture_day) activeDays, sum(behavior_num) behaviorNum\r\nfrom ${dws}.dws_element_relation_phone_virtual_account_detail\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= \'${start_day}\'\r\nand capture_day <= \'${end_day}\'\r\n${keyword_condition}\r\n${data_type_condition}\r\ngroup by virtual_account, app_type, norm_data_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_APP_TYPE_LIST', NULL, 'select\r\n	distinct app_type as appType\r\nfrom\r\n	ads_archive_app_statistics where app_type <> \'IM\'\r\norder by\r\n	app_type ');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'QUERY_ATTACHMENT_INFO_FILE_PATH', NULL, 'SELECT\r\n	file_path AS filePath, ${account_field} AS account\r\nfrom\r\n	${dws}.dws_element_behavior_attachment_day\r\nwhere\r\n	${account_type_condition}  in ({arc_account})\r\n	and attach_md5 = {attach_md5}\r\n	and norm_data_type in ${norm_data_type}\r\n	and attach_path <> \'\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_ONE_WEEK_VIRTUAL_ACCOUNT_NUMBER', NULL, 'SELECT\r\n	arcName,\r\n	count(1) as nums\r\nfrom\r\n	(\r\n	select\r\n		phone_number as arcName,\r\n		virtual_account,\r\n		norm_data_type data_type,\r\n	  app_type\r\n	from\r\n		${dws}.dws_element_relation_phone_virtual_account_detail\r\n	WHERE\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account <> \'\'\r\n		and phone_number in (${phone}) group by phone_number, virtual_account, norm_data_type, app_type\r\n	) tmp group by arcName;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_RELATION_EXTENSION', NULL, 'select `number`,`total`,`call`,fax,sms from (\r\nselect connect_number `number`, sum(behaviorNum) `total`,\r\nsum(if(callTag = 2, behaviorNum, 0)) `call`,\r\nsum(if(callTag = 1, behaviorNum, 0)) fax,\r\nsum(if(callTag = 3, behaviorNum, 0)) sms\r\nfrom (\r\nselect \r\ncase \r\nwhen src_number = \'${arcAccount}\' then dst_number  \r\nwhen dst_number = \'${arcAccount}\' then src_number  \r\nend connect_number,\r\ncall_tag callTag,\r\nsum(behavior_num) behaviorNum\r\nfrom ${dws}.dws_element_behavior_call_day \r\nwhere (src_number = \'${arcAccount}\' or dst_number = \'${arcAccount}\') and capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\n${call_tag_condition}\r\nand spam_flag=0\r\ngroup by connect_number,call_tag\r\n) as t group by number\r\n) as t1  where `number` <> \'\' ${minLinkCountCondition}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_RELATION_EXTENSION', NULL, 'select src_account,dst_account,total from (\r\nSELECT\r\ncase \r\nwhen virtual_account in (${relateVirtualAccounts}) then virtual_account  \r\nwhen target_account in (${relateVirtualAccounts}) then target_account  \r\nend src_account,\r\ncase \r\nwhen virtual_account in (${relateVirtualAccounts}) then target_account \r\nwhen target_account in (${relateVirtualAccounts}) then virtual_account  \r\nend dst_account,\r\nsum(behavior_num) total\r\nFROM ${dws}.dws_element_behavior_connection_day \r\nwhere \r\n(virtual_account in (${relateVirtualAccounts}) or target_account in (${relateVirtualAccounts}) )\r\n${virtual_app_type_condition}\r\nand data_type = \'${data_type}\'\r\nand capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\nand spam_flag=0\r\ngroup by src_account,dst_account\r\n) as t where dst_account <> \'\' ${minLinkCountCondition}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'RADIUS_VIRTUAL_ACCOUNT_TAB', NULL, 'select virtual_account virtualAccount, `any`(auth_account) authAccount, norm_data_type dataType, app_type appType, `max`(latest_relation_time) latestTime,`min`(earliest_relation_time) earliest, count(DISTINCT capture_day) activeDays, sum(behavior_num) behaviorNum\r\nfrom ${dws}.dws_element_relation_auth_account_virtual_account_detail\r\nwhere auth_account = \'${arcAccount}\'\r\nand capture_day >= \'${start_day}\'\r\nand capture_day <= \'${end_day}\'\r\nand auth_type = \'${arcAccountType}\'\r\n${keyword_condition}\r\n${data_type_condition}\r\ngroup by virtual_account, app_type, norm_data_type');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_COUNT_VIRTUAL_ACCOUNT_BY_DATA_TYPE', NULL, 'select\r\n	count(1) as `count`,\r\n	dataType\r\nfrom\r\n	(\r\n	select\r\n		virtual_account as virtualAccount,\r\n		app_type appType,\r\n		norm_data_type as dataType\r\n	from\r\n		${dws}.dws_element_relation_phone_virtual_account_detail\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		AND phone_number = {auth_account} ${key_word}\r\n	group by\r\n		virtual_account,\r\n		app_type,\r\n		norm_data_type ) as t\r\ngroup by\r\n	dataType\r\norder by `count` desc,dataType desc');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_COMMUNICATE_MAIL_NUMBER', NULL, 'SELECT \r\n	arcName,\r\n	count(distinct account) as nums\r\nfrom\r\n	(\r\n	select\r\n		1 as flag,\r\n		target_account as arcName,\r\n		virtual_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account in (${email}) ${dataType}\r\nUNION\r\n	select\r\n		2 as flag,\r\n		virtual_account as arcName,\r\n		target_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${email}) ${dataType}\r\n) t\r\nwhere account <> \'\'\r\ngroup by\r\n	arcName;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_STAY_RECORD', NULL, 'select phone_stay_id stayId, base_station_no stationNo, min(in_time) inTime, max(out_time) outTime, truncate(sum(stay_time)/3600, 3) stayTime\r\nfrom ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= ${start_time}\r\nand capture_time <= ${end_time}\r\nand base_station_no = {stationNo}\r\ngroup by phone_stay_id, base_station_no');



UPDATE `tb_arc_drill_down_field_config` SET `desc_zh` = '采集时间' WHERE `data_type` = 'http' and field = 'latest_relation_time';
UPDATE `tb_arc_drill_down_field_config` SET `desc_zh` = '采集时间' WHERE `data_type` = 'ftp' and field = 'capture_time';
UPDATE `tb_arc_drill_down_field_config` SET `desc_zh` = '文件个数' WHERE  field = 'attach_num';

