DROP TABLE IF EXISTS dws.dws_element_nickname_detail;

CREATE TABLE IF NOT EXISTS dws.dws_element_nickname_detail (
  `virtual_account` VARCHAR(512) NOT NULL COMMENT '虚拟账号',
  `data_type` INT NOT NULL COMMENT '协议类型',
  `alias` VARCHAR(1024) COMMENT '别名',
  `capture_day` DATE NOT NULL COMMENT '捕获日期',
  `capture_month` INT NOT NULL COMMENT '捕获月份',
  `earliest_relation_time` BIGINT min NOT NULL COMMENT '最早关联时间',
  `latest_relation_time` BIGINT max NOT NULL COMMENT '最后关联时间',
  `behavior_num` BIGINT sum NOT NULL COMMENT '行为次数',
  `insert_time` BIGINT REPLACE NOT NULL COMMENT '数据插入时间'
)
AGGREGATE KEY (`virtual_account`,`data_type`,`alias`, `capture_day`, `capture_month`)
COMMENT 'Email档案行为明细小时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`virtual_account`,`data_type`,`alias`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP TABLE IF EXISTS ads_archive.ads_archive_tag_statistics_day;

CREATE TABLE IF NOT EXISTS ads_archive.ads_archive_tag_statistics_day (
    archive_name VARCHAR(1024) NOT NULL COMMENT '档案名称',
    archive_type TINYINT NOT NULL COMMENT '档案类型',
    tag_name VARCHAR(128) NOT NULL COMMENT '标签名称',
    tag_value VARCHAR(128) NOT NULL COMMENT '标签值',
    behavior_num BIGINT  NOT NULL COMMENT '行为次数',
    earliest_relation_time BIGINT NOT NULL COMMENT '最早关联时间',
    latest_relation_time BIGINT NOT NULL COMMENT '最晚关联时间',
    capture_day DATE NOT NULL COMMENT '捕获日期',
    capture_month INT NOT NULL COMMENT '捕获月份',
    insert_time BIGINT NOT NULL COMMENT '插入时间'
) ENGINE=OLAP
DUPLICATE KEY (`archive_name`)
COMMENT '档案标签统计日表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))()
DISTRIBUTED BY HASH(`archive_name`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3"
);

DROP MATERIALIZED VIEW IF EXISTS ads_archive_tag_statistics_month_agg_view_month ON ads_archive_tag_statistics_day;

CREATE MATERIALIZED VIEW ads_archive_tag_statistics_month_agg_view_month
AS
SELECT
	`archive_name`,
	`archive_type`,
	`tag_name`,
	`tag_value`,
	`capture_month`,
  	SUM(`behavior_num`) AS `behavior_num`,
	MIN(`earliest_relation_time`) AS `earliest_relation_time`,
	MAX(`latest_relation_time`) AS `latest_relation_time`
FROM
	ads_archive.ads_archive_tag_statistics_day
GROUP BY `archive_name`, `archive_type`, `tag_name`, `tag_value`, `capture_month`;








