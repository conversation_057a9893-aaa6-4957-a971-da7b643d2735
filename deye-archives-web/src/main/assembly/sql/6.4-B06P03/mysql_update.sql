-- 变更 code
delete from tb_arc_service_sql where `code` in ('COMMON_GET_TAG_TOP_LIST','COMMON_GET_TAG_LIST_DAY','COMMON_GET_TAG_LIST_MONTH','PHONE_REAL_TIME_TRAJECTORY','IM_LAST_RELATIOJN_NICKNAME_CAPTUREDAY','IM_LAST_RELATIOJN_NICKNAME','EMAIL_NICKNAME_RECORD','EMAIL_ONE_WEEK_NICKNAME_NUMBER');

INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_GET_TAG_TOP_LIST', NULL, 'SELECT\r\n	GROUP_CONCAT(`tag_name` ORDER BY behavior_num DESC , \',\') tagName,\r\n	tag_value tagValue,\r\n	SUM(behavior_num) num\r\nFROM\r\n	${ads}.ads_archive_tag_statistics_day\r\nWHERE\r\n	capture_day BETWEEN {start_day}\r\nAND {end_day}\r\nAND archive_name = {arc_name}\r\nAND archive_type = ${arc_type}\r\nGROUP BY\r\n	tag_value ORDER BY num DESC\r\nLIMIT ${top_num};');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_GET_TAG_LIST_DAY', NULL, 'SELECT\r\n	GROUP_CONCAT(DISTINCT tag_name , \',\') tagName,\r\n	tag_value tagValue,\r\n	SUM(behavior_num) behaviorNum,\r\n  min(earliest_relation_time) earliestTime,\r\n  max(latest_relation_time) latestTime\r\nFROM\r\n	${ads}.ads_archive_tag_statistics_day\r\nWHERE\r\n	capture_day BETWEEN {start_day}\r\nAND {end_day}\r\nAND archive_name = {arc_name}\r\nAND archive_type = ${arc_type}\r\n ${keyword_condition}\r\nGROUP BY\r\n	tag_value');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_GET_TAG_LIST_MONTH', NULL, 'SELECT\r\n	GROUP_CONCAT(`tag_name` ORDER BY behavior_num DESC , \',\') tagName,\r\n	tag_value tagValue,\r\n	SUM(behavior_num) behaviorNum,\r\n  min(earliest_relation_time) earliestTime,\r\n  max(latest_relation_time) latestTime\r\nFROM\r\n	${ads}.ads_archive_tag_statistics_day\r\nWHERE\r\n	capture_month >= ${start_month}\r\nAND capture_month <= ${end_month}\r\nAND archive_name = {arc_name}\r\nAND archive_type = ${arc_type}\r\n ${keyword_condition}\r\nGROUP BY\r\n	tag_value');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_REAL_TIME_TRAJECTORY', NULL, 'SELECT \r\nstation_no baseStationNo\r\n,network_type netWorkType\r\n,station_longitude lng\r\n,station_latitude lat\r\n,address stationAddess\r\n,in_time earliestRelationTime\r\n,out_time latestRelationTime\r\n,(case when out_time > in_time then out_time - in_time else 0 end)/(1000) onlineHour\r\nfrom (\r\nselect lead_tmp.phone_number\r\n,device_id\r\n,lead_tmp.session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,lead_tmp.capture_time\r\n,last_station\r\n,rn\r\n,next_time\r\n,lead_tmp.capture_time as in_time\r\n,case \r\n	  when lead_tmp.session_id = t2.session_id and action <> \'stop\' and lead_tmp.capture_time = t2.capture_time then 999999999\r\n	  when rn = 1 and station_no = last_station then 0\r\n      when rn = 1 and station_no <> last_station then lead_tmp.capture_time\r\n      when next_time = 0 then lead_tmp.capture_time\r\n else next_time end as out_time\r\nfrom (\r\nselect phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,last_station\r\n,rn\r\n,lead(capture_time, 1, 0) over(partition by phone_number, session_id order by capture_time asc) as next_time\r\nfrom (\r\nSELECT phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,lag(station_no, 1, \'\') over(partition by phone_number, session_id order by capture_time asc) as last_station\r\n,row_number() over(partition by phone_number, session_id order by capture_time desc) as rn\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n) as t1\r\nwhere rn = 1 or last_station <> station_no) lead_tmp\r\nleft join (\r\nSELECT \r\nphone_number,\r\nsession_id,\r\ncapture_time\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\norder by capture_time desc limit 1\r\n) t2\r\non t2.phone_number = lead_tmp.phone_number\r\n) lead_tmp2\r\nwhere out_time > 0\r\n${station_condition}\r\n${keyword_condition}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_NICKNAME_RECORD', NULL, 'select alias nickname, min(earliest_relation_time) earliestTime, max(latest_relation_time) latestTime, sum(behavior_num) num\r\nfrom ${dws}.dws_element_nickname_detail\r\nwhere virtual_account = {virtual_account}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand data_type = 101\r\ngroup by alias');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_ONE_WEEK_NICKNAME_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	count(distinct alias) as nums\r\nfrom\r\n		${dws}.dws_element_nickname_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${email})\r\n	and data_type = 101\r\ngroup by\r\n		virtual_account;');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'IM_LAST_RELATIOJN_NICKNAME_CAPTUREDAY', NULL, 'select\r\n	capture_day\r\nfrom\r\n	dws.dws_element_nickname_detail\r\nwhere\r\n	virtual_account = {arc_name}\r\n	and data_type = ${data_type}\r\n  and alias <> \'\'\r\ngroup by\r\n	capture_day\r\nORDER BY\r\n	capture_day DESC\r\nLIMIT 1;');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'IM_LAST_RELATIOJN_NICKNAME', NULL, 'select\r\n	alias\r\nfrom\r\n	dws.dws_element_nickname_detail\r\nwhere\r\n	virtual_account = {arc_name}\r\n	and data_type = ${data_type}\r\n  and capture_day = {capture_day}\r\n  and alias <> \'\'\r\nORDER BY\r\n	latest_relation_time DESC\r\nLIMIT 1;');


-- 更新字典表
UPDATE `tb_arc_dict` SET `dict_name_en`='Email' WHERE (`dict_name_en`='E-mail' and `dict_key`='norm_data_type');
UPDATE `tb_arc_dict` SET `dict_name_en`='AppCall' WHERE (`dict_name_en`='Internet phone' and `dict_key`='norm_data_type');
UPDATE `tb_arc_dict` SET `dict_name_en`='Shopping' WHERE (`dict_name_en`='Online shopping' and `dict_key`='norm_data_type');
UPDATE `tb_arc_dict` SET `dict_name_en`='NF_URL' WHERE (`dict_name_en`='URL' and `dict_key`='norm_data_type');
UPDATE `tb_arc_dict` SET `dict_name_en`='NF_other_log' WHERE (`dict_name_en`='OTHER_lOG' and `dict_key`='norm_data_type');
UPDATE `tb_arc_dict` SET `dict_name_zh`=`dict_name_en`, `dict_name_fr`=`dict_name_en` WHERE (`dict_key`='norm_data_type');