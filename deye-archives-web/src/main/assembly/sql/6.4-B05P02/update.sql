delete from tb_arc_drill_down_field_config where data_type = 'mobile_radius' and field = 'network_type';

INSERT INTO `tb_arc_drill_down_field_config`(`data_type`, `field`, `desc_zh`, `desc_fr`, `sort`, `is_core`, `is_hide`, `is_search`, `is_attach_source`, `create_time`, `modify_time`, `sortable`) VALUES ('mobile_radius', 'network_type', '网络类型', 'type de réseau', 130, 1, 0, 1, 0, 1712074522, 1712074522, '1');


delete from tb_arc_service_sql where code in ('EMAIL_ONE_WEEK_ACTIVE_AREA_NUMBER','IM_ONE_WEEK_ACTIVE_AREA_NUMBER','PHONE_IMEI_LIST','PHONE_FREQUENT_ACTIVE_AREA','PHONE_REAL_TIME_TRAJECTORY','PHONE_AREA_HOT_STAY_RECORD');

INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_FREQUENT_ACTIVE_AREA', NULL, 'SELECT station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 3) onlineHour,\r\n`any`(address) stationAddess, `any`(station_longitude) lng, `any`(station_latitude) lat\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= \'${start_day}\'\r\nand capture_day <= \'${end_day}\'\r\ngroup by baseStationNo order by onlineHour desc');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_REAL_TIME_TRAJECTORY', NULL, 'SELECT \r\nstation_no baseStationNo\r\n,network_type netWorkType\r\n,station_longitude lng\r\n,station_latitude lat\r\n,address stationAddess\r\n,in_time earliestRelationTime\r\n,out_time latestRelationTime\r\n,truncate((case when out_time > in_time then out_time - in_time else 0 end)/(3600000), 3) onlineHour\r\nfrom (\r\nselect lead_tmp.phone_number\r\n,device_id\r\n,lead_tmp.session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,last_station\r\n,rn\r\n,next_time\r\n,capture_time as in_time\r\n,case \r\n	  when lead_tmp.session_id = t2.session_id and action <> \'stop\' then *********\r\n	  when rn = 1 and station_no = last_station then 0\r\n      when rn = 1 and station_no <> last_station then capture_time\r\n      when next_time = 0 then capture_time\r\n else next_time end as out_time\r\nfrom (\r\nselect phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,last_station\r\n,rn\r\n,lead(capture_time, 1, 0) over(partition by phone_number, session_id order by capture_time asc) as next_time\r\nfrom (\r\nSELECT phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,lag(station_no, 1, \'\') over(partition by phone_number, session_id order by capture_time asc) as last_station\r\n,row_number() over(partition by phone_number, session_id order by capture_time desc) as rn\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n ${station_condition}\r\n ${keyword_condition}\r\n) as t1\r\nwhere rn = 1 or last_station <> station_no) lead_tmp\r\nleft join (\r\nSELECT \r\nphone_number,\r\nsession_id\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n ${station_condition}\r\n ${keyword_condition}\r\norder by capture_time desc limit 1\r\n) t2\r\non t2.phone_number = lead_tmp.phone_number\r\n) lead_tmp2\r\nwhere out_time > 0\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('EMAIL_ONE_WEEK_ACTIVE_AREA_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	count(distinct active_country) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_email_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and virtual_account in (${email})\r\n	and active_country <> \'\'\r\ngroup by\r\n		virtual_account;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('IM_ONE_WEEK_ACTIVE_AREA_NUMBER', NULL, 'SELECT \r\n	virtual_account as arcName,\r\n	virtual_app_type as appType,\r\n	count(distinct active_country) as nums\r\nfrom\r\n		${dws}.dws_element_behavior_im_detail\r\nwhere\r\n		capture_day >= {start_day}\r\n	and capture_day <= {end_day}\r\n	and active_country <> \'\'\r\n	and virtual_account in (${imAccount}) ${app_type}\r\ngroup by\r\n		virtual_account, virtual_app_type;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_AREA_HOT_STAY_RECORD', NULL, 'select station_no stationNo, in_time inTime, out_time outTime, truncate(stay_time/3600, 3) stayTime\r\nfrom ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= ${start_time}\r\nand capture_time <= ${end_time}\r\nand station_no = {stationNo}\r\n ${hour_condition}\r\n');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_IMEI_LIST', NULL, 'select device_id imei, min(capture_time) earliestTime, max(capture_time) latestTime from \r\n${ads}.ads_archive_phone_location_his\r\nwhere phone_number = {arcAccount}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n${keyword_condition}\r\ngroup by device_id ');
