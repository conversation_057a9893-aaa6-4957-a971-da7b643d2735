delete from tb_arc_service_sql where code in  ('PHONE_CONNECT_AREA_BELONG','PHONE_REGULAR_TRAJECTORY','PHONE_ONE_WEEK_COMMUNICATION_AREA_NUMBER','PHONE_FREQUENT_ACTIVE_AREA','PHONE_REAL_TIME_TRAJECTORY','IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER','PHONE_AREA_HOT_STAY_RECORD','PHONE_AREA_HOT_TIME_STATISTICS','QUERY_ATTACHMENT_INFO_FILE_PATH');

INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_CONNECT_AREA_BELONG', NULL, 'select `any`(src_attribution) src_attribution, connected_attribution, SUM(behavior_num) total from (\r\nselect \r\ncase \r\nwhen src_number = \'${arcAccount}\' then calling_attribution  \r\nwhen dst_number = \'${arcAccount}\' then called_attribution  \r\nend src_attribution,\r\ncase \r\nwhen src_number = \'${arcAccount}\' then called_attribution  \r\nwhen dst_number = \'${arcAccount}\' then calling_attribution  \r\nend connected_attribution,\r\nbehavior_num\r\nfrom ${dws}.dws_element_behavior_call_day \r\nwhere (src_number = \'${arcAccount}\' or dst_number = \'${arcAccount}\') and capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\n) as t\r\nwhere connected_attribution <> \'\'\r\ngroup by connected_attribution');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_FREQUENT_ACTIVE_AREA', NULL, 'SELECT base_station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 3) onlineHour,\r\n`any`(address) stationAddess, `any`(station_longitude) lng, `any`(station_latitude) lat\r\nFROM ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= \'${start_day}\'\r\nand capture_day <= \'${end_day}\'\r\ngroup by baseStationNo order by onlineHour desc');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_REAL_TIME_TRAJECTORY', NULL, 'SELECT base_station_no baseStationNo, `any`(network_type) netWorkType, truncate(sum(stay_time)/3600, 3) onlineHour,\r\n`any`(address) stationAddess, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum, min(in_time) earliestRelationTime, max(out_time) latestRelationTime\r\nFROM ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n${hour_condition} ${station_condition}\r\n${keyword_condition}\r\ngroup by baseStationNo');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_REGULAR_TRAJECTORY', NULL, 'select `any`(srcBaseStationNo) srcBaseStationNo, `any`(srcLng) srcLng,`any`(srcLat) srcLat,`any`(srcNetWorkType) srcNetWorkType,`any`(dstBaseStationNo) dstBaseStationNo,`any`(dstLng) dstLng,`any`(dstLat) dstLat,`any`(dstNetWorkType) dstNetWorkType,`any`(srcStationAddess) srcStationAddess, `any`(dstStationAddess) dstStationAddess,\r\nsum(trajectoryCount) trajectoryCount, map_agg(capture_hour, trajectoryCount) trajectoryPeriod, pathId from (\r\nSELECT  `any`(src_station_no) srcBaseStationNo, `any`(src_station_longitude) srcLng, `any`(src_station_latitude) srcLat, `any`(src_network_type) srcNetWorkType,`any`(dst_station_no) dstBaseStationNo, `any`(dst_station_longitude) dstLng, `any`(dst_station_latitude) dstLat,`any`(dst_network_type) dstNetWorkType,\r\n`any`(src_address) srcStationAddess, `any`(dst_address) dstStationAddess,count() trajectoryCount,capture_hour, \r\n(MURMUR_HASH3_32(src_station_no) + MURMUR_HASH3_32(dst_station_no)) pathId\r\nFROM ${ads}.ads_archive_phone_base_station_path_detail\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_month >= ${start_month}\r\nand capture_month <= ${end_month}\r\n${keyword_condition}\r\ngroup by pathId,capture_hour\r\n) as t\r\ngroup by pathId');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_ONE_WEEK_COMMUNICATION_AREA_NUMBER', NULL, 'SELECT \r\n	number as arcName,\r\n	count(distinct area) as nums\r\nfrom\r\n	(\r\n	select\r\n		1 as flag,\r\n		dst_number as number,\r\n		calling_attribution as area\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and dst_number in (${phone})\r\n		and calling_attribution <> \'\'\r\n	group by\r\n		dst_number, calling_attribution\r\nUNION\r\n	select\r\n		2 as flag,\r\n		src_number as number,\r\n		called_attribution as area\r\n	from\r\n		${dws}.dws_element_behavior_call_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and src_number in (${phone})\r\n		and called_attribution <> \'\'\r\n	group by\r\n		src_number, called_attribution\r\n) t\r\ngroup by number;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'IM_ONE_WEEK_COMMUNICATE_ACCOUNT_NUMBER', NULL, 'SELECT\r\n	arcName,\r\n	appType,\r\n	count(distinct account) as nums from\r\n(\r\n	SELECT\r\n		1 as flag,\r\n		virtual_account as arcName,\r\n		virtual_app_type as appType,\r\n		target_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and virtual_account in (${imAccount}) ${app_type} ${dataType}\r\n		and target_account <> \'\'\r\nUNION\r\n	SELECT\r\n		2 as flag,\r\n		target_account as arcName,\r\n		virtual_app_type as appType,\r\n		virtual_account as account\r\n	from\r\n		${dws}.dws_element_behavior_connection_day\r\n	where\r\n		capture_day >= {start_day}\r\n		and capture_day <= {end_day}\r\n		and target_account in (${imAccount}) ${app_type} ${dataType}\r\n		and virtual_account <> \'\'\r\n) t\r\ngroup by\r\n	arcName,\r\n	appType;');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_TIME_STATISTICS', NULL, 'select CONCAT(FLOOR(capture_hour / ${interval}) * ${interval}, \'-\', (FLOOR(capture_hour / ${interval}) * ${interval} + ${interval})) timeRange,\r\nsum(stay_num) stayCount, count(DISTINCT base_station_no) areaCount, truncate(sum(stay_time)/3600, 3) stayHour\r\nfrom ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n${keyword_condition}\r\ngroup by timeRange\r\n${sort} ${top}');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_STAY_RECORD', NULL, 'select phone_stay_id stayId, base_station_no stationNo, min(in_time) inTime, max(out_time) outTime, truncate(sum(stay_time)/3600, 3) stayTime\r\nfrom ${ads}.ads_archive_phone_base_station_stay_detail\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand base_station_no = {stationNo}\r\ngroup by phone_stay_id, base_station_no');
INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('QUERY_ATTACHMENT_INFO_FILE_PATH', NULL, 'SELECT\r\n	file_path AS filePath, ${account_field} AS account\r\nfrom\r\n	${dws}.dws_element_behavior_attachment_day\r\nwhere\r\n	${account_type_condition}  in ({arc_account})\r\n	and attach_md5 = {attach_md5}\r\n	and data_type in ${data_type}\r\n	and attach_path <> \'\'\r\n	and capture_day >= {start_day}\r\n	and capture_day <= {end_day}');

