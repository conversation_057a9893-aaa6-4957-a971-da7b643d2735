--  Auto-generated SQL script #202406211606
-- 鉴权计费IP类型的翻译配置
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('ip_type',1,-1,'ip_type');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('ip_type', '0', 'IPv4', 'IPv4', 'IPv4');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('ip_type', '1', 'IPv6', 'IPv6', 'IPv6');

-- 漫游的翻译配置
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('roam_latitude',1,-1,'roam_latitude');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('roam_latitude', '20001', '漫游', 'itinérance', 'Roaming');

-- 删除信令周期内位置变化
--  Auto-generated SQL script #202406211702
DELETE FROM tb_arc_drill_down_field_config
	WHERE id=1326;
DELETE FROM tb_arc_drill_down_field_config
	WHERE id=1372;

-- 网络协议字典
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('protocol',1,-1,'protocol');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('protocol', '1', 'TCP', 'TCP', 'TCP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('protocol', '2', 'UDP', 'UDP', 'UDP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('protocol', '99', 'Autre', 'Autre', 'Autre');

-- 坐标系字段
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('coordinate_type',1,-1,'coordinate_type');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('coordinate_type', '1', 'WGS-84', 'WGS-84', 'WGS-84');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('coordinate_type', '2', 'GCJ-02', 'GCJ-02', 'GCJ-02');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('coordinate_type', '3', 'BD-09', 'BD-09', 'BD-09');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('coordinate_type', '4', 'MapBar', 'MapBar', 'MapBar');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('coordinate_type', '5', '搜狗', 'Sogou', 'Sogou');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('coordinate_type', '99', 'Autre', 'Autre', 'Autre');

-- 上网动作类型
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('action',1,-2,'auth_action');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('auth_action', '0', '开始计费', 'Démarrer la facturation', 'Démarrer la facturation');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('auth_action', '1', '结束计费', 'Stopper la facturation', 'Stopper la facturation');

-- NF 协议号
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('protocol',1,-1,'nf_protocol');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '1', 'HTTP', 'HTTP', 'HTTP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '2', 'WAP', 'WAP', 'WAP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '3', 'SMTP', 'SMTP', 'SMTP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '4', 'POP3', 'POP3', 'POP3');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '5', 'IMAP', 'IMAP', 'IMAP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '6', 'NNTP', 'NNTP', 'NNTP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '7', 'FTP', 'FTP', 'FTP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '8', 'SFTP', 'SFTP', 'SFTP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '9', 'TELNET', 'TELNET', 'TELNET');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '10', 'HTTPS', 'HTTPS', 'HTTPS');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '11', 'RTSP', 'RTSP', 'RTSP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '12', 'MMS', 'MMS', 'MMS');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '13', 'WEP', 'WEP', 'WEP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '14', 'WPA', 'WPA', 'WPA');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '15', 'PPTP', 'PPTP', 'PPTP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '16', 'L2TP', 'L2TP', 'L2TP');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '17', 'SOCKS', 'SOCKS', 'SOCKS');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '18', 'Cmpop', 'Cmpop', 'Cmpop');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '19', 'Cmsmtp', 'Cmsmtp', 'Cmsmtp');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '91', 'Private', 'Private', 'Private');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '99', 'Autre', 'Autre', 'Autre');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('nf_protocol', '98', 'WEBSOCKET', 'WEBSOCKET', 'WEBSOCKET');

-- 硬件特征串
INSERT INTO tb_arc_drill_down_field_translate_config (field,translate_type,protocol_id,dict_key)
	VALUES ('hardware_sign',1,-1,'hardware_sign');

INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '1', 'IMEI', 'IMEI', 'IMEI');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '2', 'MAC地址', 'Adresse MAC', 'Adresse MAC');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '3', 'QQ特征串 16位appsid', 'Caractéristique spéciale de QQ', 'Caractéristique spéciale de QQ');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '4', '迅雷特征串 MAC', 'Caractéristique spéciale de Thunder', 'Caractéristique spéciale de Thunder');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '5', '360特征串', 'Caractéristique spéciale de 360', 'Caractéristique spéciale de 360');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '6', '暴风影音', 'Baofeng Yingyin', 'Baofeng Yingyin');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '7', '酷狗音乐', 'KuGou YinYue', 'KuGou YinYue');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '8', '风行', 'FengXing', 'FengXing');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '9', 'Qvod', 'Qvod', 'Qvod');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '87', 'IOS设备唯一ID(idfa)', 'Identifiant unique de l\'appareil iOS', 'Identifiant unique de l\'appareil iOS');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '88', '安卓市场(HiMarket)', 'Marché Android', 'Marché Android');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '89', '易信', 'YiXin', 'YiXin');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '90', 'imsi', 'imsi', 'imsi');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '91', '艾瑞硬件特征', 'Caractéristique matérielle iRui', 'Caractéristique matérielle iRui');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '92', 'UcWeb手机串号信息', 'Informations de série de téléphone mobile UcWeb', 'Informations de série de téléphone mobile UcWeb');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '93', '迅雷特征串 非MAC', 'Caractéristique spéciale de Thunder', 'Caractéristique spéciale de Thunder');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '94', '爱奇艺', 'AiQiYi', 'AiQiYi');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '95', '携程', 'XieCheng', 'XieCheng');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '96', 'YY mac', 'YY mac', 'YY mac');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '97', 'Caractéristique spéciale de QQ (4 caractères)', 'Caractéristique spéciale de QQ', 'Caractéristique spéciale de QQ');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '98', '360mac后8位', '360 MAC 8 derniers bits', '360 MAC 8 derniers bits');
INSERT INTO tb_arc_dict
(dict_key, dict_code, dict_name_zh, dict_name_fr, dict_name_en)
VALUES('hardware_sign', '99', '其他', 'Autre', 'Autre');

-- 更新NF协议的认账账号默认不支持搜索
UPDATE tb_arc_drill_down_field_config
	SET is_hide=1
	WHERE data_type = 'nf_url' and field = "user_name";
UPDATE tb_arc_drill_down_field_config
	SET is_hide=0
	WHERE data_type = 'nf_url' and field = "auth_account";
UPDATE tb_arc_drill_down_field_config
	SET is_search=0
	WHERE data_type = 'nf_url' and field = "auth_account";

UPDATE tb_arc_drill_down_field_config
	SET is_hide=1
	WHERE data_type = 'nf_chat' and field = "user_name";
UPDATE tb_arc_drill_down_field_config
	SET is_hide=0
	WHERE data_type = 'nf_chat' and field = "auth_account";
UPDATE tb_arc_drill_down_field_config
	SET is_search=0
	WHERE data_type = 'nf_chat' and field = "auth_account";

UPDATE tb_arc_drill_down_field_config
	SET is_hide=1
	WHERE data_type = 'nf_other_log' and field = "user_name";
UPDATE tb_arc_drill_down_field_config
	SET is_hide=0
	WHERE data_type = 'nf_other_log' and field = "auth_account";
UPDATE tb_arc_drill_down_field_config
    SET is_search=0
    WHERE data_type = 'nf_other_log' and field = "auth_account";


INSERT INTO `tb_arc_drill_down_field_translate_config`(`field`, `translate_type`, `protocol_id`, `dict_key`) VALUES ('ownership_land', 1, -1, 'country_code');
INSERT INTO `tb_arc_drill_down_field_translate_config`(`field`, `translate_type`, `protocol_id`, `dict_key`) VALUES ('internet_land', 1, -1, 'country_code');

INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '109', '通话', 'Appel téléphonique', 'Appel téléphonique');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '200', '短信', 'Message texte', 'Message texte');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '210', '传真', 'Télécopie', 'Télécopie');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '2109', '网络电话', 'Téléphonie sur Internet', 'Téléphonie sur Internet');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '101', 'Email', 'Email', 'Email');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '990', 'RADIUS', 'RADIUS', 'RADIUS');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '994', '位置', 'LOCATION', 'LOCATION');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '121', 'VPN', 'VPN', 'VPN');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '103', 'IM', 'IM', 'IM');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '119', '社交', ' Réseaux sociaux', ' Réseaux sociaux');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '123', '出行', ' Déplacement', ' Déplacement');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '949', '工具', 'Outil', 'Outil');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '142', '终端', 'Terminal', 'Terminal');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '113', 'RemoteCTRL', 'RemoteCTRL', 'RemoteCTRL');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '204', 'Location', 'Emplacement géographique', 'Emplacement géographique');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '100', 'HTTP', 'HTTP', 'HTTP');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '105', 'FTP', 'FTP', 'FTP');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '108', 'Telnet', 'Telnet', 'Telnet');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '125', 'Engine', 'Engine', 'Engine');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '2002', '多媒体', 'Multimédia', 'Multimédia');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '138', '资讯', 'Information', 'Information');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '140', '娱乐', 'Divertissement', 'Divertissement');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '122', '网购', 'Achat en ligne', 'Achat en ligne');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '963', '金融', 'Finance', 'Finance');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '999', '其他', 'Autre', 'Autre');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1201', 'URL', 'URL', 'URL');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1202', 'EMAIL', 'EMAIL', 'EMAIL');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1203', 'CHAT', 'CHAT', 'CHAT');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1204', 'BBS_WEIBO', 'BBS_WEIBO', 'BBS_WEIBO');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1205', 'OTHER_lOG', 'OTHER_lOG', 'OTHER_lOG');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1222', '访问网站', 'Visiter un site web', 'Visiter un site web');
INSERT INTO `tb_arc_dict`(`dict_key`, `dict_code`, `dict_name_zh`, `dict_name_fr`, `dict_name_en`) VALUES ('business_data_type', '1223', '其它行为', 'Autres actions', 'Autres actions');

