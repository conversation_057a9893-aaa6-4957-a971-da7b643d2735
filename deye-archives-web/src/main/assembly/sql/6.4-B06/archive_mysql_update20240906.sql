CREATE TABLE `tb_arc_collection_info` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` varchar(64) NOT NULL COMMENT '用户id',
  `archive_id` varchar(64) NOT NULL COMMENT '档案id',
  `archive_type` TINYINT  NOT NULL COMMENT '档案类型',
   create_time BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='档案收藏表';