-- 变更 code
delete from tb_arc_service_sql where `code` in ('COMMON_GET_TAG_TOP_LIST','COMMON_GET_TAG_LIST_DAY','COMMON_GET_TAG_LIST_MONTH','PHONE_HIS_WEEK_TRAJECTORY','PHONE_HIS_MONTH_TRAJECTORY','EMAIL_PHONE_EXTRACT_SOURCE');

INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_GET_TAG_TOP_LIST', NULL, 'SELECT\r\n	GROUP_CONCAT(`tag_name` ORDER BY behavior_num DESC , \',\') tagName,\r\n	tag_value tagValue,\r\n	SUM(behavior_num) num\r\nFROM\r\n	${ads}.ads_archive_tag_statistics_day\r\nWHERE\r\n	capture_day BETWEEN {start_day}\r\nAND {end_day}\r\nAND archive_name = {arc_name}\r\nAND archive_type = ${arc_type}\r\nAND tag_value <> \'\'\r\nGROUP BY\r\n	tag_value ORDER BY num DESC\r\nLIMIT ${top_num};');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_GET_TAG_LIST_DAY', NULL, 'SELECT\r\n	GROUP_CONCAT(`tag_name` ORDER BY behavior_num DESC , \',\') tagName,\r\n	tag_value tagValue,\r\n	SUM(behavior_num) behaviorNum,\r\n  min(earliest_relation_time) earliestTime,\r\n  max(latest_relation_time) latestTime\r\nFROM\r\n	${ads}.ads_archive_tag_statistics_day\r\nWHERE\r\n	capture_day BETWEEN {start_day}\r\nAND {end_day}\r\nAND archive_name = {arc_name}\r\nAND archive_type = ${arc_type}  ${keyword_condition}\r\nAND tag_value <> \'\'\r\nGROUP BY\r\n	tag_value');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'COMMON_GET_TAG_LIST_MONTH', NULL, 'SELECT\r\n	GROUP_CONCAT(`tag_name` ORDER BY behavior_num DESC , \',\') tagName,\r\n	tag_value tagValue,\r\n	SUM(behavior_num) behaviorNum,\r\n  min(earliest_relation_time) earliestTime,\r\n  max(latest_relation_time) latestTime\r\nFROM\r\n	${ads}.ads_archive_tag_statistics_day\r\nWHERE\r\n	capture_month >= ${start_month}\r\nAND capture_month <= ${end_month}\r\nAND archive_name = {arc_name}\r\nAND archive_type = ${arc_type}\r\nAND tag_value <> \'\'\r\n ${keyword_condition}\r\nGROUP BY\r\n	tag_value');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_HIS_WEEK_TRAJECTORY', NULL, 'SELECT `any`(address) stationAddess, station_no baseStationNo, `any`(network_type) netWorkType, sum(stay_time) onlineHour,\r\nmin(in_time) earliestRelationTime, max(out_time) latestRelationTime, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n ${station_condition}\r\n ${keyword_condition}\r\n group by station_no\r\n HAVING stayNum >= ${stayNumLimit}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_HIS_MONTH_TRAJECTORY', NULL, 'SELECT `any`(address) stationAddess, station_no baseStationNo, `any`(network_type) netWorkType, sum(stay_time) onlineHour,\r\nmin(in_time) earliestRelationTime, max(out_time) latestRelationTime, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_month >= {start_month}\r\nand capture_month <= {end_month}\r\n ${station_condition}\r\n ${keyword_condition}\r\n group by station_no\r\n HAVING stayNum >= ${stayNumLimit}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_PHONE_EXTRACT_SOURCE', NULL, 'select virtual_account virtualAccount, auth_account authAccount, auth_type authType,attach_name attachName, file_path filePath, DATE_FORMAT(FROM_UNIXTIME(capture_time/1000), \'%Y-%m-%d %H:%i:%s\') captureDay,capture_time as captureTime\r\nfrom  ${dws}.dws_element_email_phone_extract_detail\r\nwhere virtual_account = {virtual_account}\r\nand phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}');

