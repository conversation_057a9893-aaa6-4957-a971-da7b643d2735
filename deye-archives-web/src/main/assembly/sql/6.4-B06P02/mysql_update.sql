-- 变更 code
delete from tb_arc_service_sql where `code` in ('PHONE_AREA_HOT_TIME_STATISTICS','PHONE_FREQUENT_ACTIVE_AREA','EMAIL_RELATION_EXTENSION','PHONE_RELATION_EXTENSION','EMAIL_PHONE_EXTRACT_SOURCE','PHONE_HIS_DAY_TRAJECTORY','PHONE_AREA_HOT_TRAJECTORY');

INSERT INTO `tb_arc_service_sql` (`code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_TIME_STATISTICS', NULL, 'select CONCAT(FLOOR(capture_hour / ${interval}) * ${interval}, \'-\', (FLOOR(capture_hour / ${interval}) * ${interval} + ${interval})) timeRange,\r\nsum(stay_num) stayCount, count(DISTINCT station_no) areaCount, sum(stay_time) staySeconds\r\nfrom ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n${keyword_condition}\r\ngroup by timeRange\r\n${sort} ${top}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_FREQUENT_ACTIVE_AREA', NULL, 'SELECT station_no baseStationNo, `any`(network_type) netWorkType, sum(stay_time) onlineHour,\r\n`any`(address) stationAddess, `any`(station_longitude) lng, `any`(station_latitude) lat\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= \'${start_day}\'\r\nand capture_day <= \'${end_day}\'\r\ngroup by baseStationNo order by onlineHour desc');
INSERT INTO `tb_arc_service_sql` (`code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_RELATION_EXTENSION', NULL, 'select src_account,dst_account,total from (\r\nSELECT\r\ncase \r\nwhen virtual_account in (${relateVirtualAccounts}) then virtual_account  \r\nwhen target_account in (${relateVirtualAccounts}) then target_account  \r\nend src_account,\r\ncase \r\nwhen virtual_account in (${relateVirtualAccounts}) then target_account \r\nwhen target_account in (${relateVirtualAccounts}) then virtual_account  \r\nend dst_account,\r\nsum(behavior_num) total\r\nFROM ${dws}.dws_element_behavior_connection_day \r\nwhere \r\n(virtual_account in (${relateVirtualAccounts}) or target_account in (${relateVirtualAccounts}) )\r\n${virtual_app_type_condition}\r\nand virtual_account <> target_account\r\nand data_type = \'${data_type}\'\r\nand capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\nand spam_flag=0\r\ngroup by src_account,dst_account\r\n) as t where dst_account <> \'\' ${minLinkCountCondition}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_RELATION_EXTENSION', NULL, 'select `number`,`total`,`call`,fax,sms from (\r\nselect connect_number `number`, sum(behaviorNum) `total`,\r\nsum(if(callTag = 2, behaviorNum, 0)) `call`,\r\nsum(if(callTag = 1, behaviorNum, 0)) fax,\r\nsum(if(callTag = 3, behaviorNum, 0)) sms\r\nfrom (\r\nselect \r\ncase \r\nwhen src_number = \'${arcAccount}\' then dst_number  \r\nwhen dst_number = \'${arcAccount}\' then src_number  \r\nend connect_number,\r\ncall_tag callTag,\r\nsum(behavior_num) behaviorNum\r\nfrom ${dws}.dws_element_behavior_call_day \r\nwhere (src_number = \'${arcAccount}\' or dst_number = \'${arcAccount}\') and capture_day >= \'${start_day}\' and capture_day <= \'${end_day}\'\r\nand src_number <> dst_number \r\n${call_tag_condition}\r\nand spam_flag=0\r\ngroup by connect_number,call_tag\r\n) as t group by number\r\n) as t1  where `number` <> \'\' ${minLinkCountCondition}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_IMEI_LIST_BATCH', NULL, 'select phone_number account, max(CONCAT(capture_time,\'_\',device_id)) timeImei from \r\n${ads}.ads_archive_phone_location_his\r\nwhere phone_number in (${arcAccount})\r\nand device_id <> \'\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\ngroup by phone_number; ');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'EMAIL_PHONE_EXTRACT_SOURCE', NULL, 'select virtual_account virtualAccount, auth_account authAccount, auth_type authType,attach_name attachName, file_path filePath, DATE_FORMAT(FROM_UNIXTIME(capture_time/1000), \'%Y-%m-%d %H:%i:%s\') captureDay\r\nfrom  ${dws}.dws_element_email_phone_extract_detail\r\nwhere virtual_account = {virtual_account}\r\nand phone_number = {phone}\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_HIS_DAY_TRAJECTORY', NULL, 'SELECT address stationAddess, station_no baseStationNo, network_type netWorkType, stay_time onlineHour,\r\nin_time earliestRelationTime, out_time latestRelationTime, station_longitude lng, station_latitude lat\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n ${station_condition}\r\n ${keyword_condition}');
INSERT INTO `tb_arc_service_sql` ( `code`, `my_sql`, `ck_sql`) VALUES ( 'PHONE_AREA_HOT_TRAJECTORY', NULL, 'SELECT `any`(address) stationAddess, station_no baseStationNo, `any`(network_type) netWorkType, sum(stay_time) onlineHour,\r\nmin(in_time) earliestRelationTime, max(out_time) latestRelationTime, `any`(station_longitude) lng, `any`(station_latitude) lat, sum(stay_num) stayNum\r\nFROM ${ads}.ads_archive_phone_location_his\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\n ${hour_condition}\r\n ${station_condition}\r\n ${keyword_condition}\r\n group by station_no\r\n');

-- 更新翻译字典表
update tb_arc_dict set dict_name_en = 'Puerto Rico' where dict_name_zh='波多黎各';
update tb_arc_dict set dict_name_en = 'Palestine' where dict_name_zh='巴勒斯坦';
update tb_arc_dict set dict_name_en = 'Portugal' where dict_name_zh='葡萄牙';
update tb_arc_dict set dict_name_en = 'Palau' where dict_name_zh='帕劳';
update tb_arc_dict set dict_name_en = 'Paraguay' where dict_name_zh='巴拉圭';
update tb_arc_dict set dict_name_en = 'Qatar' where dict_name_zh='卡塔尔';
update tb_arc_dict set dict_name_en = 'Andorra' where dict_name_zh='安道尔';
update tb_arc_dict set dict_name_en = 'United Arab Emirates' where dict_name_zh='阿联酋';
update tb_arc_dict set dict_name_en = 'Afghanistan' where dict_name_zh='阿富汗';
update tb_arc_dict set dict_name_en = 'Antigua and Barbuda' where dict_name_zh='安提瓜和巴布达';
update tb_arc_dict set dict_name_en = 'Anguilla' where dict_name_zh='安圭拉';
update tb_arc_dict set dict_name_en = 'Albania' where dict_name_zh='阿尔巴尼亚';
update tb_arc_dict set dict_name_en = 'Armenia' where dict_name_zh='亚美尼亚';
update tb_arc_dict set dict_name_en = 'Angola' where dict_name_zh='安哥拉';
update tb_arc_dict set dict_name_en = 'Antarctica' where dict_name_zh='南极洲';
update tb_arc_dict set dict_name_en = 'Argentina' where dict_name_zh='阿根廷';
update tb_arc_dict set dict_name_en = 'American Samoa' where dict_name_zh='美属萨摩亚';
update tb_arc_dict set dict_name_en = 'Réunion' where dict_name_zh='留尼汪';
update tb_arc_dict set dict_name_en = 'Austria' where dict_name_zh='奥地利';
update tb_arc_dict set dict_name_en = 'Australia' where dict_name_zh='澳大利亚';
update tb_arc_dict set dict_name_en = 'Aruba' where dict_name_zh='阿鲁巴';
update tb_arc_dict set dict_name_en = 'Åland' where dict_name_zh='奥兰群岛';
update tb_arc_dict set dict_name_en = 'Azerbaijan' where dict_name_zh='阿塞拜疆';
update tb_arc_dict set dict_name_en = 'Romania' where dict_name_zh='罗马尼亚';
update tb_arc_dict set dict_name_en = 'Bosnia and Herz.' where dict_name_zh='波黑';
update tb_arc_dict set dict_name_en = 'Barbados' where dict_name_zh='巴巴多斯';
update tb_arc_dict set dict_name_en = 'Serbia' where dict_name_zh='塞尔维亚';
update tb_arc_dict set dict_name_en = 'Bangladesh' where dict_name_zh='孟加拉国';
update tb_arc_dict set dict_name_en = 'Russia' where dict_name_zh='俄罗斯联邦';
update tb_arc_dict set dict_name_en = 'Belgium' where dict_name_zh='比利时';
update tb_arc_dict set dict_name_en = 'Burkina Faso' where dict_name_zh='布基纳法索';
update tb_arc_dict set dict_name_en = 'Rwanda' where dict_name_zh='卢旺达';
update tb_arc_dict set dict_name_en = 'Bulgaria' where dict_name_zh='保加利亚';
update tb_arc_dict set dict_name_en = 'Bahrain' where dict_name_zh='巴林';
update tb_arc_dict set dict_name_en = 'Burundi' where dict_name_zh='布隆迪';
update tb_arc_dict set dict_name_en = 'Benin' where dict_name_zh='贝宁';
update tb_arc_dict set dict_name_en = 'Saint Barthélemy' where dict_name_zh='圣巴泰勒米';
update tb_arc_dict set dict_name_en = 'Bermuda' where dict_name_zh='百慕大';
update tb_arc_dict set dict_name_en = 'Brunei' where dict_name_zh='文莱';
update tb_arc_dict set dict_name_en = 'Bolivia' where dict_name_zh='玻利维亚';
update tb_arc_dict set dict_name_en = 'Saudi Arabia' where dict_name_zh='沙特阿拉伯';
update tb_arc_dict set dict_name_en = 'Solomon Islands' where dict_name_zh='所罗门群岛';
update tb_arc_dict set dict_name_en = 'Bonaire' where dict_name_zh='博奈尔岛、圣尤斯达蒂斯和萨巴';
update tb_arc_dict set dict_name_en = 'Seychelles' where dict_name_zh='塞舌尔';
update tb_arc_dict set dict_name_en = 'Brazil' where dict_name_zh='巴西';
update tb_arc_dict set dict_name_en = 'Sudan' where dict_name_zh='苏丹';
update tb_arc_dict set dict_name_en = 'Bahamas' where dict_name_zh='巴哈马';
update tb_arc_dict set dict_name_en = 'Sweden' where dict_name_zh='瑞典';
update tb_arc_dict set dict_name_en = 'Bhutan' where dict_name_zh='不丹';
update tb_arc_dict set dict_name_en = 'Singapore' where dict_name_zh='新加坡';
update tb_arc_dict set dict_name_en = 'Bouvet Island' where dict_name_zh='布维岛';
update tb_arc_dict set dict_name_en = 'Botswana' where dict_name_zh='博茨瓦纳';
update tb_arc_dict set dict_name_en = 'Saint Helena' where dict_name_zh='圣赫勒拿';
update tb_arc_dict set dict_name_en = 'Slovenia' where dict_name_zh='斯洛文尼亚';
update tb_arc_dict set dict_name_en = 'Svalbard and Jan Mayen' where dict_name_zh='斯瓦尔巴岛和扬马延岛';
update tb_arc_dict set dict_name_en = 'Belarus' where dict_name_zh='白俄罗斯';
update tb_arc_dict set dict_name_en = 'Slovakia' where dict_name_zh='斯洛伐克';
update tb_arc_dict set dict_name_en = 'Belize' where dict_name_zh='伯利兹';
update tb_arc_dict set dict_name_en = 'Sierra Leone' where dict_name_zh='塞拉利昂';
update tb_arc_dict set dict_name_en = 'San Marino' where dict_name_zh='圣马力诺';
update tb_arc_dict set dict_name_en = 'Senegal' where dict_name_zh='塞内加尔';
update tb_arc_dict set dict_name_en = 'Somalia' where dict_name_zh='索马里';
update tb_arc_dict set dict_name_en = 'Canada' where dict_name_zh='加拿大';
update tb_arc_dict set dict_name_en = 'Suriname' where dict_name_zh='苏里南';
update tb_arc_dict set dict_name_en = 'South Sudan' where dict_name_zh='南苏丹';
update tb_arc_dict set dict_name_en = 'Cocos [Keeling] Islands' where dict_name_zh='科科斯（基林）群岛';
update tb_arc_dict set dict_name_en = 'Dem. Rep. Congo' where dict_name_zh='刚果民主共和国';
update tb_arc_dict set dict_name_en = 'São Tomé and Príncipe' where dict_name_zh='圣多美和普林西比';
update tb_arc_dict set dict_name_en = 'Central African Rep.' where dict_name_zh='中非';
update tb_arc_dict set dict_name_en = 'El Salvador' where dict_name_zh='萨尔瓦多';
update tb_arc_dict set dict_name_en = 'Congo' where dict_name_zh='刚果';
update tb_arc_dict set dict_name_en = 'Switzerland' where dict_name_zh='瑞士';
update tb_arc_dict set dict_name_en = 'Sint Maarten' where dict_name_zh='圣马丁岛';
update tb_arc_dict set dict_name_en = 'Syria' where dict_name_zh='叙利亚';
update tb_arc_dict set dict_name_en = 'Ivory Coast' where dict_name_zh='象牙海岸';
update tb_arc_dict set dict_name_en = 'Eswatini' where dict_name_zh='斯威士兰';
update tb_arc_dict set dict_name_en = 'Cook Islands' where dict_name_zh='库克群岛';
update tb_arc_dict set dict_name_en = 'Chile' where dict_name_zh='智利';
update tb_arc_dict set dict_name_en = 'Cameroon' where dict_name_zh='喀麦隆';
update tb_arc_dict set dict_name_en = 'China' where dict_name_zh='中国';
update tb_arc_dict set dict_name_en = 'Colombia' where dict_name_zh='哥伦比亚';
update tb_arc_dict set dict_name_en = 'Turks and Caicos Islands' where dict_name_zh='特克斯和凯科斯群岛';
update tb_arc_dict set dict_name_en = 'Costa Rica' where dict_name_zh='哥斯达黎加';
update tb_arc_dict set dict_name_en = 'Chad' where dict_name_zh='乍得';
update tb_arc_dict set dict_name_en = 'French Southern Territories' where dict_name_zh='法属南部领地';
update tb_arc_dict set dict_name_en = 'Cuba' where dict_name_zh='古巴';
update tb_arc_dict set dict_name_en = 'Togo' where dict_name_zh='多哥';
update tb_arc_dict set dict_name_en = 'Cabo Verde' where dict_name_zh='佛得角';
update tb_arc_dict set dict_name_en = 'Thailand' where dict_name_zh='泰国';
update tb_arc_dict set dict_name_en = 'Curaçao' where dict_name_zh='库拉索';
update tb_arc_dict set dict_name_en = 'Christmas Island' where dict_name_zh='圣诞岛';
update tb_arc_dict set dict_name_en = 'Cyprus' where dict_name_zh='塞浦路斯';
update tb_arc_dict set dict_name_en = 'Tajikistan' where dict_name_zh='塔吉克斯坦';
update tb_arc_dict set dict_name_en = 'Czech Rep.' where dict_name_zh='捷克';
update tb_arc_dict set dict_name_en = 'Tokelau' where dict_name_zh='托克劳';
update tb_arc_dict set dict_name_en = 'East Timor' where dict_name_zh='东帝汶';
update tb_arc_dict set dict_name_en = 'Turkmenistan' where dict_name_zh='土库曼斯坦';
update tb_arc_dict set dict_name_en = 'Tunisia' where dict_name_zh='突尼斯';
update tb_arc_dict set dict_name_en = 'Tonga' where dict_name_zh='汤加';
update tb_arc_dict set dict_name_en = 'Turkey' where dict_name_zh='土耳其';
update tb_arc_dict set dict_name_en = 'Kashmir' where dict_name_zh='克什米尔';
update tb_arc_dict set dict_name_en = 'Trinidad and Tobago' where dict_name_zh='特立尼达和多巴哥';
update tb_arc_dict set dict_name_en = 'Germany' where dict_name_zh='德国';
update tb_arc_dict set dict_name_en = 'Tuvalu' where dict_name_zh='图瓦卢';
update tb_arc_dict set dict_name_en = 'Taiwan' where dict_name_zh='中国台湾';
update tb_arc_dict set dict_name_en = 'Tanzania' where dict_name_zh='坦桑尼亚';
update tb_arc_dict set dict_name_en = 'Djibouti' where dict_name_zh='吉布提';
update tb_arc_dict set dict_name_en = 'Denmark' where dict_name_zh='丹麦';
update tb_arc_dict set dict_name_en = 'Dominica' where dict_name_zh='多米尼加';
update tb_arc_dict set dict_name_en = 'Dominican Republic' where dict_name_zh='多米尼加';
update tb_arc_dict set dict_name_en = 'Ukraine' where dict_name_zh='乌克兰';
update tb_arc_dict set dict_name_en = 'Uganda' where dict_name_zh='乌干达';
update tb_arc_dict set dict_name_en = 'Algeria' where dict_name_zh='阿尔及利亚';
update tb_arc_dict set dict_name_en = 'U.S. Minor Outlying Islands' where dict_name_zh='美国本土外小岛屿';
update tb_arc_dict set dict_name_en = 'Ecuador' where dict_name_zh='厄瓜多尔';
update tb_arc_dict set dict_name_en = 'United States' where dict_name_zh='美国';
update tb_arc_dict set dict_name_en = 'Estonia' where dict_name_zh='爱沙尼亚';
update tb_arc_dict set dict_name_en = 'Egypt' where dict_name_zh='埃及';
update tb_arc_dict set dict_name_en = 'W. Sahara' where dict_name_zh='西撒哈拉';
update tb_arc_dict set dict_name_en = 'Uruguay' where dict_name_zh='乌拉圭';
update tb_arc_dict set dict_name_en = 'Uzbekistan' where dict_name_zh='乌兹别克斯坦';
update tb_arc_dict set dict_name_en = 'Vatican City' where dict_name_zh='梵蒂冈';
update tb_arc_dict set dict_name_en = 'Eritrea' where dict_name_zh='厄立特里亚';
update tb_arc_dict set dict_name_en = 'Saint Vincent and the Grenadines' where dict_name_zh='圣文森特和格林纳丁斯';
update tb_arc_dict set dict_name_en = 'Spain' where dict_name_zh='西班牙';
update tb_arc_dict set dict_name_en = 'Ethiopia' where dict_name_zh='埃塞俄比亚';
update tb_arc_dict set dict_name_en = 'Venezuela' where dict_name_zh='委内瑞拉';
update tb_arc_dict set dict_name_en = 'British Virgin Islands' where dict_name_zh='英属维尔京群岛';
update tb_arc_dict set dict_name_en = 'U.S. Virgin Islands' where dict_name_zh='美属维尔京群岛';
update tb_arc_dict set dict_name_en = 'Vietnam' where dict_name_zh='越南';
update tb_arc_dict set dict_name_en = 'Vanuatu' where dict_name_zh='瓦努阿图';
update tb_arc_dict set dict_name_en = 'Finland' where dict_name_zh='芬兰';
update tb_arc_dict set dict_name_en = 'Fiji' where dict_name_zh='斐济';
update tb_arc_dict set dict_name_en = 'Falkland Islands' where dict_name_zh='福克兰群岛（马尔维纳斯）';
update tb_arc_dict set dict_name_en = 'Federated States of Micronesia' where dict_name_zh='密克罗尼西亚联邦';
update tb_arc_dict set dict_name_en = 'Faroe Islands' where dict_name_zh='法罗群岛';
update tb_arc_dict set dict_name_en = 'France' where dict_name_zh='法国';
update tb_arc_dict set dict_name_en = 'Wallis and Futuna' where dict_name_zh='瓦利斯和富图纳';
update tb_arc_dict set dict_name_en = 'Gabon' where dict_name_zh='加蓬';
update tb_arc_dict set dict_name_en = 'United Kingdom' where dict_name_zh='英国';
update tb_arc_dict set dict_name_en = 'Samoa' where dict_name_zh='萨摩亚';
update tb_arc_dict set dict_name_en = 'Grenada' where dict_name_zh='格林纳达';
update tb_arc_dict set dict_name_en = 'Georgia' where dict_name_zh='格鲁吉亚';
update tb_arc_dict set dict_name_en = 'French Guiana' where dict_name_zh='法属圭亚那';
update tb_arc_dict set dict_name_en = 'Guernsey' where dict_name_zh='格恩西岛';
update tb_arc_dict set dict_name_en = 'Ghana' where dict_name_zh='加纳';
update tb_arc_dict set dict_name_en = 'Gibraltar' where dict_name_zh='直布罗陀';
update tb_arc_dict set dict_name_en = 'Greenland' where dict_name_zh='格陵兰';
update tb_arc_dict set dict_name_en = 'Gambia' where dict_name_zh='冈比亚';
update tb_arc_dict set dict_name_en = 'Guinea' where dict_name_zh='几内亚';
update tb_arc_dict set dict_name_en = 'Guadeloupe' where dict_name_zh='瓜德罗普';
update tb_arc_dict set dict_name_en = 'Equatorial Guinea' where dict_name_zh='赤道几内亚';
update tb_arc_dict set dict_name_en = 'Greece' where dict_name_zh='希腊';
update tb_arc_dict set dict_name_en = 'South Georgia and the South Sandwich Islands' where dict_name_zh='南乔治亚 岛和南桑德韦奇岛';
update tb_arc_dict set dict_name_en = 'Guatemala' where dict_name_zh='危地马拉';
update tb_arc_dict set dict_name_en = 'Guam' where dict_name_zh='关岛';
update tb_arc_dict set dict_name_en = 'Guinea-Bissau' where dict_name_zh='几内亚比绍';
update tb_arc_dict set dict_name_en = 'Guyana' where dict_name_zh='圭亚那';
update tb_arc_dict set dict_name_en = 'Kosovo' where dict_name_zh='科索沃';
update tb_arc_dict set dict_name_en = 'Hong Kong' where dict_name_zh='中国香港';
update tb_arc_dict set dict_name_en = 'Heard Island and McDonald Islands' where dict_name_zh='赫德岛和麦克唐纳岛';
update tb_arc_dict set dict_name_en = 'Honduras' where dict_name_zh='洪都拉斯';
update tb_arc_dict set dict_name_en = 'Croatia' where dict_name_zh='克罗地亚';
update tb_arc_dict set dict_name_en = 'Yemen' where dict_name_zh='也门';
update tb_arc_dict set dict_name_en = 'Haiti' where dict_name_zh='海地';
update tb_arc_dict set dict_name_en = 'Hungary' where dict_name_zh='匈牙利';
update tb_arc_dict set dict_name_en = 'Mayotte' where dict_name_zh='马约特';
update tb_arc_dict set dict_name_en = 'Indonesia' where dict_name_zh='印度尼西亚';
update tb_arc_dict set dict_name_en = 'Ireland' where dict_name_zh='爱尔兰';
update tb_arc_dict set dict_name_en = 'Israel' where dict_name_zh='以色列';
update tb_arc_dict set dict_name_en = 'Isle of Man' where dict_name_zh='英国属地曼岛';
update tb_arc_dict set dict_name_en = 'India' where dict_name_zh='印度';
update tb_arc_dict set dict_name_en = 'British Indian Ocean Territory' where dict_name_zh='英属印度洋领地';
update tb_arc_dict set dict_name_en = 'South Africa' where dict_name_zh='南非';
update tb_arc_dict set dict_name_en = 'Iraq' where dict_name_zh='伊拉克';
update tb_arc_dict set dict_name_en = 'Iran' where dict_name_zh='伊朗';
update tb_arc_dict set dict_name_en = 'Iceland' where dict_name_zh='冰岛';
update tb_arc_dict set dict_name_en = 'Italy' where dict_name_zh='意大利';
update tb_arc_dict set dict_name_en = 'Zambia' where dict_name_zh='赞比亚';
update tb_arc_dict set dict_name_en = 'Jersey' where dict_name_zh='泽西岛';
update tb_arc_dict set dict_name_en = 'Zimbabwe' where dict_name_zh='津巴布韦';
update tb_arc_dict set dict_name_en = 'Jamaica' where dict_name_zh='牙买加';
update tb_arc_dict set dict_name_en = 'Jordan' where dict_name_zh='约旦';
update tb_arc_dict set dict_name_en = 'Japan' where dict_name_zh='日本';
update tb_arc_dict set dict_name_en = 'Kenya' where dict_name_zh='肯尼亚';
update tb_arc_dict set dict_name_en = 'Kyrgyzstan' where dict_name_zh='吉尔吉斯斯坦';
update tb_arc_dict set dict_name_en = 'Cambodia' where dict_name_zh='柬埔寨';
update tb_arc_dict set dict_name_en = 'Kiribati' where dict_name_zh='基里巴斯';
update tb_arc_dict set dict_name_en = 'Comoros' where dict_name_zh='科摩罗';
update tb_arc_dict set dict_name_en = 'St Kitts and Nevis' where dict_name_zh='圣基茨和尼维斯';
update tb_arc_dict set dict_name_en = 'Dem. Rep. Korea' where dict_name_zh='朝鲜';
update tb_arc_dict set dict_name_en = 'Korea' where dict_name_zh='韩国';
update tb_arc_dict set dict_name_en = 'Kuwait' where dict_name_zh='科威特';
update tb_arc_dict set dict_name_en = 'Cayman Islands' where dict_name_zh='开曼群岛';
update tb_arc_dict set dict_name_en = 'Kazakhstan' where dict_name_zh='哈萨克斯坦';
update tb_arc_dict set dict_name_en = 'Laos' where dict_name_zh='老挝';
update tb_arc_dict set dict_name_en = 'Lebanon' where dict_name_zh='黎巴嫩';
update tb_arc_dict set dict_name_en = 'Saint Lucia' where dict_name_zh='圣卢西亚';
update tb_arc_dict set dict_name_en = 'Liechtenstein' where dict_name_zh='列支敦士登';
update tb_arc_dict set dict_name_en = 'Sri Lanka' where dict_name_zh='斯里兰卡';
update tb_arc_dict set dict_name_en = 'Liberia' where dict_name_zh='利比里亚';
update tb_arc_dict set dict_name_en = 'Lesotho' where dict_name_zh='莱索托';
update tb_arc_dict set dict_name_en = 'Lithuania' where dict_name_zh='立陶宛';
update tb_arc_dict set dict_name_en = 'Luxembourg' where dict_name_zh='卢森堡';
update tb_arc_dict set dict_name_en = 'Latvia' where dict_name_zh='拉脱维亚';
update tb_arc_dict set dict_name_en = 'Libya' where dict_name_zh='利比亚';
update tb_arc_dict set dict_name_en = 'Morocco' where dict_name_zh='摩洛哥';
update tb_arc_dict set dict_name_en = 'Principality of Monaco' where dict_name_zh='摩纳哥';
update tb_arc_dict set dict_name_en = 'Moldova' where dict_name_zh='摩尔多瓦';
update tb_arc_dict set dict_name_en = 'Montenegro' where dict_name_zh='黑山';
update tb_arc_dict set dict_name_en = 'Saint Martin' where dict_name_zh='圣马丁';
update tb_arc_dict set dict_name_en = 'Madagascar' where dict_name_zh='马达加斯加';
update tb_arc_dict set dict_name_en = 'Marshall Islands' where dict_name_zh='马绍尔群岛';
update tb_arc_dict set dict_name_en = 'Macedonia' where dict_name_zh='前南马其顿';
update tb_arc_dict set dict_name_en = 'Mali' where dict_name_zh='马里';
update tb_arc_dict set dict_name_en = 'Myanmar' where dict_name_zh='缅甸';
update tb_arc_dict set dict_name_en = 'Mongolia' where dict_name_zh='蒙古';
update tb_arc_dict set dict_name_en = 'Macao' where dict_name_zh='中国澳门';
update tb_arc_dict set dict_name_en = 'Northern Mariana Islands' where dict_name_zh='北马里亚纳';
update tb_arc_dict set dict_name_en = 'Martinique' where dict_name_zh='马提尼克';
update tb_arc_dict set dict_name_en = 'Mauritania' where dict_name_zh='毛利塔尼亚';
update tb_arc_dict set dict_name_en = 'Montserrat' where dict_name_zh='蒙特塞拉特';
update tb_arc_dict set dict_name_en = 'Malta' where dict_name_zh='马耳他';
update tb_arc_dict set dict_name_en = 'Mauritius' where dict_name_zh='毛里求斯';
update tb_arc_dict set dict_name_en = 'Maldives' where dict_name_zh='马尔代夫';
update tb_arc_dict set dict_name_en = 'Malawi' where dict_name_zh='马拉维';
update tb_arc_dict set dict_name_en = 'Mexico' where dict_name_zh='墨西哥';
update tb_arc_dict set dict_name_en = 'Malaysia' where dict_name_zh='马来西亚';
update tb_arc_dict set dict_name_en = 'Mozambique' where dict_name_zh='莫桑比克';
update tb_arc_dict set dict_name_en = 'Namibia' where dict_name_zh='纳米比亚';
update tb_arc_dict set dict_name_en = 'New Caledonia' where dict_name_zh='新喀里多尼亚';
update tb_arc_dict set dict_name_en = 'Niger' where dict_name_zh='尼日尔';
update tb_arc_dict set dict_name_en = 'Norfolk Island' where dict_name_zh='诺福克岛';
update tb_arc_dict set dict_name_en = 'Nigeria' where dict_name_zh='尼日利亚';
update tb_arc_dict set dict_name_en = 'Nicaragua' where dict_name_zh='尼加拉瓜';
update tb_arc_dict set dict_name_en = 'Netherlands' where dict_name_zh='荷兰';
update tb_arc_dict set dict_name_en = 'Norway' where dict_name_zh='挪威';
update tb_arc_dict set dict_name_en = 'Nepal' where dict_name_zh='尼泊尔';
update tb_arc_dict set dict_name_en = 'Nauru' where dict_name_zh='瑙鲁';
update tb_arc_dict set dict_name_en = 'Niue' where dict_name_zh='纽埃';
update tb_arc_dict set dict_name_en = 'New Zealand' where dict_name_zh='新西兰';
update tb_arc_dict set dict_name_en = 'Oman' where dict_name_zh='阿曼';
update tb_arc_dict set dict_name_en = 'Panama' where dict_name_zh='巴拿马';
update tb_arc_dict set dict_name_en = 'Peru' where dict_name_zh='秘鲁';
update tb_arc_dict set dict_name_en = 'French Polynesia' where dict_name_zh='法属波利尼西亚';
update tb_arc_dict set dict_name_en = 'Papua New Guinea' where dict_name_zh='巴布亚新几内亚';
update tb_arc_dict set dict_name_en = 'Philippines' where dict_name_zh='菲律宾';
update tb_arc_dict set dict_name_en = 'Pakistan' where dict_name_zh='巴基斯坦';
update tb_arc_dict set dict_name_en = 'Poland' where dict_name_zh='波兰';
update tb_arc_dict set dict_name_en = 'Saint Pierre and Miquelon' where dict_name_zh='圣皮埃尔和密克隆';
update tb_arc_dict set dict_name_en = 'Pitcairn Islands' where dict_name_zh='皮特凯恩';