delete from tb_arc_service_sql where code in ('PHONE_REAL_TIME_TRAJECTORY');

INSERT INTO `tb_arc_service_sql`(`code`, `my_sql`, `ck_sql`) VALUES ('PHONE_REAL_TIME_TRAJECTORY', NULL, 'SELECT \r\nstation_no baseStationNo\r\n,network_type netWorkType\r\n,station_longitude lng\r\n,station_latitude lat\r\n,address stationAddess\r\n,in_time earliestRelationTime\r\n,out_time latestRelationTime\r\n,truncate((case when out_time > in_time then out_time - in_time else 0 end)/(3600000), 3) onlineHour\r\nfrom (\r\nselect lead_tmp.phone_number\r\n,device_id\r\n,lead_tmp.session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,lead_tmp.capture_time\r\n,last_station\r\n,rn\r\n,next_time\r\n,lead_tmp.capture_time as in_time\r\n,case \r\n	  when lead_tmp.session_id = t2.session_id and action <> \'stop\' and lead_tmp.capture_time = t2.capture_time then 999999999\r\n	  when rn = 1 and station_no = last_station then 0\r\n      when rn = 1 and station_no <> last_station then lead_tmp.capture_time\r\n      when next_time = 0 then lead_tmp.capture_time\r\n else next_time end as out_time\r\nfrom (\r\nselect phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,last_station\r\n,rn\r\n,lead(capture_time, 1, 0) over(partition by phone_number, session_id order by capture_time asc) as next_time\r\nfrom (\r\nSELECT phone_number\r\n,device_id\r\n,session_id\r\n,action\r\n,station_no\r\n,network_type\r\n,station_longitude\r\n,station_latitude\r\n,address\r\n,capture_time\r\n,lag(station_no, 1, \'\') over(partition by phone_number, session_id order by capture_time asc) as last_station\r\n,row_number() over(partition by phone_number, session_id order by capture_time desc) as rn\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\n) as t1\r\nwhere rn = 1 or last_station <> station_no) lead_tmp\r\nleft join (\r\nSELECT \r\nphone_number,\r\nsession_id,\r\ncapture_time\r\nfrom ${ads}.ads_archive_phone_location\r\nwhere phone_number = \'${arcAccount}\'\r\nand capture_day >= {start_day}\r\nand capture_day <= {end_day}\r\nand capture_time >= {start_time}\r\nand capture_time <= {end_time}\r\norder by capture_time desc limit 1\r\n) t2\r\non t2.phone_number = lead_tmp.phone_number\r\n) lead_tmp2\r\nwhere out_time > 0\r\n${station_condition}\r\n${keyword_condition}');
