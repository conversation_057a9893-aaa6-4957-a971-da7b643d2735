package com.semptian.archives.web;

import com.semptian.archives.web.service.model.ArcCollectionModel;
import com.semptian.archives.web.service.model.ArcQueryModel;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.service.ArchivesInfoService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class ArcCollectionTest {


    @Resource
    private ArchivesInfoService arcInfoService;

    @Test
    public void updateArcCollectionTest() {

        ArcCollectionModel arcCollectionModel = new ArcCollectionModel();
        arcCollectionModel.setArcId("070e696eefef13eb6f9d9c6c507ce18f");
        arcCollectionModel.setArcType(5);
        arcCollectionModel.setIsCare(1);

        assert arcInfoService.updateArcCollection(arcCollectionModel, "7923387");

    }

    @Test
    public void clearArcCollectionTest() {

        assert arcInfoService.clearArcCollection("7923387");

    }

    @Test
    public void queryArcInfoTest() {

        ArcQueryModel arcQueryModel = new ArcQueryModel();
        arcQueryModel.setSize(10);
        arcQueryModel.setOnPage(1);
        arcQueryModel.setSortField("activeRate");
        arcQueryModel.setSortType(1);
        arcQueryModel.setKeyWord("213022527050");
        arcQueryModel.setArcType(5);
        arcQueryModel.setUserId("7923387");
        arcQueryModel.setIsCare(1);

        DateModel dateModel = new DateModel();
        dateModel.setStartDay("2024-08-30");
        dateModel.setEndDay("2024-09-05");
        dateModel.setDateOption(1);

        List<Integer> permissionList = Lists.newArrayList();
        permissionList.add(5);
        Object result = arcInfoService.queryArcInfo(arcQueryModel, dateModel, permissionList);
        assertNotNull(result, "result should not be null");

    }

}