package com.semptian.archives.web;

import cn.hutool.core.util.StrUtil;
import com.semptian.archives.web.service.common.util.DeyeGetCountryCodeByPhoneNumber;
import com.semptian.base.template.TianHeDorisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class PhoneArcServiceImplTest {

    @Resource
    private TianHeDorisTemplate tianHeDorisTemplate;

    @Test
    public void test() {
        String sql = "SELECT phone_number  from dev_ads.ads_archive_phone_statistics ";

        List<Map<String, Object>> list = tianHeDorisTemplate.queryForList(sql);

        List<String> errorList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            try {
                String phoneNumber = map.getOrDefault("phone_number", "").toString();
                System.out.println("*************phoneNumber:" + phoneNumber);

                String countryCode = DeyeGetCountryCodeByPhoneNumber.getCountryNameByPhoneNumber(phoneNumber);
                System.out.println("*************countryCode:" + countryCode);
                if (StrUtil.isEmpty(countryCode)) {
                    errorList.add(phoneNumber);
                }
            }catch (Exception e) {
                log.error("error", e);
            }
        }

        System.out.println("*************errorList:" + errorList);
    }
}
