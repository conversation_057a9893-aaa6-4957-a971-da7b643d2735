package com.semptian.archives.web;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlSchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.alibaba.druid.util.JdbcConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/6 14:52
 **/
public class SqlParserUtil {

    public static void main(String[] args) {
        String sql = "SELECT * FROM dwd.dwd_lis_im WHERE capture_day between '2024-07-15' and '2024-07-21' AND auth_account = 'radius_1721370559' AND auth_type = 1020001 AND norm_data_type in (101,103) order by capture_time desc  limit 2000 offset 0";

        ArrayList<TableStat.Condition> conditionsList = getConditions(sql);

        System.out.println(conditionsList);
    }

    public static ArrayList<TableStat.Condition> getConditions(String sql) {
        String sqlType = JdbcConstants.MYSQL;
        List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sql, sqlType);
        ArrayList<TableStat.Condition> conditionsList = new ArrayList<>();

        for (SQLStatement sqlStatement : sqlStatements) {
            MySqlSchemaStatVisitor mySqlSchemaStatVisitor = new MySqlSchemaStatVisitor();
            sqlStatement.accept(mySqlSchemaStatVisitor);
            List<TableStat.Condition> conditions = mySqlSchemaStatVisitor.getConditions();
            conditionsList.addAll(conditions);
        }

        return conditionsList;
    }

    public static Boolean assertCondition(ArrayList<TableStat.Condition> conditions, String column, String operator) {
        return assertCondition(conditions, column, operator, null);
    }

    public static Boolean assertCondition(ArrayList<TableStat.Condition> conditions, String column, String operator, String value) {
        for (TableStat.Condition condition : conditions) {
            if (condition.getColumn().getName().equals(column) && condition.getOperator().equals(operator)) {
                return true;
            }
        }
        return false;
    }
}
