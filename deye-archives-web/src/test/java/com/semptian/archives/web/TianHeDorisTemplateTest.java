package com.semptian.archives.web;

import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.base.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class TianHeDorisTemplateTest {


    @Autowired
    private TianHeDorisTemplate tianHeDorisTemplate;

    @Test
    public void testQueryForCount() {
        Long l = tianHeDorisTemplate.queryForCount("SELECT bitmap_count(arc_visitor_area) cnt from ads_archive_website_statistics where `domain` ='www.baidu.com'");
        System.out.println(l);
    }

    @Test
    public void testQueryForList() {
        List<Map<String, Object>> mapList = tianHeDorisTemplate.queryForList("SELECT `domain` ,behavior_num ,bitmap_count(arc_visitor_area) cnt from ads_archive_website_statistics where `domain` ='www.baidu.com'");
        System.out.println(JsonUtil.toJsonString(mapList));
    }

}
