package com.semptian.archives.web;

import com.semptian.base.service.elasticsearch.SearchOperation;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * ES查询服务实现类测试类
 * <AUTHOR>
 * @date 2024/1/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class EsQueryServiceImplTest {

    @Resource
    private SearchOperation searchOperation;

    @Test
    public void testGetRecordsByIds() {
        List<String> ids = Lists.newArrayList();
        ids.add("5357770bddd255d51894902f56ce0bec");
        ids.add("7487029f8667a0bb55be1d19bfd6dc1a");
        ids.add("caba1c49f4b2ef2c1e0a0885fedf02b7");
        List<Map<String, Object>> recordsByIds = searchOperation.getRecordsByIds("id", ids, "deye_v64_quanxidangan_app", "deye_v64_quanxidangan_fix_ip", "deye_v64_quanxidangan_website");

        System.out.println(recordsByIds);
    }

}