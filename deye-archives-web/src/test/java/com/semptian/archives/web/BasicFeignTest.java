package com.semptian.archives.web;


import com.google.common.collect.Lists;
import com.semptian.archives.web.service.fegin.BasicFeign;
import com.semptian.archives.web.service.model.ImportantTargetModel;
import com.semptian.base.service.ReturnModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * BasicFeignTest
 * <AUTHOR> Hu
 * @date 2024/4/28
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class BasicFeignTest {


    @Resource
    BasicFeign basicFeign;

    @Test
    public void getImportantTargetCategoryByBatchTest() {
        List<ImportantTargetModel> importantTargetModelList = Lists.newArrayList();

        ImportantTargetModel importantTargetModel = new ImportantTargetModel();
        importantTargetModel.setImportantTarget("209.156.137.20");
        importantTargetModel.setImportantTargetType("3");
        importantTargetModelList.add(importantTargetModel);

        ImportantTargetModel importantTargetModel2 = new ImportantTargetModel();
        importantTargetModel2.setImportantTarget("KOpQhesf");
        importantTargetModel2.setImportantTargetType("1");
        importantTargetModelList.add(importantTargetModel2);

        ImportantTargetModel importantTargetModel3 = new ImportantTargetModel();
        importantTargetModel3.setImportantTarget("13261659661");
        importantTargetModel3.setImportantTargetType("2");
        importantTargetModelList.add(importantTargetModel3);

        ReturnModel<List<ImportantTargetModel>> returnModel = basicFeign.getImportantTargetCategoryByBatch(importantTargetModelList);

        System.out.println(returnModel.getData());
    }
}
