package com.semptian.archives.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.meiya.whalex.util.JsonUtil;
import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.archives.web.dao.archive.entity.ArcRemarkEntity;
import com.semptian.archives.web.dao.archive.entity.UserRelationDataEntity;
import com.semptian.archives.web.service.common.enums.LanguageEnum;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.archives.web.service.service.UserRelationDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class UserRelationDataServiceTest {

    @Resource
    private UserRelationDataService userRelationDataService;

    @Resource
    private ArcCommonService arcCommonService;

    @Test
    @Rollback(value = false)
    public void testUpdateRelationDataArcType() {
        QueryWrapper<UserRelationDataEntity> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("is_del", 0);
        List<UserRelationDataEntity> list = userRelationDataService.list(queryWrapper);

        Set<String> ids = list.stream().map(UserRelationDataEntity::getArcId).collect(Collectors.toSet());

        //将结果转换为Map<String, Integer>,key为ID、value为archiveType
        List<ArcEsEntity> arcInfoByIds = arcCommonService.getArcInfoByIds(Lists.newArrayList(ids));
        Map<String, Integer> map = arcInfoByIds.stream().collect(Collectors.toMap(ArcEsEntity::getId, ArcEsEntity::getArchiveType));

        for (UserRelationDataEntity userRelationDataEntity : list) {
            Integer archiveType = map.get(userRelationDataEntity.getArcId());
            if (archiveType != null) {
                userRelationDataEntity.setArcType(archiveType);
                userRelationDataService.updateById(userRelationDataEntity);
            }
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testParseRelationData() {
        UserRelationDataEntity userRelationDataEntity = userRelationDataService.getBaseMapper().selectById(29);

        String relationData = userRelationDataEntity.getRelationData();

        Map<String, Object> stringObjectMap = JsonUtil.jsonStrToMap(relationData);

        List<Map<String, Object>> nodes = (List<Map<String, Object>>) stringObjectMap.get("nodes");

        for (Map<String, Object> node : nodes) {
            Object name = node.getOrDefault("name", "");
            Object content = node.getOrDefault("content", "");
            Object fullLabel = node.getOrDefault("fullLabel", "");
            Object label = node.getOrDefault("label", "");
            Object title = node.getOrDefault("title", "");

            log.info("name:{},content:{},label:{},title:{},fullLabel:{}", name, content, label, title, fullLabel);
        }

        List<Map<String, Object>> edges = (List<Map<String, Object>>) stringObjectMap.get("edges");
        for (Map<String, Object> edge : edges) {
            Object peopleRelationType = edge.getOrDefault("peopleRelationType", "");
            log.info("peopleRelationType:{}", peopleRelationType);
        }
    }

    @Test
    @Rollback(value = false)
    public void testRemarkSave() {
        ArcRemarkEntity arcRemarkEntity = new ArcRemarkEntity();

        arcRemarkEntity.setRemark("自动化测试666666222333");
        arcRemarkEntity.setArcId("cd7e73e68f562bfe687eb7104996bdd3");
        arcRemarkEntity.setExpansionArcId("cd7e73e68f562bfe687eb7104996bdd3");
        userRelationDataService.remarkSave(arcRemarkEntity, "7923442");
    }

    @Test
    @Rollback(value = false)
    public void testConnectCreate() {
        userRelationDataService.connectCreate("cd7e73e68f562bfe687eb7104996bdd3", "0ece8508a4e9219e50789189eb0fdf47", "2", "cd7e73e68f562bfe687eb7104996bdd3", LanguageEnum.FR_DZ.getLang());
    }
}
