package com.semptian.archives.web;

import com.alibaba.druid.stat.TableStat;
import com.alibaba.fastjson.JSONObject;
import com.semptian.archives.web.service.model.DrillDownModel;
import com.semptian.archives.web.service.service.impl.ArcCommonServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class DrillDownTest {

    @Resource
    private ArcCommonServiceImpl arcCommonService;

    @Test
    public void getEmailDrillDown() {
        // email 关系扩线下钻
        // {"filePathList":[],"arcAccountType":"","arcType":"2","times":"0-24","onPage":1,"arcAccount":"<EMAIL>","endDay":"2024-07-21","drillDownSceneEnum":"RADIUS_EXPANSION_DETAIL","sortField":"","lang":"zh_CN","virtualAccount":"<EMAIL>","startDay":"2024-07-15","dataType":101,"dateOption":0,"dstAccount":"<EMAIL>","isAll":0,"keyWord":"","size":100,"srcAccount":"<EMAIL>"}
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"\",\"arcType\":\"2\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"<EMAIL>\",\"endDay\":\"2024-07-21\",\"drillDownSceneEnum\":\"RADIUS_EXPANSION_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"virtualAccount\":\"<EMAIL>\",\"startDay\":\"2024-07-15\",\"dataType\":101,\"dateOption\":0,\"dstAccount\":\"<EMAIL>\",\"isAll\":0,\"keyWord\":\"\",\"size\":100,\"srcAccount\":\"<EMAIL>\"}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "spam_flag", "=");
        assert SqlParserUtil.assertCondition(conditions, "mail_from", "=");
        assert SqlParserUtil.assertCondition(conditions, "mail_to", "LIKE");
        assert SqlParserUtil.assertCondition(conditions, "mail_cc", "LIKE");
        assert SqlParserUtil.assertCondition(conditions, "mail_bcc", "LIKE");
    }

    @Test
    public void getImDrillDown() {
        // im 关系扩线下钻
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1030001\",\"arcType\":\"6\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"im_test001@semptian1721187801_.com\",\"endDay\":\"2024-07-21\",\"drillDownSceneEnum\":\"RADIUS_EXPANSION_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"virtualAccount\":\"im_test001@semptian1721187801_.com\",\"startDay\":\"2024-07-15\",\"dataType\":103,\"dateOption\":0,\"dstAccount\":\"imtest007@semptian1721187801_.com\",\"virtualAccountAppType\":\"1030001\",\"isAll\":0,\"keyWord\":\"\",\"size\":100,\"srcAccount\":\"im_test001@semptian1721187801_.com\"}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "from_id", "=");
        assert SqlParserUtil.assertCondition(conditions, "to_id", "=");
        assert SqlParserUtil.assertCondition(conditions, "tool_type", "=");
    }

    @Test
    public void getPhoneRelationDrillDown() {
        // 号码 关系扩线
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020004\",\"arcType\":\"5\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"************\",\"endDay\":\"2024-07-21\",\"drillDownSceneEnum\":\"PHONE_EXPANSION_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"startDay\":\"2024-07-15\",\"dataType\":109,\"dateOption\":0,\"dstAccount\":\"************\",\"isAll\":0,\"keyWord\":\"\",\"size\":100,\"srcAccount\":\"************\"}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "calling_number", "=");
        assert SqlParserUtil.assertCondition(conditions, "called_number", "=");
        assert SqlParserUtil.assertCondition(conditions, "spam_flag", "=");
    }

    @Test
    public void getVirtualAccountDrillDown() {
        // Radius 档案虚拟账号下钻, 带有 app_type 的场景
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020001\",\"arcType\":\"1\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"yhfmobileradius2\",\"endDay\":\"2024-07-22\",\"drillDownSceneEnum\":\"VIRTUAL_ACCOUNT_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"virtualAccount\":\"ikolabftp\",\"startDay\":\"2024-07-16\",\"dataType\":103,\"dateOption\":0,\"virtualAccountAppType\":\"1030001\",\"isAll\":0,\"keyWord\":\"\",\"size\":100}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "main_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "tool_type", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getVirtualExportDrillDown() {
        // 虚拟账号-导出
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020001\",\"arcType\":\"1\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"yhfmobileradius2\",\"endDay\":\"2024-07-22\",\"scope\":0,\"drillDownSceneEnum\":\"VIRTUAL_ACCOUNT_DETAIL\",\"lang\":\"zh_CN\",\"virtualAccount\":\"ikolabftp\",\"startDay\":\"2024-07-16\",\"dataType\":103,\"dateOption\":0,\"virtualAccountAppType\":\"1030001\",\"isAll\":0,\"keyWord\":\"\",\"size\":2000}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "main_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "tool_type", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getVirtualAccountDrillDown1() {
        // Radius 档案虚拟账号下钻, 不带 app_type 的场景
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020001\",\"arcType\":\"1\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"yhfmobileradius2\",\"endDay\":\"2024-07-22\",\"drillDownSceneEnum\":\"VIRTUAL_ACCOUNT_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"virtualAccount\":\"ikolabftp\",\"startDay\":\"2024-07-22\",\"dataType\":108,\"dateOption\":0,\"virtualAccountAppType\":\"\",\"isAll\":0,\"keyWord\":\"\",\"size\":100}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "norm_data_type", "IN");
        assert SqlParserUtil.assertCondition(conditions, "main_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getPhoneVirtualAccountDrillDown1() {
        // 号码档案 虚拟账号
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020004\",\"arcType\":\"5\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"***************\",\"endDay\":\"2024-07-19\",\"drillDownSceneEnum\":\"VIRTUAL_ACCOUNT_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"virtualAccount\":\"<EMAIL>\",\"startDay\":\"2024-07-19\",\"dataType\":2109,\"dateOption\":0,\"virtualAccountAppType\":\"\",\"isAll\":0,\"keyWord\":\"\",\"size\":100}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "main_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getPhoneVirtualAccountExportDrillDown1() {
        // 号码档案 虚拟账号
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020004\",\"arcType\":\"5\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"***************\",\"endDay\":\"2024-07-19\",\"scope\":0,\"drillDownSceneEnum\":\"VIRTUAL_ACCOUNT_DETAIL\",\"lang\":\"zh_CN\",\"virtualAccount\":\"<EMAIL>\",\"startDay\":\"2024-07-19\",\"dataType\":2109,\"dateOption\":0,\"virtualAccountAppType\":\"\",\"isAll\":0,\"keyWord\":\"\",\"size\":2000}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "main_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getLisDrillDown1() {
        // lis 明细
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020001\",\"arcType\":\"1\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"radius_1721370559\",\"endDay\":\"2024-07-21\",\"drillDownSceneEnum\":\"LIS_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"startDay\":\"2024-07-15\",\"dataType\":2001,\"dateOption\":0,\"isAll\":0,\"keyWord\":\"\",\"size\":100}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "norm_data_type", "IN");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getLisExportDrillDown1() {
        //  lis 明细 导出
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020001\",\"arcType\":\"1\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"radius_1721370559\",\"endDay\":\"2024-07-21\",\"drillDownSceneEnum\":\"LIS_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"startDay\":\"2024-07-15\",\"dataType\":2001,\"dateOption\":0,\"isAll\":0,\"keyWord\":\"\",\"size\":100}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "norm_data_type", "IN");
        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    @Test
    public void getNFDrillDown() {
        // 号码档案 虚拟账号
        String sentence = "{\"filePathList\":[],\"arcAccountType\":\"1020004\",\"arcType\":\"5\",\"times\":\"0-24\",\"onPage\":1,\"arcAccount\":\"***************\",\"netAction\":0,\"endDay\":\"2024-07-22\",\"drillDownSceneEnum\":\"NF_DETAIL\",\"sortField\":\"\",\"lang\":\"zh_CN\",\"startDay\":\"2024-07-16\",\"dataType\":1201,\"dateOption\":0,\"isAll\":0,\"keyWord\":\"\",\"size\":100}";
        ArrayList<TableStat.Condition> conditions = getConditionBySceneJson(sentence);

        assert SqlParserUtil.assertCondition(conditions, "auth_account", "=");
        assert SqlParserUtil.assertCondition(conditions, "auth_type", "=");
    }

    private ArrayList<TableStat.Condition> getConditionBySceneJson(String sentence) {
        // 通过 fastjson 将json 解析成 DrillDownModel 对象
        DrillDownModel drillDownModel = JSONObject.parseObject(sentence, DrillDownModel.class);
//        String sql = arcCommonService.getDataDetailSql(drillDownModel);
//        log.info("get sql is \r\n[{}]", sql);
        ArrayList<TableStat.Condition> conditions = SqlParserUtil.getConditions("");
        return conditions;
    }
}