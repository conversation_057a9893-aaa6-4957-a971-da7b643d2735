package com.semptian.archives.web;

import com.semptian.archives.web.service.model.ArcUpdateEsModel;
import com.semptian.archives.web.service.service.EsDataOperateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class EsDataOperateServiceImplTest {


    @Resource
    private EsDataOperateService esDataOperateService;

    @Test
    public void updateDataById() {
        String index = "deye_v64_quanxidangan_app";
        String field = "id";
        String id = "caba1c49f4b2ef2c1e0a0885fedf1110";

        ArcUpdateEsModel arcUpdateEsModel = new ArcUpdateEsModel();

        arcUpdateEsModel.setArchive_alias("archive_alias11");

        boolean updated = esDataOperateService.updateDataById(index, field, id, arcUpdateEsModel);
        System.out.println(updated);
    }
}