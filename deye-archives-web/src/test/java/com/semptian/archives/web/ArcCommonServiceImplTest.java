package com.semptian.archives.web;

import com.google.common.collect.Lists;
import com.semptian.archives.web.dao.archive.entity.ArcEsEntity;
import com.semptian.archives.web.service.common.enums.ArcTypeEnum;
import com.semptian.archives.web.service.common.enums.LanguageEnum;
import com.semptian.archives.web.service.model.DateModel;
import com.semptian.archives.web.service.model.PageWarpEntity;
import com.semptian.archives.web.service.service.ArcCommonService;
import com.semptian.base.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class ArcCommonServiceImplTest {

    @Resource
    private ArcCommonService arcCommonService;

    @Test
    public void getArcInfoByIds() {
        List<ArcEsEntity> arcInfoByIds = arcCommonService.getArcInfoByIds(Lists.newArrayList("6fc0bd7cd2855e2b925dfdd5699d1f19"), ArcTypeEnum.FIXED_IP);
        System.out.println(JsonUtil.toJsonString(arcInfoByIds));
    }

    @Test
    public void getArcInfoByIds2() {
        List<String> ids = Lists.newArrayList();
        ids.add("5357770bddd255d51894902f56ce0bec");
        ids.add("7487029f8667a0bb55be1d19bfd6dc1a");
        ids.add("caba1c49f4b2ef2c1e0a0885fedf02b7");
        List<ArcEsEntity> arcInfoByIds = arcCommonService.getArcInfoByIds(ids);
        System.out.println(JsonUtil.toJsonString(arcInfoByIds));
    }

    @Test
    public void getArcTopTags() {
        Object result = arcCommonService.getTagTopList(1, "arc1", LanguageEnum.AR_DZ.getLang());
        System.out.println(JsonUtil.toJsonString(result));
    }

    @Test
    public void getArcTagList() {
        DateModel dateModel = new DateModel();
        dateModel.setDateOption(2);
        PageWarpEntity pageWarpEntity = new PageWarpEntity();
        Object result = arcCommonService.getTagList(1, "arc1", "", dateModel, pageWarpEntity, LanguageEnum.AR_DZ.getLang());
        System.out.println(JsonUtil.toJsonString(result));
    }
}