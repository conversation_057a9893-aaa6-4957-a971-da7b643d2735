package com.semptian.archives.web;

import com.semptian.archives.web.service.common.util.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchivesWebServerApp.class)
public class DateUtilsTest {

    @Test
    public void testGetLatest7Day() {
        System.out.println(DateUtils.getLatest7Day());
    }

    @Test
    public void testIsFirstDayOfMonthValid() {
        assertTrue(DateUtils.isFirstDayOfMonth("2023-03-01"), "March 1st should be the first day of the month");
        assertTrue(DateUtils.isFirstDayOfMonth("2023-01-01"), "January 1st should be the first day of the month");
        assertTrue(DateUtils.isFirstDayOfMonth("2023-12-01"), "December 1st should be the first day of the month");
    }

    @Test
    public void testIsFirstDayOfMonthInvalid() {
        assertFalse(DateUtils.isFirstDayOfMonth("2023-03-02"), "March 2nd should not be the first day of the month");
        assertFalse(DateUtils.isFirstDayOfMonth("2023-01-02"), "January 2nd should not be the first day of the month");
        assertFalse(DateUtils.isFirstDayOfMonth("2023-12-31"), "December 31st should not be the first day of the month");
    }

    @Test
    public void testIsFirstDayOfMonthIncorrectFormat() {
        assertFalse(DateUtils.isFirstDayOfMonth("01-03-2023"), "Incorrect format should return false");
        assertFalse(DateUtils.isFirstDayOfMonth("2023/03/01"), "Incorrect format should return false");
        assertFalse(DateUtils.isFirstDayOfMonth("2023-3-1"), "Incorrect format should return false");
    }
}