<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.semptian</groupId>
    <artifactId>deye-archives-service</artifactId>
    <packaging>pom</packaging>
    <version>6.4-B08</version>
    <modules>

        <module>archives-core</module>
        <module>archives-dao</module>
        <module>archives-service</module>
        <module>deye-archives-web</module>
    </modules>


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <swagger.version>2.6.1</swagger.version>
        <base-core.version>4.0.0-SNAPSHOT</base-core.version>
        <base-dao.version>4.0.0-SNAPSHOT</base-dao.version>
        <base-log.version>4.0.0-SNAPSHOT</base-log.version>
        <base-web.version>4.0.0-SNAPSHOT</base-web.version>
        <base-redis.version>4.0.0-SNAPSHOT</base-redis.version>
        <base-tianhe.version>4.1.8-SNAPSHOT</base-tianhe.version>
        <mysql-connector-java.version>5.1.45</mysql-connector-java.version>
        <operate-log-report.version>3.0.15-SNAPSHOT</operate-log-report.version>
        <mybatisplus.boot-starter.version>3.4.0</mybatisplus.boot-starter.version>
        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <spring-cloud.version>2.2.6.RELEASE</spring-cloud.version>
        <spring-cloud-starter-openfeign.version>2.2.6.RELEASE</spring-cloud-starter-openfeign.version>

    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>${java.version}</target>
                    <source>${java.version}</source>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://192.168.80.70:8081/nexus/content/repositories/releases/</url>
            <uniqueVersion>true</uniqueVersion>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Snapshots</name>
            <url>http://192.168.80.70:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>